from scraper.OCSpider import OCSpider

class MalaysiaCompetitionCommissionNewsClippings(OCSpider):
    name = "MalaysiaCompetitionCommissionNewsClippings"

    start_urls_names = {
        'https://www.mycc.gov.my/news-clipping' : 'News Clipping',
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//div[@class="inner-layer"]')
        for row in rows:
            title = row.xpath('.//div[@class="newsClip-title"]//text()').get()
            url = row.xpath('.//div[@class="download-btn"]//a/@href | .//div[@class="newsClip-image"]//a/@href').get()
            date = row.xpath('.//div[@class="newsClip-date"]//time/text()').get()
            if title and url and date:
                title = title.strip()
                url = response.urljoin(url.strip())
                self.article_title_pdf_mapping[url] = (title, url, date)
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
       return "%d %b %Y"

    def get_date(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[2]

    def get_images(self, response) -> list[str]:
        url = self.article_title_pdf_mapping.get(response.url, ("", "", ""))[1]
        if url and url.lower().endswith(('.jpg', '.jpeg')):
            return [url]
        return []

    def get_document_urls(self, response, entry=None) -> list[str]:
        url = self.article_title_pdf_mapping.get(response.url, ("", "", ""))[1]
        if url and url.lower().endswith('.pdf'):
            return [url]
        return []

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        next_page_relative = response.xpath('//li[contains(@class, "pager__item--next")]/a/@href').get()
        return response.urljoin(next_page_relative)
    
