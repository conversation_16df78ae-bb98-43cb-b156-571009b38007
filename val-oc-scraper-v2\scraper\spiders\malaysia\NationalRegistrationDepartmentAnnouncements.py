from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class NationalRegistrationDepartmentAnnouncements(OCSpider):
    name = "NationalRegistrationDepartmentAnnouncements"

    start_urls_names = {
        "https://www.imi.gov.my/index.php/berita-pengumuman/": "News"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.imi.gov.my/index.php/berita-pengumuman/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {} 

    def get_articles(self, response) -> list:
          articles = []
          for article in response.xpath('//*[@id="footable_11670"]/tbody/tr'):
            url = article.xpath(".//td[1]//@href").get()
            title = article.xpath(".//td[1]//text()").get()  # Removed comma to avoid tuple
            date = article.xpath(".//td[2]//text()").get()
            if url and date and title:
                articles.append(url)
                self.article_data_map[url] = {"date": date,"title": title}
          return list(set(articles))

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="elementor-widget-container"]/ul/li//text() | //div[@class="elementor-widget-container"]/p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//*[@id="main"]/div/section[3]//div[2]//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        curr_date1=  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(curr_date1, languages=['en', 'ms']) 
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//*[@id="main"]//iframe/@data-src | //*[@id="content"]//object/@data').getall()
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 