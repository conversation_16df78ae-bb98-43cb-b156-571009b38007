from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import Optional
import re
import scrapy

class TheGovernmentOfficeOfVietnam(OCSpider):
    name = "TheGovernmentOfficeOfVietnam"

    start_urls_names = {
        "https://vpcp.chinhphu.vn/timelinefocus/24/1.htm": "Tin nổi bật"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:  
        return response.xpath("//div[@class='box-stream-item']//h2[@class='title-top']//a/@href | //div[@class='content-news']//h2//a/@href").getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return  response.xpath('//div[@class="container-680"]//h1[@class="detail-title"]/text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath("//div[@class='detail-content afcbc-body']//p/text()").getall())
       
    def get_images(self, response) -> list:
        return response.xpath('//figure[@class="VCSortableInPreviewMode"]//img/@src').getall()

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return response.xpath("substring-before(normalize-space(//div[@class='detail-time']//div[@data-role='publishdate']/text()), ' ')").get()

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        m = re.search(r'/(\d+)\.htm$', response.url)
        page_num = int(m.group(1)) if m else int(current_page or 1)
        next_page = page_num + 1
        return next_page

    def go_to_next_page(self, response, start_url: str, current_page: Optional[int] = 1):
        next_page_num = self.get_next_page(response, current_page)
        if not next_page_num:
            return
        try:
            has_articles = bool(self.get_articles(response))
        except Exception:
            has_articles = True
        if not has_articles:
            return
        base = re.sub(r'/\d+\.htm$', '', start_url.rstrip('/'))
        next_url = f"{base}/{next_page_num}.htm"
        yield scrapy.Request(
            url=next_url,
            callback=self.parse,
            meta={
                "page": next_page_num,
                "start_url": start_url,
            }
        )
