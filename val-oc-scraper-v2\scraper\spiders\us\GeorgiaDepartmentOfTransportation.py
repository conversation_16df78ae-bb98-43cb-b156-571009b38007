from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime

class GeorgiaDepartmentOfTransportation(OCSpider):
    name = "GeorgiaDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://www.dot.ga.gov/GDOT/Pages/TheNetwork.aspx": "All News",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    HEADLESS_BROWSER_WAIT_TIME = 30000 # 30 Seconds wait time

    charset = "utf-8"
    
    article_map = {} # Mapping article with title from start URL
    
    article_date_map = {} # Mapping article with date from start URL

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        articles = self.extract_articles_with_titles(response)
        urls = list(articles.keys()) 
        return urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        cleaned_url = response.url.split('?')[0]  # Remove tracking parameters
        if cleaned_url in self.article_map:
            title = self.article_map[cleaned_url].get("title", "")
            return title
        
    def get_body(self, response) -> str:
        return body_normalization( response.xpath('//td[@class="mcnTextContent"]//text()').getall())
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        cleaned_url = response.url.split('?')[0]  
        raw_date = self.article_date_map.get(cleaned_url)
        raw_date = raw_date.split(",")[:2] 
        raw_date = " ".join(raw_date).strip()  
        formatted_date = datetime.strptime(raw_date, "%B %d %Y").strftime("%m-%d-%Y")
        return formatted_date
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None
    
    def extract_articles_with_titles(self, response):
        self.article_map = {}
        self.article_date_map = {}
        articles = response.xpath('//div[contains(@class, "js-filterable__item")]')
        for index, article in enumerate(articles):
            links = article.xpath('.//a[@class="nb-article-preview"]/@href').getall()
            link = links[0].strip().split(",")[0]  
            link = link.split('?')[0]
            title = article.xpath('.//div[contains(@class, "nb-article-preview__title")]/text()').get()
            title = title.strip() 
            raw_date = article.xpath('.//date[contains(@class, "nb-article-preview__date")]/text()').get()
            raw_date = raw_date.strip() 
            self.article_map[link] = {"title": title, "date": raw_date}
            self.article_date_map[link] = raw_date  
        return self.article_map  