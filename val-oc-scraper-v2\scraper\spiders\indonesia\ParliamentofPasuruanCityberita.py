from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentofPasuruanCityberita(OCSpider):
    name = "ParliamentofPasuruanCityberita"

    start_urls_names = {
        "https://dprd.pasuruankota.go.id/category/berita/": "News"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self):
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Jakarta"  
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='content']//h2//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry  

    def get_title(self, response) -> str:
        return response.xpath('//title/text()').get().strip() 

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='content']//p//text()").getall()) 

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='logo']//img//@src").getall()

    def date_format(self) -> str:
        return "%B %d, %Y"     
    
    def get_date(self, response) -> str:
        return response.xpath("//span[@class='tie-date']/text() |//span[contains(@class, 'date')]/text()").get().strip()

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response): 
        # No next page to scrape
        return None