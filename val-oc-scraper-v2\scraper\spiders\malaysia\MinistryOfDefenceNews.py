from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from typing import Optional
from scraper.utils.helper import body_normalization
import re

class MinistryOfDefenceNews(OCSpider):
    name = "MinistryOfDefenceNews"

    start_urls_names = {
        "https://www.mod.gov.my/index.php/en/media3/news": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:  
        return response.xpath("//table[contains(@class, 'category')]//tbody//tr//td[@class='list-title']/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="article-header"]/h1[@itemprop="headline"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@itemprop="articleBody"]//p/text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        text = response.xpath("(//div[@itemprop='articleBody']//p/text())[1]").get()
        match = re.search(r'\d{1,2} \w+ \d{4}', text)
        return match.group() if match else ''

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath("//li[@class='page-item']/a[@aria-label='Go to next page']/@href").get()
        if next_page:
            return response.urljoin(next_page)
        return None