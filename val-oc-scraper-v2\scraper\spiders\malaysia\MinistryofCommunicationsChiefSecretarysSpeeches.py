from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
from scraper.middlewares import HeadlessBrowserProxy

class MinistryofCommunicationsChiefSecretarysSpeeches(OCSpider):
    name = "MinistryofCommunicationsChiefSecretarysSpeeches"
    
    start_urls_names = {
        "https://www.komunikasi.gov.my/media-menu/ucapan/ketua-setiausaha-kkd": "News",
        "https://www.komunikasi.gov.my/media-menu/ucapan/ketua-setiausaha-kkd/522-ucapan/11668-ucapan-ketua-setiausaha-kkmm-2017": "News",
        "https://www.komunikasi.gov.my/media-menu/ucapan/ketua-setiausaha-kkd/522-ucapan/13575-ucapan-ketua-setiausaha": "News",
        "https://www.komunikasi.gov.my/media-menu/ucapan/ketua-setiausaha-kkd/522-ucapan/25901-ucapan-ketua-setiausaha-kkmm-2019": "News",
        "https://www.komunikasi.gov.my/media-menu/ucapan/ketua-setiausaha-kkd/522-ucapan/25904-ucapan-ketua-setiausaha-kkmm-2020": "News",
        "https://www.komunikasi.gov.my/media-menu/ucapan/ketua-setiausaha-kkd/522-ucapan/25902-ucapan-ketua-setiausaha-kkmm-2021": "News",
        "https://www.komunikasi.gov.my/media-menu/ucapan/ketua-setiausaha-kkd/522-ucapan/25903-ucapan-ketua-setiausaha-kkmm-2022": "News"
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request 

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//tbody//tr"):
            url = article.xpath(".//td[2]//p//span//a//@href | .//a//@href").get()
            title = article.xpath(".//td[2]//p//span//a//text() | .//a//text()").get()
            date = article.xpath(".//td[1]//p//span/strong//text() | //tbody//tr/td/p/span//text()").get()
            if url and title and date:
                self.article_data_map[url] = {
                    "title": title.strip(),
                    "date": date.strip(),
                }
                articles.append(url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None