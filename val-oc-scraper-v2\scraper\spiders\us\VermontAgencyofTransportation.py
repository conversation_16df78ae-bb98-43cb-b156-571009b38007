from scraper.OCSpider import <PERSON>CSpider
from typing import List
import scrapy
import re

class VermontAgencyOfTransportation(OCSpider):
    name = 'VermontAgencyOfTransportation'

    country = "US"
    
    start_urls_names = {
        'https://vtrans.vermont.gov/news': "News",
        'https://vtrans.vermont.gov/about/news/archive': "News",
    }

    def parse_intermediate(self, response):
        all_articles = response.xpath("//p//a[contains(@href,'.pdf')]//@href").getall()
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page): # Indexing for virtual pagination, to scrap more tham 100 articles
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx, 
                    'articles': all_articles, 
                    'start_url': start_url
                },
                dont_filter=True
            )
            
    charset = "iso=8859-1"

    article_data_map ={}  # Mapping of Articles and PDF are done from start URL

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"

    def get_articles(self, response) -> list:
        try:
            articles = response.xpath("//p//a[contains(@href,'.pdf')]")
            all_articles = []
            for article in articles:
                full_url = article.xpath(".//@href").get()
                title = article.xpath(".//text()").get()
                if full_url:
                    if not title:
                        title = response.xpath("//title/text()").get(default="").strip()
                    self.article_data_map[full_url] = {  # Mapping done for indexing will not work
                        "title": title.strip(),
                        "pdf": [full_url],
                    }
                    all_articles.append(full_url)
            start_idx = response.meta.get('start_idx', 0) # Indexing should be called from parse_intermediate only
            end_idx = start_idx + 100
            return all_articles[start_idx:end_idx]  # Only Article url's are extracted and returned
        except:
            return
    

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title"," ").strip()
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str: # Some articles getting failed due to no date found -- 2019& older
        match = re.search(r"/(\d{2})\.(\d{2})\.(\d{4})%20", response.url)
        if match:
            month, day, year = match.groups()
            return f"{year}-{month}-{day}"
        else:
            match = re.search(r"/(\d{2})\.(\d{2})\.(\d{2})%20", response.url)
            if match:
                month, day, year = match.groups()
                return f"20{year}-{month}-{day}"
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None) -> List[str]:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf"," ")

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return None