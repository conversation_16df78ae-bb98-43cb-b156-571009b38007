from scraper.OCSpider import OCSpider
import dateparser

class MinistryOfDefenceMinistersSpeeches(OCSpider):
    name = "MinistryOfDefenceMinistersSpeeches"

    start_urls_names = {
        "https://www.mod.gov.my/index.php/en/media3/teks-ucapan-menteri": "News"
        }

    start_urls_with_no_pagination_set = {
        "https://www.mod.gov.my/index.php/en/media3/teks-ucapan-menteri"
    }
    
    charset = "iso-8859-1"

    country = "Malaysia"

    article_data_map ={}

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
     
    def get_articles(self, response) -> list:  
        articles = []
        for article in response.xpath("//div[@class='uk-scope']//h6//a[@class='uk-link-reset']"):
            url = article.xpath(".//@href").get()
            title = article.xpath(".//text()").get()
            if url and title:
                self.article_data_map[url] = {"title" : title.strip()}
                articles.append(url)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ''

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        if "-" in title:
            date_str = title.split("-", 1)[0].strip()
            parsed = dateparser.parse(date_str, languages=["ms", "en"]) 
            return parsed.strftime("%d %B %Y")
        else:
            date_str=title.split(" ")[-1].strip()
            parsed = dateparser.parse(date_str, languages=["ms", "en"]) 
            return parsed.strftime("%d %B %Y")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None