from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class HigherEducationDepartmentPressReleases(OCSpider):
    name = "HigherEducationDepartmentPressReleases"

    start_urls_names = {
        "https://jpt.mohe.gov.my/portal/index.php/ms/kenyataan-media": "News"
    }

    start_urls_with_no_pagination_set = {}
        
    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {} 

    def get_articles(self, response) -> list:
          articles = []
          for article in response.xpath('//*[@id="adminForm"]/table/tbody/tr'):
            url = article.xpath(".//td[1]//a//@href").get()
            date = article.xpath(".//td[2]//text()").get()
            title = article.xpath(".//td[1]//a//text()").get()
            if url and date and title:
                    articles.append(url)
                    self.article_data_map[url] = {"date": date,"title": title}
          return list(set(articles))
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        text = body_normalization(response.xpath('//p[@style="text-align: justify;"]/text()').getall())
        if text:
            return text
        else:
            return ""

    def get_images(self, response) -> list:
        return response.xpath('//*[@class="uk-margin-medium-top"]/p//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        curr_date1=  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(curr_date1, languages=['en', 'ms']) 
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        pdf =  response.xpath('//div[@class="uk-margin-medium-top"]//a[not(contains(@href, ".jpg")) and not(contains(@href, ".png"))]/@href').getall()
        pdfs = []
        for i in pdf:
            if "http" not in i :
                i= "https://jpt.mohe.gov.my" + i
            else:
                i = i
            pdfs.append(i)
        return pdfs
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = response.xpath('(//a[contains(@class, "next")])[last()]/@href').get()
        if  next_page:
            return next_page
        return None 