from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import unquote
import re

class FederalTerritorySportsCouncilWIPERSActsandLegislations(OCSpider):
    name = "FederalTerritorySportsCouncilWIPERSActsandLegislations"

    start_urls_names = {
        'https://www.wipers.gov.my/akta-pekeliling/': 'Circular '  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.wipers.gov.my/akta-pekeliling/'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    articles_url_date_info={}

    def get_articles(self, response) -> list:
        article_list = []
        articles = response.xpath('//div[@class="elementor-element elementor-element-ad6b00b jltma-glass-effect-no elementor-widget elementor-widget-text-editor"]//li')
        for article in articles:
            href = article.xpath('.//a/@href').get()
            title = article.xpath('.//a/text()').get()
            date = re.search(r"(\d{4})/(\d{1,2})", href)
            if href and date.group(0):
                article_list.append(href)
                self.articles_url_date_info[href]=[title,date.group(0)]
        return article_list

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.articles_url_date_info.get(response.url,)[0]

    def get_body(self, response) -> str:
        return ""       

    def get_date(self, response) -> str: 
        article_date = self.articles_url_date_info.get(response.url, None)[1]
        if article_date:
            return article_date
 
    def date_format(self) -> str:
        return "%Y/%m"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None