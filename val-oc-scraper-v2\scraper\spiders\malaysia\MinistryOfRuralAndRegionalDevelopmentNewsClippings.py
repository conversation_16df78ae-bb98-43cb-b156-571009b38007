from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
import dateparser

class MinistryOfRuralAndRegionalDevelopmentNewsClippings(OCSpider):
    name = "MinistryOfRuralAndRegionalDevelopmentNewsClippings"

    charset = "iso-8859-1"

    start_urls_names = {
        f"https://www.rurallink.gov.my/keratan-akhbar/liputan-media-{day}-april-2025/": "Media"
        for day in range(11, 6, -1)
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    article_data_map = {}  # Mapping date and title to child articles from start URL

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//a[contains(@href, '/PRINT_NEWS/')]"):
            url = article.xpath(".//@href").get()
            title = article.xpath(".//text()").get() or url
            date = article.xpath("//p[contains(text(), 'Published')]/span[1]/text()").get() or "Unknown"
            if url and title:
                self.article_data_map[url] = {
                    "date": date.strip(), 
                    "title": title.strip()
                }
                articles.append(url)
        return list(set(articles))

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        if '.pdf' in response.url:
            return ""
        return ""

    def get_images(self, response) -> list:
        if 'jpg' in response.url.lower() or 'mp4' in response.url.lower():
            return [response.url]
        
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response): 
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(date, languages=['ms']) 
        return parsed_date.strftime("%Y-%m-%d")
    
    def get_document_urls(self, response, entry=None) -> list:
        if '.pdf' in response.url.lower():
            return [response.url]
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        # No next page to crawl
        return None