from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfBoyolaliRegency(OCSpider):
    name = "ParliamentOfBoyolaliRegency"

    start_urls_names = {
        "https://dprdboyolali.com/category/berita-terkini/": "Berita Terkini"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:  
       return response.xpath('//h2[@class="entry-title"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="entry-title"]//text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="container aos-init aos-animate"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//time/text()').get().strip()
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[@class="next page-numbers"]//@href').get()