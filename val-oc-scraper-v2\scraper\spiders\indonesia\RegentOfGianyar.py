from scraper.OCSpider import OCSpider
import scrapy

class RegentOf<PERSON><PERSON><PERSON>(OCSpider):
    name = "RegentOfGianyar"

    proxy_country = "in"

    start_urls_names = {
        "https://jdih.gianyarkab.go.id/": "Articles"
    }

    charset = "iso-8859-1"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
           'scraper.middlewares.GeoProxyMiddleware': 350,
        },
        "DOWNLOAD_DELAY": 1,
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='card-body']//ul//li"):
            url = article.xpath(".//a[contains(@href,'.pdf')]//@href").get()
            title = article.xpath(".//h5//a//text()").get()
            date = article.xpath(".//div[@class='d-flex align-items-center justify-content-between mt-1']//p//text()").get()
            if url and title and date:
                full_url = url
                title = title.strip()
                if '.pdf' in full_url.lower():
                    pdf = full_url
                else:
                    pdf = "None"
                clean_date = date.strip()
                self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                articles.append(full_url) 
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y-%m-%d %H:%M:%S"
    
    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date =date.replace("Tanggal : ","")
        return date.strip()
        
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        if response.url.lower().endswith(".pdf"):
            return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None