from typing import Optional
from datetime import datetime
from scraper.OCSpider import OCSpider

class AccountantGeneralsDepartmentStrategicPlan(OCSpider):
    name = "AccountantGeneralsDepartmentStrategicPlan"

    start_urls_names = {
        "https://www.anm.gov.my/korporat/pelan-strategik-janm": "Strategic Plan"  # Pagination is not supported
    }
    
    start_urls_with_no_pagination_set = {
        "https://www.anm.gov.my/korporat/pelan-strategik-janm"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}
        
    def get_articles(self, response) -> list:
        articles = []
        for link in response.xpath("//div[@class='com-content-article__body']"):
            url = link.xpath(".//a[contains(@href, '.pdf')]//@href").get()
            title = response.xpath("//div[@class='page-header']//h2//text()").get()
            date = response.xpath("//dd[@class='modified']//time//text()[2]").get()
            if url and title and date:
                date = date.strip()
                self.article_data_map[url] = {"date": date, "title": title}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response):
        MALAY_TO_ENGLISH_MONTH = {
        "Januari": "January",
        "Februari": "February",
        "Mac": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }
        date_str = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        for malay, english in MALAY_TO_ENGLISH_MONTH.items():
            if malay in date_str:
                return date_str.replace(malay, english)
        return date_str

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return None