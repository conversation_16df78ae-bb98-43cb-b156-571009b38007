import re
from scraper.utils.helper import body_normalization
from scraper.OfficialLineSpider import OfficialLineSpider


# Use this pattern if you want for using geo targeted proxy
# specific use the middleware GeoProxyMiddleware and set the country code

class Ynetnews(OfficialLineSpider):

    name = "ynetnews"

    source = "北京青年报"
    
    # this var is required for setting which country's proxy to use
    # eg: cn => China ref: https://scrapeops.io/docs/web-scraping-proxy-api-aggregator/advanced-functionality/country-geotargeting/
    proxy_country = "cn"
    
    custom_settings = { 
        "DOWNLOADER_MIDDLEWARES" : 
        {
            # for using geo targeted proxy, add this middleware
            'scraper.middlewares.GeoProxyMiddleware': 350,
        },
        "DOWNLOAD_DELAY" : 2,            
    }

    start_urls_names = {
        "https://epaper.ynet.com/html/%s/%s/node_1331.htm" : "北京青年报",
    }

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_start_url(self, url, date):
        return url % (date.strftime('%Y-%m'), date.strftime('%d'))   

    def get_articles(self, response) -> list:
        return response.xpath('//ul[@class="jcul"]/li')

    def get_href(self, entry) -> str:
        return entry.xpath("./a/@href").get()

    def get_layout(self, response) -> str:
        return response.xpath('//div[@class="bjmain"]//dd/text()').extract_first().strip()

    def get_title(self, response) -> str:
        return " ".join(response.xpath('//div[@class="rit"]/h1/text()').extract()).replace("\r", "").replace("\n", "").strip()

    def get_subhead(self, response) ->str:
        return " ".join(response.xpath("//div[@class='rit']/p[@class='fbiaot']/text()").extract()).replace("\t", "").replace("\r", "").replace("\n", "").strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="contnt"]//text()').extract())

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return self.date.strftime("%Y-%m-%d")

    def get_images(self, response) -> list:
        images = []
        image_list = response.xpath("//div[@class='contnt']/table//img//@src").extract()
        if len(image_list) > 0:
            return ["http://epaper.ynet.com/" + re.sub("\.\./\.\./\.\./", "", image) for image in image_list]
        else:
            return images

    def get_authors(self, response):
        author = re.findall("记者  (.*?)）", self.get_body(response))
        if not author:
            author = re.findall("记者  (.*?) ", self.get_body(response))
        if author:
            author = author[0]
        else:
            author = ""
        return author

    def get_next_page(self, response) -> str:
        page_no = int(re.compile('node_(\d+).htm').search(response.url).group(1))
        page_list = response.xpath('//div[@id="artcile_list_wapper"]/table/tbody/tr/td[1]/a[contains(@href,"node")]').extract()
        if len(page_list) == 1:
            return None
        else:
            page_no += 1
            return self.get_start_url(f"https://epaper.ynet.com/html/%s/%s/node_{page_no}.htm", self.date)