from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization
import scrapy

class VietnamTelevisionOnlineNewspaper(OCSpider):
    name = "VietnamTelevisionOnlineNewspaper"

    start_urls_names = {
        'https://vtv.vn/chinh-tri.htm': 'news'
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="box-category-item"]//a[@data-type="title"]/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="title"]//text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detail-cmain"]//p//text()').getall())
        
    def get_date(self, response) -> str:
        date_data = response.xpath('//p[@class="days"]/text()').get()
        date = re.search(r"\d{2}/\d{2}/\d{4} \d{2}:\d{2}", date_data or '')
        if date:
            return date.group(0)
        else:
            ValueError(f"Date not found for url = {response.url}")
       
    def date_format(self) -> str:
        return "%d/%m/%Y %H:%M"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="detail-cmain"]//img/@src').getall()

    def get_document_urls(self, response, entry=None)->list:
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        page_no = response.meta.get('page_no' , 1)
        return page_no
    
    def go_to_next_page(self, response, start_url, current_page=None):
        page_no = self.get_next_page(response)
        page_no += 1
        if page_no:
            start_url = response.meta.get('start_url')
            yield scrapy.Request(
                url = f"https://vtv.vn/timelinelist/100180/{page_no}.htm" ,
                method= "GET",
                callback = self.parse, 
                meta= {
                    'page_no' : page_no ,
                    'start_url' : start_url
                }
            )
        else:
            yield None