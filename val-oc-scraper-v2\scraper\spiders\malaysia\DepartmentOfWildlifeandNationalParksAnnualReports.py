from scraper.OCSpider import OCSpider
from urllib.parse import quote, unquote

class DepartmentOfWildlifeandNationalParksAnnualReports(OCSpider):
    name = "DepartmentOfWildlifeandNationalParksAnnualReports"

    country = "Malaysia"

    start_urls_names = {
    "https://www.wildlife.gov.my/index.php/penerbitan/108-laporan-tahunan": "Annual Reports"
}
    
    start_urls_with_no_pagination_set = {
        "https://www.wildlife.gov.my/index.php/penerbitan/108-laporan-tahunan"
    }

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self): 
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    article_pdf_date_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//tr//td[2]')  
        for row in rows:
            year = row.xpath('.//a//text()').get()
            pdf_link = row.xpath('.//a//@href').get()
            if pdf_link and year:
                pdf_link = unquote(pdf_link.strip())  
                pdf_link = response.urljoin(quote(pdf_link, safe=':/'))
                self.article_pdf_date_mapping[pdf_link] = { "pdf_link": pdf_link, "date": year, }
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return ""

    def get_body(self, response) -> str:
        return ""

    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        return self.article_pdf_date_mapping.get(response.url, {}).get("date", "")

    def get_images(self, response) -> list:
        return []

    def get_document_urls(self, response, entry=None) -> list: 
        return [self.article_pdf_date_mapping.get(response.url, {}).get("pdf_link", "")]

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return None