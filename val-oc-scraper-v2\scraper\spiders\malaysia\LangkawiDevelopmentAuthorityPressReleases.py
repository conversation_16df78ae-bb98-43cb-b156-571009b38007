from scraper.OCSpider import OCSpider
import scrapy
from urllib.parse import unquote
from scrapy import Selector

class LangkawiDevelopmentAuthorityPressReleases(OCSpider):
    name = "LangkawiDevelopmentAuthorityPressReleases"

    start_urls_names = {
        'https://www.lada.gov.my/kenyataan-media/' : 'Press Clippings'
    }

    start_urls_with_no_pagination_set = {}

    api_start_urls = {
        'https://www.lada.gov.my/kenyataan-media/': 'https://www.lada.gov.my/kenyataan-media/page/{current_page}/',
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_url = self.api_start_urls.get(start_url)
        current_page = response.meta.get("current_page", 1)
        api_url = api_url.format(current_page=current_page)
        yield scrapy.Request(
            api_url,
            callback=self.parse, 
            dont_filter=True, 
            meta ={
            "start_url" : response.url, 
            "current_page": current_page
            }
        )

    malay_months = {
        "Januari": "January",
        "Februari": "February",
        "Mac": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }

    def get_articles(self, response):
        sel = Selector(text=response.text)
        articles = sel.xpath('//article')
        urls = []
        mapping = {}
        for art in articles:
            url = art.xpath('.//h2[contains(@class,"blog-shortcode-post-title")]/a/@href').get()
            title = art.xpath('.//h2[contains(@class,"blog-shortcode-post-title")]/a/text()').get()
            date = art.xpath('.//p[contains(@class,"fusion-single-line-meta")]/span[not(@class)]/text()').get()
            if url and title and date:
                mapping[url] = {
                    "title": title.strip(),
                    "date": date.strip(),
                }
                urls.append(url)     
        self.article_to_date_map.update(mapping)
        return urls

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('title')

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url)
        date_part = self.article_to_date_map.get(normalized_url).get('date')
        for malay_month, english_month in self.malay_months.items():
            if malay_month in date_part:
                date_part = date_part.replace(malay_month, english_month)
                break
        return date_part.strip()

    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        next_page = str(int(response.meta.get("current_page", 1)) + 1)
        if response.status != 200:
            return
        return next_page
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        next_page = self.get_next_page(response)
        if next_page:
            next_page_url = self.api_start_urls.get(start_url).format(current_page=next_page)
            yield scrapy.Request(next_page_url, callback=self.parse, dont_filter=True, meta={"start_url": start_url, "current_page": next_page})
        else:
            self.log("No more pages to scrape.")