from datetime import datetime
from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfEastJava(OCSpider):
    name = "ParliamentOfEastJava"
    
    start_urls_names = {
        "https://dprd.jatimprov.go.id/berita": "News"
    }

    charset = "utf-8"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="news3col__col"]/a[@class="news3col__img"]/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="detailhead"]/h1[@class="detail-judul"]/text()').get()
      
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detailcontent"]/p/text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> Optional[str]:
        ID_MONTH_TO_NUMBER = {
            "Januari": "01",
            "Februari": "02",
            "Maret": "03",
            "April": "04",
            "Mei": "05",
            "Juni": "06",
            "Juli": "07",
            "Agustus": "08",
            "September": "09",
            "Oktober": "10",
            "November": "11",
            "Desember": "12",
        }
        raw_date = response.xpath('//div[@class="repshare__col"]/span[@class="repshare__date"]/text()').get()
        raw_date = raw_date.strip()
        # Removing weekday if present
        if ',' in raw_date:
            raw_date = raw_date.split(',', 1)[-1].strip()
        parts = raw_date.split()
        if len(parts) == 3:
            day, month_id, year = parts
            month_num = ID_MONTH_TO_NUMBER.get(month_id)
            if month_num:
                # Format as MM-DD-YYYY
                return f"{month_num}-{int(day):02d}-{year}"
        return None
       
    def get_authors(self, response):
        return response.xpath('//div[@class="repshare__col"]/span[@class="repshare__nama"]/text()').get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page =  response.xpath('//div[@class="paging"]//li[@class="active"]/following-sibling::li[a]/a[1]/@href').get()
        if next_page:
            return next_page
        else:
            return None