from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
import re

class RegistrationofCompanyPressReleases(OCSpider):
    name = "RegistrationofCompanyPressReleases"

    start_urls_names = {
        'https://www.ssm.com.my/Pages/Publication/Press_Release/Press-Release.aspx': 'press'
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        proxy_articles =[]
        articles = response.xpath('//td[@class="ms-vb2"]/a/@href').getall()
        for article in articles:
            proxy_articles.append(self.construct_article_url(article))
        return proxy_articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title_parts = response.xpath('//td[@id="SPFieldText"]//text()').getall()
        title = ' '.join(part.strip() for part in title_parts if part.strip())
        return title

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="ms-rtestate-field"]//text()').getall())

    def get_date(self, response) -> str:
        date_data = str(response.xpath('//td[@id="SPFieldDateTime"]//text()').getall())
        match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', date_data)
        if match:
            return match.group(1)
        else:
            raise ValueError(f"[ERROR] Date not found in response from {response.url}")

    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response):
        onclick = response.xpath('//td/a[@href="javascript:"]/@onclick').get()
        if onclick:
        # Extract the actual URL from within the onclick JS
            match = re.search(r'RefreshPageTo\(event,\s*"([^"]+)"', onclick)
        if match:
            relative_url = match.group(1)
            return response.urljoin(relative_url)
        
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def construct_article_url(self, article):   
        hbp = HeadlessBrowserProxy()
        if article:
            return hbp.get_proxy(article, timeout=10000)  
        else: 
            return None
