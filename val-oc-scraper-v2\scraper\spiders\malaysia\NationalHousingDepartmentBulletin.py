from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from typing import Optional
from urllib.parse import urljoin
from dateutil import parser

class NationalHousingDepartmentBulletin(OCSpider):
    name = "NationalHousingDepartmentBulletin"
    
    start_urls_names = {
        "https://ehome.kpkt.gov.my/index.php/pages/view/362?mid=349": "BULETIN JABATAN PERUMAHAN NEGARA",
    }

    article_data_map = {}

    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_date_map = {}
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        articles = list(self.article_data_map.keys())
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        url = response.url
        article_info = self.article_data_map.get(url)
        if article_info and 'title' in article_info:
            return article_info['title'].strip()
        return "No title found"

    def get_body(self, response) -> str:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        if "application/pdf" in content_type:
            return "[PDF content not extracted]"
        try:
            return response.text
        except AttributeError:
            return ""
        
    def get_images(self, response) -> list:
        return ""    

    def date_format(self) -> str:
        return"%d-%m-%Y"
    
    def get_date(self, response) -> Optional[str]:
        article_url = response.url
        article_info = self.article_data_map.get(article_url)   
        if not article_info:
            return None   
        raw_date = article_info.get("date")
        if not raw_date:
            return None
        try:
            date_obj = parser.parse(raw_date.strip(), dayfirst=True)
            return date_obj.strftime(self.date_format())
        except (ValueError, TypeError):
            return None

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        article_info = self.article_data_map.get(response.url)
        if article_info and "pdf" in article_info:
            return article_info["pdf"]
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None

    def extract_articles_with_dates(self, response):
        mapping = {}
        base_url = response.url
        sections = response.xpath('//div[contains(@class, "accordion_header")]')
        for section in sections:
            year = section.xpath('./h3/text()').get()
            if year:
                year = year.strip().replace("TAHUN", "").strip()
            links = section.xpath('.//a')
            for link in links:
                title = link.xpath('.//span/text()').get()
                pdf_relative_url = link.xpath('./@href').get()
                if title and pdf_relative_url:
                    pdf_url = urljoin(base_url, pdf_relative_url.strip())
                    mapping[pdf_url] = {
                        "title": title.strip(),
                        "date": year,
                        "pdf": [pdf_url]
                    }
        self.article_data_map.update(mapping)
        return self.article_data_map
