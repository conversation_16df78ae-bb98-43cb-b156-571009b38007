from typing import Optional
from datetime import datetime
from scraper.OCSpider import OCSpider

class SYTECH(OCSpider):
    name = "<PERSON><PERSON><PERSON><PERSON>"

    start_urls_names = {
        "https://www.syst.com.cn/cn/xwsj/list_32.aspx?page=1": "新闻事件",
    }

    charset = "utf-8"

    article_data_map = {}  # Mapping date with child url from start URL

    @property
    def source_type(self) -> str:
        return "private_enterprise"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        relative_urls = response.xpath('//div[@class="newsList"]//section[@class="newsEven"]/a/@href').getall()
        return [response.urljoin(url) for url in relative_urls]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="pgTop"]//hgroup[@class="pgTitle new_title_centerd"]/h3/text()').get()

    def get_body(self, response) -> str:
        return " "

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        url = response.url.strip()
        if url in self.article_data_map:
            return self.article_data_map[url].get("date", "")
        else:
            return ""

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="contactInfo"]//img/@src').getall()

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//div[@class="pagenavi"]/a[@class="a_next"]/@href').get()
        return response.urljoin(next_page) if next_page else None

    def extract_articles_with_dates(self, response):
        articles = response.xpath('//div[@class="newsList"]/section[@class="newsEven"]')
        for article in articles:
            url = article.xpath(".//a/@href").get()
            full_url = response.urljoin(url) if url else None
            day = article.xpath(".//div[@class='date']/time/i/text()").get()
            month_year = article.xpath(".//div[@class='date']/time/text()[normalize-space()]").get()
            day = day.strip() if day else ""
            month_year = month_year.strip() if month_year else ""
            if full_url and day and month_year:
                try:
                    date_str = f"{month_year}-{day.zfill(2)}"
                    date = datetime.strptime(date_str, "%Y-%m-%d").strftime(self.date_format())
                    self.article_data_map[full_url] = {"date": date}
                except Exception:
                    continue