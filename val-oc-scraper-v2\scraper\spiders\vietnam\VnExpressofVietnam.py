from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization

class VnExpressofVietnam(OCSpider):
    name = "VnExpressofVietnam"

    start_urls_names = {
        'https://VnExpress.net/thoi-su': 'news'
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return 'unknown'

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    @property
    def language(self):
        return "Vietnamese"

    def get_articles(self, response) -> list:
        articles = response.xpath('//h3[@class="title-news"]//a/@href').getall()
        return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="title-detail"]//text()').get() or ''
        
    def get_body(self, response) -> str:
        body = response.xpath('//p[@class="description"]//text()').getall()
        body.extend(response.xpath('//article[@class="fck_detail "]//p[@class="Normal"]//text()').getall())
        return body_normalization(body)
        
    def get_date(self, response) -> str:
        date_data = response.xpath('//span[@class="date"]//text()').get() or response.xpath('//div[@class="left txt-dieuhuong flexbox"]//span//text()').get() or response.xpath('//div[@class="left time_post_art"]/text()').get() or ''
        date = re.search(r'\d{1,2}/\d{1,2}/\d{4},\s\d{2}:\d{2}', date_data)
        if date:
            return date.group(0)
        else:
            ValueError(f"Date not found for url = {response.url}")
       
    def date_format(self) -> str:
        return "%d/%m/%Y, %H:%M"

    def get_images(self, response) -> list[str]:
        return response.xpath('//figure//picture//img[@class="lazy lazied"]').getall()

    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[@class="btn-page next-page "]/@href').get()