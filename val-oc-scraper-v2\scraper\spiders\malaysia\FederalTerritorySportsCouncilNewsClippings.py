
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class FederalTerritorySportsCouncilNewsClippings(OCSpider):
    name = "FederalTerritorySportsCouncilNewsClippings"

    start_urls_names = {
        "https://www.wipers.gov.my/keratan-akhbar/": "News"      # pagination is not supported
        }
    start_urls_with_no_pagination_set={
        "https://www.wipers.gov.my/keratan-akhbar/"
    }
    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       data= response.xpath("//ol/li/ol/li/a[contains(@href, '.pdf')]/@href").getall()
       return data
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
       link = response.url
       filename = link.split("/")[-1].replace(".pdf", "")
       return f'Wilayahku {filename}'
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
       link = response.url
       match = re.search(r'/(\d{4})', link)
       year = match.group(1)
       year1= str(year)
       parsed_date = dateparser.parse(year1, languages=['en', 'ms'])
       return parsed_date.strftime("%Y") 

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 