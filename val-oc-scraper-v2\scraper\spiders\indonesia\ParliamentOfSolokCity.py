from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfSolokCity(OCSpider):
    name = "ParliamentOfSolokCity"

    start_urls_names = {
        "https://dprd.solokkota.go.id/post/": "POST"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_to_date_map = {}  # Mapping date with child articles from start URL
    
    def get_articles(self, response) -> list:  
        self.article_date_map(response) 
        return response.xpath('//div[@class="card-body"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="blog_details"]/h2/text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="blog_details"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="blog_details"]//p//img/@src').getall()
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        article_url = response.url
        article_date = self.article_to_date_map.get(article_url, None)
        if article_date:
            return article_date
        else:
            self.logger.error(f"No date found for URL: {article_url}")
            return None
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//li[@class="page-item"]/a[@rel="next"]/@href').get()
    
    def article_date_map(self, response):
        map = {}
        for item in response.xpath('//div[@class="card card-sm h-100"]'):
            url = item.xpath('.//div[@class="card-body"]//a/@href').get()
            date = item.xpath('.//div[@class="text-muted"]//text()').get()
            if url and date:
                full_url = response.urljoin(url)
                clean_date = date.strip()
                map[full_url] = clean_date
        self.article_to_date_map.update(map)
        return         