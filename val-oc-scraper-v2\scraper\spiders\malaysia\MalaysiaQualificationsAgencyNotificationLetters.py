# from scraper.OCSpider import OCSpider
# import re

# class MalaysiaQualificationsAgencyNotificationLetters(OCSpider):
#     name = "MalaysiaQualificationsAgencyNotificationLetters"

#     start_urls_names = {
#         "https://www.mqa.gov.my/new/pubsnotificationletter_2025.cfm#gsc.tab=0": "Notification Letters"
#     }

#     start_urls_with_no_pagination_set = {
#         "https://www.mqa.gov.my/new/pubsnotificationletter_2025.cfm#gsc.tab=0"
#     }

#     charset = "iso-8859-1"

#     country = "Malaysia"

#     @property
#     def language(self): 
#         return "Malay"

#     @property
#     def source_type(self) -> str:
#         return "ministry"
    
#     @property
#     def timezone(self):
#         return "Asia/Kuala_Lumpur"
    
#     article_data_map = {}

#     def get_articles(self, response) -> list:
#         articles = []
#         for row in response.xpath("//table[@class='table table-bordered table-striped']/tbody/tr"):
#             url = row.xpath(".//td[3]//a/@href").get()
#             title = row.xpath("./td[2]/text()").get()
#             if not url or not title:
#                 continue
#             url = response.urljoin(url.strip())
#             title = title.strip()
#             year_match = re.search(r"\b(\d{4})(?:\s*-\s*(\d{4}))?\b", title)
#             if year_match:
#                 year = year_match.group(1)
#             else:
#                 year = ""
#             self.article_data_map[url] = {
#                 "title": title,
#                 "date": year,
#                 "full_url": url,
#             }
#             articles.append(url)
#         return articles

#     def get_href(self, entry) -> str:
#         return entry

#     def get_title(self, response) -> str:
#         return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

#     def get_body(self, response) -> str:
#         return ""

#     def get_images(self, response) -> list:
#         return []
        
#     def date_format(self) -> str:
#         return "%Y-%m-%d"

#     def get_date(self, response) -> str:
#         return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

#     def get_authors(self, response):
#         return ""

#     def get_document_urls(self, response, entry=None) -> list:
#         return [response.url]

#     def get_page_flag(self) -> bool:
#         return False

#     def get_next_page(self, response):
#         return None





from scraper.OCSpider import OCSpider
import dateparser
from scraper.utils.helper import body_normalization


class MalaysiaQualificationsAgencyNotificationLetters(OCSpider):
    name = "MalaysiaQualificationsAgencyNotificationLetters"

    start_urls_names = {
        "https://www.mqa.gov.my/new/pubsnotificationletter_2025.cfm#gsc.tab=0": "Notification Letters"
    }

    start_urls_with_no_pagination_set = {
        "https://www.mqa.gov.my/new/pubsnotificationletter_2025.cfm#gsc.tab=0"
    }

    charset = "iso-8859-1"
    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for row in response.xpath("//table[@class='table table-bordered table-striped']/tbody/tr"):
            url = row.xpath(".//td[3]//a/@href").get()
            title_raw = row.xpath(".//td[2]").get()
            title_text = row.xpath(".//td[2]//text()").getall()
            title = body_normalization(title_text)

            if not url or not title:
                continue

            url = response.urljoin(url.strip())
            parsed_date = dateparser.parse(title, languages=["ms"], settings={"PREFER_DAY_OF_MONTH": "first"})

            self.article_data_map[url] = {
                "title": title,
                "full_url": url,
                "date": parsed_date.strftime("%Y-%m-%d") if parsed_date else ""
            }
            articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None
