from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
import re
class EducationServiceCommissionNewsClippings(OCSpider):
    name = "EducationServiceCommissionNewsClippings"

    start_urls_names = {
        f"https://www.spp.gov.my/ms/keratan-akhbar/{year}": "News"
         for year in range(2025,2015,-1)
        }
    
    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "Bank"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
        articles= []
        for article in response.xpath('//table//tr'):
            url = article.xpath(".//th//a//@href").get()
            date=article.xpath(".//td[1]//text()").get()
            title= body_normalization(article.xpath(".//th//a//text()").getall())
            if url and date and title:
                articles.append(url)
                self.article_data_map[url]={
                    "date":date,"title":title
                }

        return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ''

    def get_images(self, response) -> list:
        return response.xpath('//*[@id="g-mainbody"]//p/img/@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        dates =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(dates, languages=['ms','en'])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return response.xpath('//*[@id="g-mainbody"]//a//@href').getall()
           
        
            
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) :
        next_page = response.xpath('//a[@aria-label="Go to next page"]//@href').get()
        if  next_page:
            return next_page
        return None 