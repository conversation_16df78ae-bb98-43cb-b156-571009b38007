from scraper.OCSpider import OCSpider

class ValuationAndPropertyServicesDepartmentBulletin(OCSpider):
    name = "ValuationAndPropertyServicesDepartmentBulletin"
    
    start_urls_names = {
        "https://www.jpph.gov.my/v3/ms/buletin-jpph/": "News"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "industry_association"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='tab-content']//table//tbody//tr"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//a//text()").get()
            if url and title:
                self.article_data_map[url] = {"title" : title}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m/%Y"
    
    def get_date(self, response): 
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")    
        return title.split(" ")[-1]
  
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None