
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from typing import Optional
import scrapy

class HaNoiPortalofVietnam(OCSpider):
    name = "HaNoiPortalofVietnam"

    start_urls_names = {
        "https://hanoi.gov.vn/tin-tuc-su-kien-noi-bat": "News",
        }
    
    api_start_url = {
        "https://hanoi.gov.vn/tin-tuc-su-kien-noi-bat": {
            'url': "https://hanoi.gov.vn/api/NewsZone/NewsZone",
            "payload" : {
                "PageIndex" : "1",
                "PageSize":"TwNsaMrfVrU=",
                "Catname":"nMSbbZ2pR0/XHR7JMRKsFGiPETTBu6V1",
                "LanguageId":"jM2HDDVEz40=",
                "Site":"/CP0MQRJUt0=",
                "DataIds[]":"4250825182541245",
                "DataIds[]":"4250825210249275",
                "DataIds[]": "4250825112559863",
                "DataIds[]": "4250825193456124",
            }
        }
    }

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["PageIndex"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
        
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//div[@class="news-info"]//a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detail-body"]//p//text()').getall())
   
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="detail-body"]//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//span[@data-role="publishdate"]//text()').get()
        date_obj = dateparser.parse(date, languages=['vi'])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None)->list:
        return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return str(int(response.meta.get("current_page")) + 1)

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_url.get(start_url)
        if not api_data:
           self.logger.error("API data not found for start_url")
           return
        api_url = api_data["url"]
        next_page = self.get_next_page(response, current_page)
        if next_page:
          payload = response.meta.get("payload", {}).copy()
          payload["PageIndex"] = str(next_page)
          yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            formdata=payload,
            callback=self.parse_intermediate,
            meta={"current_page": next_page, "start_url": start_url, "payload": payload},
            dont_filter=True,
           )
        else:
         return 
         
