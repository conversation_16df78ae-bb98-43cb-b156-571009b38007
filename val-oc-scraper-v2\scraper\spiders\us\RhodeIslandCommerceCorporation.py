from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class RhodeIslandCommerceCorporation(OCSpider):
    name = "RhodeIslandCommerceCorporation"

    country = "US"

    start_urls_names = {
        "https://commerceri.com/articles/" : "Press"
    }

    charset="utf-8"

    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    def get_articles(self, response) :
        return response.xpath('//h3//a//@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="post-content"]//p//text()').getall())

    def get_images(self, response) :
        return []

    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response) -> str:
        date= response.xpath('//div[@class="author-by-line"]//time//text()').get()
        date= date.replace(".","").strip()
        return date
    
    def get_authors(self, response) :
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        # No next page to scrape
        return response.xpath("//div[@class='paging-button']//a[@class='next-link']//@href").get()