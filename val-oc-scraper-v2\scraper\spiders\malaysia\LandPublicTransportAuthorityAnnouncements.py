from scraper.OCSpider import OCSpider
from typing import Optional
from urllib.parse import urljoin

class LandPublicTransportAuthorityAnnouncements(OCSpider):
    name = "LandPublicTransportAuthorityAnnouncements"
    
    start_urls_names = {
        "https://www.apad.gov.my/sumber-maklumat1/media/pengumuman3": "Announcement"  # Pagination is not supported 
    }

    start_urls_with_no_pagination_set = {
        "https://www.apad.gov.my/sumber-maklumat1/media/pengumuman3"
    }

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"    
    
    def get_articles(self, response) -> list:
        hrefs = response.xpath("//div[contains(@class, 'sppb-article-info-wrap')]//h3/a/@href").getall()
        full_urls = [urljoin(response.url, href) for href in hrefs]
        return full_urls

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='article-header']/h1[@itemprop='headline']//text()").get()

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        base_url = response.url
        img_srcs = response.xpath("//div[@class='col-lg-12']//img/@src").getall()
        full_urls = [urljoin(base_url, src) for src in img_srcs]
        return full_urls

    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> Optional[str]:
        return response.xpath("normalize-space(substring-after(//li[contains(@class, 'd-inline-block') and contains(@class, 'me-3')], ':'))").get()

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None