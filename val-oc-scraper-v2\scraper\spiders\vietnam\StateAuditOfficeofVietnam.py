from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class StateAuditOfficeofVietnam(OCSpider):
    name = "StateAuditOfficeofVietnam"

    start_urls_names = {
        "https://www.sav.gov.vn/Pages/chi-tiet-tin.aspx?l=TinTucSuKien": "News",  
        } 
    
    start_urls_with_no_pagination_set = {}


    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//div[@class="n-title"]//a//@href').getall()
       
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('(//h1//text())[2]').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p[@style="text-align:justify;"]//text() | //div[@style="text-align:justify;"]//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//p//img//@src | //figure//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('(//div[@class="date col-md-2"]//text())[2]').get()
        clean_date = date.strip() 
        date_obj = dateparser.parse(clean_date, languages=["vi"])
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return response.xpath('//a[normalize-space(text())="Sau"]/@href').get()