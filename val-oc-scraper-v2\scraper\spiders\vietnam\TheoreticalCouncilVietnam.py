from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy

class TheoreticalCouncilVietnam(OCSpider):
    name = "TheoreticalCouncilVietnam"

    country = "Vietnam"

    start_urls_names = {
        "https://hdll.vn/vi/tin-tuc.html": "Tin tức",
    }

    start_urls_with_no_pagination_set = {}

    api_start_urls = {
        "https://hdll.vn/vi/tin-tuc.html": {
            "url": "https://hdll.vn/vi/tin-tuc.html",
            "payload": {
                "__EVENTTARGET": "ctl00$cphMain$ctl00$pager",
                "__EVENTARGUMENT": "1",
                "__VIEWSTATE": "",
                "__VIEWSTATEGENERATOR": "",
                "ctl00$Searching1$txtTimKiem": "",
                "ctl00$Searching1$hdKeyword": "",
                "ctl00_HorizontalTopPrimaryTop_mnMenu_ClientState": "",
                "ctl00$WebLink$ddlWebLink": "",
            }
        }
    }

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                      "AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return

        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]
        payload["__VIEWSTATE"] = response.xpath(
            '//input[@id="__VIEWSTATE"]/@value'
        ).get("") or ""
        payload["__VIEWSTATEGENERATOR"] = response.xpath(
            '//input[@id="__VIEWSTATEGENERATOR"]/@value'
        ).get("") or ""

        current_page = str(response.meta.get("current_page", "1"))
        payload["__EVENTTARGET"] = "ctl00$cphMain$ctl00$pager"
        payload["__EVENTARGUMENT"] = current_page

        yield scrapy.FormRequest(
            url=api_url,
            formdata=payload,
            headers={**self.headers, "Referer": start_url},
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": int(current_page),
            },
            dont_filter=True,
        )

    def get_articles(self, response) -> list:
        relative_urls = list(set(response.xpath(
            '//div[@class="n-items"]//article//a[@class="title"]/@href'
        ).getall()))
        return [response.urljoin(url) for url in relative_urls]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="n-title"]//span[@class="title-article"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="n-desc"]//text()').getall())

    def get_images(self, response) -> list:
        hrefs = response.xpath('//div[@class="n-desc"]//img/@src').getall()
        return [response.urljoin(h) for h in hrefs]

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        raw_date = response.xpath('normalize-space(//span[@class="ngayphathanh"]/text())').get()
        if raw_date:
            return raw_date.replace("Ngày phát hành:", "").strip()
        return ""

    def get_authors(self, response):
        return ""
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return (int(current_page) if current_page else 1) + 1

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"]
        payload = api_data["payload"].copy()
        next_page = self.get_next_page(response, current_page)
        viewstate = response.xpath('//input[@id="__VIEWSTATE"]/@value').get("")
        if not viewstate:
            return 
        payload.update({
            "__EVENTTARGET": "ctl00$cphMain$ctl00$pager",
            "__EVENTARGUMENT": str(next_page),
            "__VIEWSTATE": viewstate,
            "__VIEWSTATEGENERATOR": response.xpath(
                '//input[@id="__VIEWSTATEGENERATOR"]/@value'
            ).get("") or "",
        })
        yield scrapy.FormRequest(
            url=api_url,
            formdata=payload,
            headers={**self.headers, "Referer": response.url},
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page,
            },
            dont_filter=True,
        )
