import json
import re
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaInternationalExchangeAndPromotiveAssociationForMedicalAndHealthCare(OCSpider):
    name="ChinaInternationalExchangeAndPromotiveAssociationForMedicalAndHealthCare"

    start_urls_names={
        "https://www.cpam.org.cn/pages/old/index/more?columnId=233":"学会动态", 
        "https://www.cpam.org.cn/pages/old/index/more?columnId=280":"学习动态",
        "https://www.cpam.org.cn/pages/old/index/more?columnId=194":"重要通告", 
    }

    charset = "utf-8"

    api_start_urls = {
        "https://www.cpam.org.cn/pages/old/index/more?columnId=233": {
            "url": "https://www.cpam.org.cn/suanzhe-web/api/web/news/list",
            "payload": {
                "columnId": "233",
                "countAction": "1",
                "pageNum": "1",
                "pageSize": "6",
                "systemNeedAssociatedOrderFlag": "desc",
                "systemNeedAssociatedGeTime": "2021-01-30 16:44:08", #This time is given for that date all articles till today are given
            },
        },
        "https://www.cpam.org.cn/pages/old/index/more?columnId=280": {
            "url": "https://www.cpam.org.cn/suanzhe-web/api/web/news/list",
            "payload": {
                "columnId": "280",
                "countAction": "1",
                "pageNum": "1",
                "pageSize": "6",
                "systemNeedAssociatedOrderFlag": "desc",
                "systemNeedAssociatedGeTime": "2021-01-30 16:44:08",
            },
        },
        "https://www.cpam.org.cn/pages/old/index/more?columnId=194": {
            "url": "https://www.cpam.org.cn/suanzhe-web/api/web/news/list",
            "payload": {
                "columnId": "194",
                "countAction": "1",
                "pageNum": "1",
                "pageSize": "6",
                "systemNeedAssociatedOrderFlag": "desc",
                "systemNeedAssociatedGeTime": "2021-01-30 16:44:08",
            },
        },
    }

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["pageNum"] = payload.get("pageNum")
        yield scrapy.http.FormRequest(
            url = api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;",
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["pageNum"]
            },
        )

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            articles = data.get("data", {}).get("list", [])
            article_urls = [
                self.construct_article_url(article)
                for article in articles
                if article
            ]
            return article_urls
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []
        
    def get_href(self, entry) -> str:
        return entry   

    def get_title(self, response) -> str:
        return response.xpath("//uni-view[@class='content']//uni-text[@class='detail-font']//span/text()").get() 
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//uni-view[@class='content']//p//text()").getall())    

    def get_images(self, response) -> list:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.urljoin("https://www.cpam.org.cn" + doc.replace("https://proxy.scrapeops.io/", "")) for doc in response.xpath('//uni-view[@class="content"]//p//a/@href').getall()]

    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        return re.search(r"\d{4}-\d{2}-\d{2}", response.xpath('//uni-view[@class="detail-time"]//text()').get()).group()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        total_pages = int(response.json().get('data', {}).get('pages', 0))
        return str(int(current_page) + 1) if int(current_page) < total_pages else None

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['pageNum'] = next_page
            yield scrapy.http.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;",
                },
                callback=self.parse_intermediate,
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                        'payload': payload,
                    }
            )
        else:
            yield None
                    
    def construct_article_url(self, article):
        hbp = HeadlessBrowserProxy()
        article_id = article.get('id')
        if article_id:
            return hbp.get_proxy(f"https://www.cpam.org.cn/pages/newDetails/newDetails?id={article_id}",timeout=50000)   # Eg: "article url": "https://www.capa.com.cn/#/index/NewsDetail?activeName=%E8%A1%8C%E4%B8%9A%E5%B9%B4%E6%8A%A5&id=1859123183003656193"
        else:
            return None