from typing import Optional
from scraper.OCSpider import OCSpider
import re
import dateparser

class ParliamentOfMalaysiaUHBills(OCSpider):
    name = "ParliamentOfMalaysiaUHBills"
    
    start_urls_names = {
       "https://www.parlimen.gov.my/bills-dewan-negara.html?uweb=dn&lang=bm":"UH Bills",
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for row in response.xpath('//table[contains(@class,"table-style-two")]//tbody//tr'):
            onclick = row.xpath('.//td[1]/a/@onclick').get()
            match = re.search(r"'(/files/billindex/pdf/[^']+)'", onclick or "")
            pdf_path = match.group(1) if match else None
            pdf_url = response.urljoin(pdf_path) if pdf_path else None
            date = row.xpath('.//td[2]/text()').get()
            title = row.xpath('.//td[3]/text()').get()
            if pdf_url and date and title:
                self.article_data_map[pdf_url] = {
                    "date": date.strip(),
                    "title": title.strip(),
                }
                articles.append(pdf_url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scarpe
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None