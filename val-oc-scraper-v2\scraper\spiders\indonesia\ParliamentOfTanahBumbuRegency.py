from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfTanahBumbuRegency(OCSpider):
    name = "ParliamentOfTanahBumbuRegency"

    start_urls_names = {
        "https://dprd.tanahbumbukab.go.id/list_berita": "berita"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    INDONESIAN_MONTHS = {
        'Januari': 'January',
        'Februari': 'February',
        'Maret': 'March',
        'April': 'April',
        'Mei': 'May',
        'Juni': 'June',
        'Juli': 'July',
        'Agustus': 'August',
        'September': 'September',
        'Oktober': 'October',
        'November': 'November',
        'Desember': 'December',
    }

    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="blog-card-text-area"]//h4//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h3[@class="mt-0"]//text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="col-lg-8"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="col-lg-8"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        date = response.xpath('(//div[@class="blog-date"]//li)[2]//text()').re_first(r'\d{1,2} \w+ \d{4}')
        for indo,eng in self.INDONESIAN_MONTHS.items():
            if indo in date:
                date = date.replace(indo,eng)
                break
        return date
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None