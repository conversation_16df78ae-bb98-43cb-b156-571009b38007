from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class NationalPoliticalPublishingHouse(OCSpider):
    name = "NationalPoliticalPublishingHouse"
    
    start_urls_names = {
        "https://www.nxbctqg.org.vn/hoat-dong-nha-xuat-ban-.html": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"
    
    @property
    def language(self): 
        return "Vietnamese"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="content"]//h3[@class="name"]//a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="box-item module"]//h1[@class="name"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="text-pages"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="text-pages"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_string = response.xpath('substring-before(substring-after(//div[@class="ngaydang"]/text()[normalize-space()], "Ngày đăng: "), " -")').get()
        return date_string.strip() if date_string else None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return response.xpath('//div[@class="pagination-page"]//a[contains(text(), "›")]/@href').get()

