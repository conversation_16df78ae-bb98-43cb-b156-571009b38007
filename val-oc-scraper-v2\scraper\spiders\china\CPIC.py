from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re
class CPIC(OCSpider):
    name = "CPIC"

    start_urls_names = {
        "https://www.cpic.com.cn/aboutUs/gsdt/rdxw/index.shtml?hit=ShouyeXwMore": "中国太保",
        "https://www.cpic.com.cn/aboutUs/gsdt/zxgg/index.shtml": "中国太保",
        "https://www.cpic.com.cn/ir/zybg/aggg/": "中国太保", 
        "https://www.cpic.com.cn/ir/zybg/aggg/?subMenu=1&inSub=1": "中国太保",
        "https://www.cpic.com.cn/ir/zybg/hggg/?subMenu=2&inSub=2": "中国太保",
        "https://www.cpic.com.cn/ir/gsbgytj/dqbg/?subMenu=1&inSub=1": "中国太保", 
        "https://www.cpic.com.cn/ir/gsbgytj/hg/?subMenu=2&inSub=2": "中国太保",
        "https://www.cpic.com.cn/ir/gszl/gddh/?subMenu=2&inSub=3": "中国太保",
        "https://www.cpic.com.cn/ir/zybg/zdgljy/?subMenu=1&inSub=1": "中国太保",
        "https://www.cpic.com.cn/ir/zybg/cfnlxx/?subMenu=2&inSub=1": "中国太保",
        "https://www.cpic.com.cn/ir/zybg/zjyy/?subMenu=3&inSub=1": "中国太保",
    }

    article_data_map ={}  # Mapping child article with title and PDF from start URL

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            articles = []
            for article in response.xpath("//div[@class='conRight']//ul//li"):
                url = article.xpath(".//a[@class='listText']/@href | .//a/@href").get()
                title = article.xpath(".//a[@class='listText']//text() | .//a//text()").get()
                if url and title:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower() or '.docx' in full_url.lower() or ".doc" in response.url.lower():
                        pdf = f'https://www.cpic.com.cn{full_url}'
                    else:
                        pdf = "None"
                    self.article_data_map[full_url] = {"title": title, "pdf": pdf}
                    articles.append(full_url) 
            article_list= set(articles)
            return list(article_list)
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower() or ".docx" in response.url.lower() or ".doc" in response.url.lower():
            return ""
        else:
            return body_normalization(response.xpath("//div[@class='conRight']//p//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        if '.docx' in response.url.lower():
            match = re.search(r"/news/(\d{4})(\d{2})(\d{2}).docx", response.url)
            if match:
                year, month, day = match.groups()
                return f"{year}-{month}-{day}"
            return None
        elif '.pdf' in response.url.lower() or '.doc' in response.url.lower():
            match = re.search(r"/file/(\d{4})/(\d{2})/(\d{2})/", response.url)
            if match:
                year, month, day = match.groups()
                return f"{year}-{month}-{day}"
            return None
        else:
            match = re.search(r"/c/(\d{4})-(\d{2})-(\d{2})/", response.url)
            if match:
                year, month, day = match.groups()
                return f"{year}-{month}-{day}"
            return None
        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")
    
    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower() or ".docx" in response.url.lower() or ".doc" in response.url.lower():
            return ""
        else:
            return response.xpath("//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='pagebar clearself']//a[span[text()='下一页']]/@href").get()
        if next_page:
            return next_page
        else:
            return None    