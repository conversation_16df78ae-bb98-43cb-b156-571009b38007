#MalaysiaPetroleumResourcesCorporationPressReleases
#MalaysiaHighwayAuthorityPressReleases
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
from scraper.middlewares import HeadlessBrowserProxy
import re

class MalaysiaPetroleumResourcesCorporationPressReleases(OCSpider):
    name = "MalaysiaPetroleumResourcesCorporationPressReleases"

    start_urls_names = {
    "https://mprc.gov.my/resources/releases/":"Resourse release"  #pagination not supportd 
}
    start_urls_with_no_pagination_set={
       "https://mprc.gov.my/resources/releases/"
    }

    charset = "iso-8859-1"

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=1000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request 
   

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
     return response.xpath('/html/body/div/div/div[1]/div/a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
     return response.xpath('/html/body/section/div/div[1]/div[1]/h2//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('/html/body/section/div/div[1]/div[3]/p').getall())

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
     date = response.xpath("/html/body/section/div/div[1]/div[3]/p[2]/b/span//text()").get()
     match = re.search(r"\b\d{1,2} [A-Z]+ \d{4}\b", date, re.IGNORECASE)
     date_str = match.group(0)
     parsed_date = dateparser.parse(date_str, languages=['ms', 'en'])
     formatted_date = parsed_date.strftime("%B %d, %Y")
     return formatted_date
     
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 