import logging
from typing import Union
import scrapy
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy

class ChinaTourismGroupDutyFreeCorporationLimited(OCSpider):
    name = 'ChinaTourismGroupDutyFreeCorporationLimited'

    start_urls_names = {
        # 'https://www.ctgdutyfree.com.cn/CompanyNews.html': 'Company News', # issue with the child articles 
        # 'https://www.ctgdutyfree.com.cn/GroupNews.html': 'Group News'  # issue with the child articles
        'https://www.ctgdutyfree.com.cn/caiwubaogao.html' : 'Financial Reports',
        'https://www.ctgdutyfree.com.cn/Announcement.html' : 'Company Announcement'
    }
    
    api_start_url = {
        'https://www.ctgdutyfree.com.cn/caiwubaogao.html': 'https://open.sseinfo.com/ir2/notice?code=601888&bulletintype=1&page={page}&perpage=10',
        'https://www.ctgdutyfree.com.cn/Announcement.html': 'https://open.sseinfo.com/ir2/notice?code=601888&bulletintype=2&page={page}&perpage=10'
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_base_url = self.api_start_url.get(start_url)
        # if api_base_url:
        current_page = response.meta.get("current_page", 1)
        api_url = api_base_url.format(page = current_page)
        if not api_base_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        yield scrapy.Request(
            url = api_url,
            callback = self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_base_url,
                "current_page": current_page
            }
        )
    #     else :
    #         # Pattern for websites where the landing page alone has JS rendering
    #         hbp = HeadlessBrowserProxy()
    #         request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
    #         )
    #         request.meta['start_url'] = response.request.meta['start_url']
    #         yield request
    
    # custom_settings = {
    #         "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    # }
    
    
    article_data_map ={}

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        # relative_articles = response.xpath('//div[@class="center"]//div[@class="right"]//a/@href').getall()
        # # articles = articles.replace('https://proxy.scrapeops.io/','https://www.ctgdutyfree.com.cn/')
        # # print("the articles are:", relative_articles)
        
        # if relative_articles:
        #     articles = [f'https://www.ctgdutyfree.com.cn/{article}' for article in relative_articles]
        #     return articles
        
        
        
        # else:
            data = response.json()
            articles = []
            for item in data.get("pageContent", []):
                url = item.get("url")
                title = item.get("title")
                date = item.get("ssedate")

                if url and title and date:
                    full_url = url
                    clean_title = title.strip()
                    clean_date = date.strip()
                    
                    # Check if it's a PDF
                    if ".pdf" in full_url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"

                    # Store in your article map
                    self.article_data_map[full_url] = {
                        "title": clean_title,
                        "date": clean_date,
                        "pdf": pdf
                    }

                    articles.append(full_url)

            return articles


        # return response.xpath('//div[@class= "center"]//div[@class= "right"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        return response.xpath('//div[@class= " col-md-8"]/h1//text()').get()
    
    def get_body(self, response) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath('//div[@class= "article-text"]//p//text()').getall())
    
    def get_images(self, response) -> list[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath('//div[@class= "article-text"]//p//img/@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        return response.xpath('//div[@class= "share_box"]//span//text()').get()
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None)-> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response, current_page) -> Union[None, str]:
        if response.status!=200:
            return None
        else:
            return current_page + 1

    def go_to_next_page(self, response, start_url, current_page):
        api_url = response.meta.get("api_url")
        if not api_url: 
            logging.info("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        print("THE VALUE OF NEXT PAGE IS:", next_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None