#Customs News
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class CustomsNews(OCSpider):
    name = "CustomsNews"

    start_urls_names = {
        "https://haiquanonline.com.vn/chuyen-dong": "News",
        }

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//*[@id="main"]/div[2]/div/div[3]/div[1]/div/div[1]/div/h3/a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="__MASTERCMS_CONTENT fw mb clearfix"]//p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//div[@class="__MASTERCMS_CONTENT fw mb clearfix"]//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//*[@id="main"]/div[2]/div/div/div[1]/div[1]/div[2]/span//text()').get()
        only_date = date.split("|")[-1].strip()
        date_obj = dateparser.parse(only_date, languages=["vi"])
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          link=  response.xpath('//div[@class="__MB_ARTICLE_PAGING fw lt f0 clearfix"]//a//@href').getall()
          if len(link)==1:
              return link[0]
          else:
              return link[1]
