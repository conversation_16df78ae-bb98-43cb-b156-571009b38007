#SelangorStateGovernmentStateSecretarysCirculars
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
import re
class SelangorStateGovernmentStateSecretarysCirculars(OCSpider):
    name = "SelangorStateGovernmentStateSecretarysCirculars"

    start_urls_names = {
        "https://www.selangor.gov.my/index.php/database_stores/store_view/11": "SUK-Announcement-2019",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/18":"SUK-Announcement-2020",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/17":"ICT Circular",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/21":"SUK-Announcement-2021",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/26":"SUK-Announcement-2022",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/27":"SUK-Announcement-2023",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/29":"SUK-Announcement-2024",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/31":"SUK-Announcement-2025",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/28":"SUK Circular Letter 2023",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/32":"Suk-Circular-letter-2025",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/16":"Covid-19-circular",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/30":"News"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       articles= []
       for article in response.xpath('//*[@class="grid-item-box"]'):
           title = article.xpath('.//h3/text()').get()
           link = article.xpath('.//p//a//@href').get()
           
           if link:
               articles.append(link)
               self.article_data_map[link]={
                    "title":title,
                    "link":link
                }
       return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
   
    def get_images(self, response) -> list:
         return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        title =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        match = re.search(r'20\d{2}', title)
        year = match.group()
        parsed_date = dateparser.parse(year, languages=['ms','en'])
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")]
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None