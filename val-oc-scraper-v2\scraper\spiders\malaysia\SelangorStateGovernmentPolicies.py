from scraper.OCSpider import OCSpider
import re
from scrapy.http import TextResponse

class SelangorStateGovernmentPolicies(OCSpider):
    name = "SelangorStateGovernmentPolicies"

    start_urls_names = {
        'https://www.selangor.gov.my/index.php/pages/view/81?mid=642': 'policy'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.selangor.gov.my/index.php/pages/view/81?mid=642"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    articles_url_date_mapping = {}

    def get_articles(self, response) -> list:
        articles_list = []
        articles = response.xpath('//div[@class="editable"]//tr')
        date_data = response.xpath('//em/text()').get()
        pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})"
        match = re.search(pattern, date_data)
        for article in articles:
            href = article.xpath('.//td//a/@href').get()
            full_url = response.urljoin(href)
            title = article.xpath('.//li[@style="text-align: left;"]/span/text()').get()
            if match and href:
                self.articles_url_date_mapping[full_url] = [title , match.group(0)]
                articles_list.append(full_url)
        return articles_list

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.articles_url_date_mapping.get((response.request.url),[])[0]

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        return self.articles_url_date_mapping.get((response.request.url),[])[1]

    def date_format(self) -> str:
        return "%Y-%m-%d %H:%M:%S"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_document_urls(self, response, entry=None):
        if entry and entry.lower().startswith("http"):
            return [entry]
        content_type = response.headers.get("Content-Type", b"").decode().lower()
        if any(x in content_type for x in ["application/pdf", "application/msword", 
                                        "application/vnd.openxmlformats-officedocument"]):
            return [response.url]
        urls = []
        if isinstance(response, TextResponse):
            doc_links = response.xpath(
                '//a[contains(translate(@href, "PDFDOCX", "pdfdocx"), ".pdf") '
                'or contains(translate(@href, "PDFDOCX", "pdfdocx"), ".doc") '
                'or contains(translate(@href, "PDFDOCX", "pdfdocx"), ".docx")]/@href'
            ).getall()
            for link in doc_links:
                if link:
                    urls.append(response.urljoin(link.strip()))
        return urls
    
    def get_next_page(self, response):
        return None