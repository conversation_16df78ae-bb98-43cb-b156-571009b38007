from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization

class ColoradoOfficeOfEconomicDevelopmentandInternationalTrade(OCSpider):
    name = 'ColoradoOfficeOfEconomicDevelopmentandInternationalTrade'

    country = "US"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 3,
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 20000  # 10 seconds wait time

    start_urls_names = {
        "https://oedit.colorado.gov/category/press-release": "News",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "America/Denver"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='views-row']//h2//a//@href").getall()
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='region region-content']//p//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%A, %B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='author_date show']//time//text()").get().strip()

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return response.xpath("//ul[@class='pagination js-pager__items']//li//a[contains(text(),'››')]//@href").get()