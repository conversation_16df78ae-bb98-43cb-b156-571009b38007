from datetime import datetime
import re
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfNorthLabuhanbatuRegency(OCSpider):
    name = "ParliamentOfNorthLabuhanbatuRegency"
    
    country = "ID"
    
    start_urls_names = {
        "https://dprd.lumajangkab.go.id/index.php/Live/kel_berita/4": "",
        # "https://dprd.lumajangkab.go.id/index.php/Live/produk_hukum": ""
    }

    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response):
        return response.xpath('//h4//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "col-md-8"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_images(self, response) -> list:
        return []

    def get_date(self, response) -> str:
        MONTH_MAP = {
            'Januari': 'January',
            'Februari': 'February',
            'Maret': 'March',
            'April': 'April',
            'Mei': 'May',
            'Juni': 'June',
            'Juli': 'July',
            'Agustus': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Desember': 'December'
        }

        raw_date = response.xpath('//div[@class= "container"]//p//text()').get()

        if not raw_date:
            return None

        raw_date = raw_date.strip().replace("WIB", "").strip()

        # Extract only the part with the date (e.g., 29 Oktober 2022)
        match = re.search(r"\d{1,2} \w+ \d{4}", raw_date)
        if not match:
            print(f"Could not extract date from: {raw_date}")
            return None

        clean_date = match.group()  # e.g., '29 Oktober 2022'

        # Translate to English month
        for indo_month, eng_month in MONTH_MAP.items():
            if indo_month in clean_date:
                clean_date = clean_date.replace(indo_month, eng_month)
                break

        try:
            date_obj = datetime.strptime(clean_date, "%d %B %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError as e:
            print(f"Date parsing failed for: {clean_date}")
            raise e

    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return None
    
    