import re
from bs4 import BeautifulSoup
from scraper.OCSpider import <PERSON>CSpid<PERSON>

"""
Use this as a template scraper if you would like to set a charset that is
not utf-8

you can just set the charset variable to the desired charset
eg:
charset = "gb2312"
"""

class ChinaNationalAviationGroupCoLTD(OCSpider):

    name = "ChinaNationalAviationGroupCoLTD"

    start_urls_names = {
        'http://www.airchinagroup.com/cnah/xwzx/zhxw/index.shtml': '中航新闻',
        'http://www.airchinagroup.com/cnah/qywh/djgt/zzjs/index.shtml': '组织建设',
        'http://www.airchinagroup.com/cnah/qywh/djgt/hddt/index.shtml': '活动动态'
    }

    charset = "gb2312"

    @property
    def source_type(self) -> str:
        return 'SOE'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = response.xpath("//ul[@class='new_lk']/li")
        return articles

    def get_href(self, entry) -> str:
        return entry.xpath("./a/@href").get()

    def get_title(self, response) -> str:
        return "".join(response.xpath("//div[@class='tv_cn']/h1/text()").extract()).strip()

    def get_body(self, response) -> str:
        return response.xpath("//span[@class='ctextcont']").get()

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        date_str = "".join(response.xpath("//div[@class='datea']/text()").extract())
        return "-".join(re.findall("(\d{4})-(\d{1,2})-(\d{2})", date_str)[0])

    def get_images(self, response) -> list:
        images = []
        for imgTag in BeautifulSoup(self.get_body(response), features="lxml").find_all('img'):
            images.append(response.urljoin(imgTag['src']))
        return images

    def get_authors(self, response):
        return []

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//a[contains(text(), '>')]/@href").extract_first()
        if next_page:
            return response.urljoin(next_page)
        return None