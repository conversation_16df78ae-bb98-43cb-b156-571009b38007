from typing import List, Union
import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re
import json

class Seazen(OCSpider):
    name = "Sea<PERSON>"

    start_urls_names = {
        "https://www.seazen.com.cn/news/group.html": "新城控股",
        "https://www.seazen.com.cn/investor/bulletin.html": "新城控股",
        "https://www.seazen.com.cn/investor/periodic.html":"新城控股",
        # "https://www.seazen.com.cn/investor/finance.html ": "新城控股", #No articles
        # "https://www.seazen.com.cn/investor/shareholder.html": "新城控股", #No articles
    }

    
    api_start_url = {
        'https://www.seazen.com.cn/news/group.html': {
            'url': 'https://www.seazen.com.cn/news/index.aspx',
            'payload': {
                "pageid": "1",
                "pagecount": "8",
                "isCount": "true",
                "id": "0",
                "date": "",
                "text": ""
            },
        },
        'https://www.seazen.com.cn/investor/bulletin.html': {
            'url': 'https://www.seazen.com.cn/investor/bulletin.aspx',
            'payload': {
                "pageid": "1",
                "pagecount": "10",
                "isCount": "false",
                "id": "0",
            },
        },
        'https://www.seazen.com.cn/investor/bulletin.html': {
            'url': 'https://www.seazen.com.cn/investor/bulletin.aspx',
            'payload': {
                "pageid": "1",
                "pagecount": "10",
                "isCount": "false",
                "id": "0",
            },
        },
        'https://www.seazen.com.cn/investor/periodic.html': {
            'url': 'https://www.seazen.com.cn/investor/periodic.aspx',
            'payload': {
                "pageid": "1",
                "pagecount": "10",
                "isCount": "false",
                "id": "0",
            },
        },
    }

    article_data_map = {}  # Mapping title, date and PDF with child articles from start URL

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        current_page = response.meta.get("current_page", 1)
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            return
        payload = api_data["payload"]
        payload["pageid"] = str(current_page)
        api_url = api_data["url"]      
        yield scrapy.FormRequest(
            url=api_url,
            formdata=payload,
            method="POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            },
            callback=self.parse,
            meta={
                "start_url": start_url, 
                "current_page": current_page
            },
            dont_filter=True
        )

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
        except Exception:
            return []
        articles = data.get("data", [])
        results = []
        if isinstance(articles, list) and articles:
            for entry in articles:
                if 'url' in entry:
                    url = entry["url"]
                    title = entry["title"]
                    date = entry["ssedate"]
                    self.article_data_map[url] = {"title": title, "date": date, "pdf": url}
                    results.append(url)
                elif 'ID' in entry:
                    article_id = entry["ID"]
                    title = entry["biaoti"]
                    date = entry["fabushijian"]
                    url = f"https://www.seazen.com.cn/news/news-details-{article_id}.html"
                    self.article_data_map[url] = {"title": title, "date": date, "pdf": "None"}
                    results.append(url)
        return results

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        return response.xpath('//dd[@class="width100 fright"]/h1/text()').get()

    def get_body(self, response) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath('//div[@class="nd-conright area_80 mo_margintop20"]//p//text()').getall())

    def get_images(self, response) -> List[str]:
        if ".pdf" in response.url.lower():
            return []
        return response.xpath('//div[@class="nd-conright area_80 mo_margintop20"]//img/@src | //div[@class="newsd-con01 cf"]//img//@src').getall()

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        if ".pdf" in response.url.lower():
            raw_date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
            if raw_date:
                try:
                    return raw_date.split("T")[0] if "T" in raw_date else raw_date
                except Exception:
                    return ""
        json_data = response.meta.get("article_json_data")
        if json_data:
            raw_date = json_data.get("fabushijian", "")
            if raw_date:
                try:
                    return raw_date.split("T")[0] if "T" in raw_date else raw_date
                except Exception:
                    pass
        day = response.xpath("//div[@class='news-date']/p[@class='news-day']/text()").get()
        year_month = response.xpath("//div[@class='news-date']/p[@class='news-month']/text()").get()
        if day and year_month:
            match = re.match(r"(\d{4})/(\d{1,2})", year_month.strip())
            if match:
                year, month = match.groups()
                return f"{year}-{int(month):02d}-{int(day):02d}"
        return ""

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return pdf_url if pdf_url and pdf_url != "None" else []

    def get_next_page(self, response) -> Union[None, int]:
        current_page = response.meta.get("current_page", 1)
        try:
            data = json.loads(response.text)
        except Exception:
            return None
        total_count = data.get("Count", 0)
        page_size = int(self.api_start_url[response.meta['start_url']]['payload']['pagecount'])
        total_pages = (total_count + page_size - 1) // page_size
        if current_page < total_pages:
            return current_page + 1
        return None

    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page=1):
        next_page = self.get_next_page(response)
        if next_page:
            api_data = self.api_start_url[start_url]
            api_url = api_data["url"]
            payload = api_data["payload"]
            payload["pageid"] = str(next_page)          
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                formdata=payload,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                },
                callback=self.parse,
                meta={
                    "start_url": start_url, 
                    "current_page": next_page
                },
                dont_filter=True
            )