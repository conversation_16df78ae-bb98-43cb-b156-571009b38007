from scraper.OCSpider import <PERSON><PERSON>pider
from datetime import datetime
from typing import Optional
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
import json
from w3lib.html import remove_tags
import re

class BursaMalaysia(OCSpider):
    name = "BursaMalaysia"

    
    
    start_urls_names = {
        "https://www.bursamalaysia.com/about_bursa/media_centre/media_releases?year=all&subject=&page=1 ": "Media Releases",
         "https://www.bursamalaysia.com/regulation/public_consultation": "Public Consultation",
        "https://www.bursamalaysia.com/regulation/lfx/directives_circulars": "Directives / Circulars",
        "https://www.bursamalaysia.com/regulation/communication_notes_guides":"Communication Notes/Guides",
         "https://www.bursamalaysia.com/market_information/announcements/company_announcement": "Company Announcements",
        "https://www.bursamalaysia.com/market_information/announcements/structure_warrants": "Structure Warrants",
        "https://www.bursamalaysia.com/listing/listing_resources/ipo/ipo_summary?page=1": "IPO Summary",  
        "https://www.bursamalaysia.com/market_information/announcements/company_announcement?cat=IO&sub_type=IO3" : "Company Announcements",

    }


    api_start_url = {
        'https://www.bursamalaysia.com/market_information/announcements/company_announcement':{
           'url' : 'https://www.bursamalaysia.com/api/v1/announcements/search?ann_type=company&company=&keyword=&dt_ht=&dt_lt=&cat=&sub_type=&mkt=&sec=&subsec=&per_page=20&page=1&_=1751257782079',
           'payload' : {
                'ann_type': 'company',
                'company': '',
                'keyword': '',
                'dt_ht': '',
                'dt_lt': '',
                'cat': '',
                'sub_type': '',
                'mkt': '',
                'sec': '',
                'subsec': '',
                'per_page': '20',
                'page': '1',
                '_': '1751257782079'
            }

        },
            'https://www.bursamalaysia.com/market_information/announcements/structure_warrants':{
            'url' : 'https://www.bursamalaysia.com/api/v1/announcements/search?ann_type=warrant&company=&keyword=&dt_ht=&dt_lt=&cat=&sub_type=&sec=&subsec=&per_page=20&page=1&_=1751258687848',
            'payload' : {
                    'ann_type': 'warrant',
                    'company': '',
                    'keyword': '',
                    'dt_ht': '',
                    'dt_lt': '',
                    'cat': '',
                    'sub_type': '',
                    'mkt': '',
                    'sec': '',
                    'subsec': '',
                    'per_page': '20',
                    'page': '1',
                    '_': '1751258687848'
            }
        },
        'https://www.bursamalaysia.com/listing/listing_resources/ipo/ipo_summary?page=1':{
            'url' : 'https://www.bursamalaysia.com/listing/listing_resources/ipo/ipo_summary?page=1',
            'payload' : {
                'page': '1'
            }
        },
        'https://www.bursamalaysia.com/market_information/announcements/company_announcement?cat=IO&sub_type=IO3':{
            'url' : 'https://www.bursamalaysia.com/api/v1/announcements/search?ann_type=company&company=&keyword=&dt_ht=&dt_lt=&cat=&sub_type=&mkt=&sec=&subsec=&per_page=2',
            'payload' : {
                'ann_type': 'company',
                    'company': '',
                    'keyword': '',
                    'dt_ht': '',
                    'dt_lt': '',
                    'cat': '',
                    'sub_type': '',
                    'mkt': '',
                    'sec': '',
                    'subsec': '',

                    'per_page': '2',
                    'page': '1',
                    '_': '1751260184138'
            }
        },

    }

    article_data_map = {}

    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    HEADLESS_BROWSER_WAIT_TIME = 50000

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_date_map = {}


    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')

        # Handle static HTML sources
        if start_url not in self.api_start_url:
            self.logger.info(f"[STATIC] Processing HTML page for: {start_url}")
            articles = self.extract_articles_with_dates(response)

            if not articles:
                self.logger.warning(f"[WARNING] No articles found for static HTML page: {start_url}")
                return

            for article in articles:
                article_url = article["url"].lower()

                self.article_data_map[article_url] = {
                    "title": article["title"],
                    "date": article["date"]
                }

                self.crawler.stats.inc_value("count_summary__articles_crawled")

                # ✅ Skip problematic file types (don't crawl, but store metadata)
                if article_url.endswith((".doc", ".docx", ".xls", ".xlsx")):
                    self.logger.info(f"[SKIP] Not requesting binary document: {article_url}")
                    continue

                yield scrapy.Request(
                    url=article_url,
                    callback=self.parse_article,
                    meta={"start_url": start_url}
                )
            return

        # Handle API-based sources
        api_data = self.api_start_url[start_url]
        api_url = api_data.get("url")
        if not api_url:
            self.logger.error(f"[ERROR] No API URL configured for {start_url}")
            return

        # Get current page or default to 1
        current_page = response.meta.get("current_page", 1)

        # Prepare payload with correct page number
        payload = api_data["payload"].copy()
        payload["page"] = str(current_page+1)

        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        self.logger.info(f"[API] Submitting request to API page {current_page} for: {start_url}")

        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            }
        )


        

    

    def get_articles(self, response) -> list:
        article_urls = []

        try:
            data = json.loads(response.text)
            records = data.get("data", [])

            if records and isinstance(records[0], list):
                count = 0
                for i, item in enumerate(records):
                    if count >= 2:
                        break  # ✅ Limit to 2

                    try:
                        if len(item) < 4:
                            continue

                        date_html = item[1]
                        match = re.search(r"<div class='d-lg-inline-block[^>]*'[^>]*>([^<]+)</div>", date_html)
                        date_text = match.group(1).strip() if match else remove_tags(date_html).strip()

                        title_html = item[3]
                        title = remove_tags(title_html).strip()
                        href_match = re.search(r"href=['\"]([^'\"]+)", title_html)
                        if not href_match:
                            self.logger.warning(f"[SKIP] No href found in JSON row #{i+1}")
                            continue

                        href = href_match.group(1)
                        full_url = response.urljoin(href)

                        self.article_data_map[full_url] = {
                            "title": title,
                            "date": date_text
                        }

                        article_urls.append({
                            "url": full_url,
                            "title": title,
                            "date": date_text
                        })
                        count += 1

                    except Exception as inner_e:
                        self.logger.warning(f"[SKIP] Error processing JSON row #{i+1}: {inner_e}")
                        continue

                return article_urls

        except json.JSONDecodeError:
            self.logger.debug("[DEBUG] Not a JSON response, falling back to HTML")

        # fallback for static HTML — also slice
        rows = response.xpath("//tbody/tr")
        for i, row in enumerate(rows[:2]):
            link = row.xpath(".//td[1]/a")
            href = link.xpath("./@href").get()
            title = link.xpath("normalize-space(string())").get()
            date_text = row.xpath("./td[3]/text()").get()

            if not (href and title and date_text):
                continue

            article_url = response.urljoin(href.strip())
            self.article_data_map[article_url] = {
                "title": title.strip(),
                "date": date_text.strip()
            }

            article_urls.append({
                "url": article_url,
                "title": title.strip(),
                "date": date_text.strip()
            })

        return article_urls








    def get_href(self, entry) -> str:
        return entry["url"]
    
    def get_title(self, response) -> str:
        url = response.url
        article_info = self.article_data_map.get(url)
        
        # Primary: use mapped title if available
        if article_info and 'title' in article_info:
            return article_info['title'].strip()

        # Fallback 1: extract from <td class="company_name">
        title = response.xpath("//td[@class='company_name']/text()").get()
        if title:
            return title.strip()

        # Fallback 2: extract from <h3> tag (if present)
        title = response.xpath("//h3/text()").get()
        if title:
            return title.strip()

        # Final fallback: empty string
        return ''



    def get_body(self, response) -> str:
        if response.url.lower().endswith(".pdf") or ".pdf" in response.url.lower():
            return ""

        for xpath in [
            "//td[@class='formContentData2']/pre/text()",
            "//td[@class='formContentData']//text()",
            "//td[@class='formContentDataH']//text()"
        ]:
            body_text_list = response.xpath(xpath).getall()
            if body_text_list:
                return "\n".join(text.strip() for text in body_text_list if text.strip())

        return ""



    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%d-%m-%Y"
    
    

    def get_date(self, response) -> Optional[str]:
        article_url = response.url
        print(f"[DEBUG] Extracting date for URL: {article_url}")

        article_info = self.article_data_map.get(article_url)
        if not article_info:
            print("[ERROR] No article info found in article_data_map")
            return None

        raw_date = article_info.get("date")
        if not raw_date or not isinstance(raw_date, str):
            print("[ERROR] Date missing or invalid in article_info")
            return None

        print(f"[DEBUG] Raw date from map: '{raw_date}'")

        # Extract first part if multiple dates present (e.g. "23 May 2023 | Updated : 29 Nov 2024")
        cleaned_date = raw_date.split('|')[0].strip()

        # Normalize full month to abbreviated (e.g., June → Jun)
        month_map = {
            "January": "Jan", "February": "Feb", "March": "Mar",
            "April": "Apr", "May": "May", "June": "Jun",
            "July": "Jul", "August": "Aug", "September": "Sep",
            "October": "Oct", "November": "Nov", "December": "Dec"
        }

        for full, abbr in month_map.items():
            cleaned_date = cleaned_date.replace(full, abbr)

        try:
            date_obj = datetime.strptime(cleaned_date, "%d %b %Y")
            formatted_date = date_obj.strftime("%d-%m-%Y")
            print(f"[DEBUG] Successfully parsed and formatted date: '{formatted_date}'")
            return formatted_date
        except ValueError as ve:
            print(f"[ERROR] Failed to parse cleaned date '{cleaned_date}': {ve}")
            return None



    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        url = response.url.lower()

        # Case 1: If the current page **is a PDF**, return it directly
        if url.endswith(".pdf"):
            return [response.url]

        # Case 2: Extract from <p class='att_download_pdf'><a href="...pdf">
        pdf_links = response.xpath("//p[@class='att_download_pdf']/a/@href").getall()

        # Case 3: Fallback to any <a> with .pdf in href
        if not pdf_links:
            pdf_links = response.xpath("//a[contains(@href, '.pdf')]/@href").getall()

        # Normalize URLs
        return [response.urljoin(link.strip()) for link in pdf_links if link.strip()]
    
    def get_next_page(self, response, current_page: Optional[int] = None) -> Optional[str]:
        # Determine current page
        if current_page is None:
            if "page=" in response.url:
                try:
                    current_page = int(response.url.split("page=")[-1])
                except ValueError:
                    self.logger.warning("Failed to extract page number from URL.")
                    return None
            else:
                current_page = 1

        # Calculate next page
        next_page = current_page + 1

        # Confirm articles exist on current page
        rows = response.xpath('//table//tbody/tr')
        if not rows:
            self.logger.info("No rows found on the current page; stopping pagination.")
            return None

        # Construct next page URL
        next_page_url = (
            f"https://www.bursamalaysia.com/about_bursa/media_centre/media_releases?year=all&subject=&page={next_page}"
        )
        return next_page_url


    def get_page_flag(self) -> bool:
        return True
    
    MAX_PAGES = 3

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        if current_page >= self.MAX_PAGES:
            self.logger.info(f"[PAGINATION] Reached max page limit {self.MAX_PAGES}; stopping.")
            return
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            self.logger.warning(f"[PAGINATION] No API config for: {start_url}")
            return

        next_page = current_page + 1
        payload = api_data["payload"].copy()
        payload["page"] = str(next_page)

        api_url = api_data["url"]

        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        self.logger.info(f"[PAGINATION] Going to page {next_page} for {start_url}")

        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            }
        )

    
    

    def extract_articles_with_dates(self, response):
        articles = []

        # --- CASE 1: MEDIA RELEASES TABLE ---
        rows = response.xpath(
            '//table[contains(@class, "media-releases-table") and contains(@class, "large-only")]//tr'
        )
        for row in rows:
            date_text = row.xpath('./td[@class="text-center"]/text()').get()
            title = row.xpath('./td[@class="text-left"]/a/text()').get()
            href = row.xpath('./td[@class="text-left"]/a/@href').get()

            if not (date_text and title and href and '.pdf' in href.lower()):
                continue

            articles.append({
                "date": date_text.strip(),
                "title": title.strip(),
                "url": response.urljoin(href.strip())
            })

        # --- CASE 2: CONSULTATION PAPER TABLE (class="2025") ---
        consultation = response.xpath('//table[@class="2025"]')
        if consultation:
            date_issued = consultation.xpath('.//p[contains(text(), "Date Issued:")]/strong/text()').get()
            main_title = consultation.xpath('.//h5/b/text()').get()

            main_doc_url = consultation.xpath('.//p[contains(@class, "bm_download")]/a/@href').get()
            main_doc_title = consultation.xpath('.//p[contains(@class, "bm_download")]/a/text()').get()

            if main_doc_url and date_issued:
                articles.append({
                    "date": date_issued.strip(),
                    "title": (main_doc_title or main_title).strip(),
                    "url": response.urljoin(main_doc_url.strip())
                })

            for li in consultation.xpath('.//ul/li'):
                doc_url = li.xpath('.//a/@href').get()
                doc_title = li.xpath('.//a/text()').get()
                if doc_url and doc_title:
                    articles.append({
                        "date": date_issued.strip(),
                        "title": doc_title.strip(),
                        "url": response.urljoin(doc_url.strip())
                    })

        # --- CASE 3: GENERIC 3-COLUMN TABLE (DATE | REF | LINKS) ---
        generic_rows = response.xpath('//tbody/tr')
        for row in generic_rows:
            # Extract date from known div (desktop) format
            date_text = row.xpath("./td[2]/div[@class='d-lg-inline-block d-none']/text()").get()

            # Fallback: Generic first column (less specific tables)
            if not date_text:
                date_text = row.xpath('./td[1]/text()').get()

            # Try primary announcement link
            link_tag = row.xpath("./td[4]/a")
            title = link_tag.xpath("text()").get()
            href = link_tag.xpath("@href").get()

            if date_text and title and href:
                try:
                    datetime.strptime(date_text.strip(), "%d %b %Y")
                except ValueError:
                    print(f"[WARNING] Skipping row with invalid date: '{date_text.strip()}'")
                    continue

                articles.append({
                    "date": date_text.strip(),
                    "title": title.strip(),
                    "url": response.urljoin(href.strip())
                })
                continue

            # Extra links in 3rd column case
            links = row.xpath('./td[3]//a')
            for link in links:
                url = link.xpath('./@href').get()
                link_title = link.xpath('./text()').get()

                if url and link_title and date_text:
                    articles.append({
                        "date": date_text.strip(),
                        "title": link_title.strip(),
                        "url": response.urljoin(url.strip())
                    })

        # --- CASE 4: ICN NOTES TABLES (class="cardTable") ---
        icn_tables = response.xpath('//table[contains(@class, "cardTable")]')
        for table in icn_tables:
            link_elem = table.xpath('.//a')
            url = link_elem.xpath('./@href').get()
            title = link_elem.xpath('./text()').get()

            description = table.xpath('.//td[contains(text(), "Communication Notes")]/following-sibling::td[1]/text()').get()
            date_block = table.xpath('.//td[contains(text(), "Issuance Date")]/following-sibling::td[1]//text()').getall()
            date = ' | '.join([d.strip() for d in date_block if d.strip()])

            if title and url and date:
                articles.append({
                    "date": date,
                    "title": title.strip(),
                    "url": response.urljoin(url.strip()),
                    "description": description.strip() if description else ""
                })

        return articles
