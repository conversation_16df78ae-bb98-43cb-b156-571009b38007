from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class PahangStateGovernmentNews(OCSpider):
    name = "PahangStateGovernmentNews"
    
    start_urls_names = {
        "https://www.pahang.gov.my/index.php/announcements/all/4?q=&date=&d=&month=&year=2025&items=100&page=1": "News" # pagination is not supported
    }
    
    start_urls_with_no_pagination_set = {
        "https://www.pahang.gov.my/index.php/announcements/all/4?q=&date=&d=&month=&year=2025&items=100&page=1"
    }

    start_urls_with_no_pagination_set = {
        "https://www.pahang.gov.my/index.php/announcements/all/4?q=&date=&d=&month=&year=2025&items=100&page=1"
    }

    charset = "iso-8859-1"

    country = "Malaysia"
       
    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
   
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='announcement-article-header']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='is-title post-title']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("(//div[@class='post-content cf entry-content content-spacious']//p)[position() >= 2 and position() <= 12]").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='col-8 main-content s-post-contain']//img//@src").getall()
        
    def date_format(self) -> str:
        return "%d %B, %Y"
    
    def get_date(self, response):
        return response.xpath("//div[@class='post-meta post-meta-a post-meta-left post-meta-single has-below']//time//text()").get()
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_authors(self, response):
        full_text = response.xpath("((//div[@class='post-content cf entry-content content-spacious']//p)[1]//text())[1]").get().strip()
        author = full_text.split(": ", 1)[1]    
        return [author] 
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) -> Optional[str]:
        return None