from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class WestVirginiaDepartmentOfEconomicDevelopment(OCSpider):
    name = 'WestVirginiaDepartmentOfEconomicDevelopment'
    
    country = "US"

    start_urls_names = {
        'https://westvirginia.gov/news/': 'News'
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "list-div"]//a[@class= "more-link"]/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class="entry-title"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "entry-content"]/p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return  response.xpath('//span[@class= "posted-on"]/a/time/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="nav-next"]/a/@href').get()
        if next_page:
            return response.urljoin(next_page) 
        else:
            return None