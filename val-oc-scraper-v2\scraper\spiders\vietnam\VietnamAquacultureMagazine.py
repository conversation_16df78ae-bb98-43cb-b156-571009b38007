from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import re

class VietnamAquacultureMagazine(OCSpider):
    name = "VietnamAquacultureMagazine"

    start_urls_names = {
        'https://thuysanvietnam.com.vn/tin-tuc-su-kien/nghe-ca-trong-nuoc/': 'Magazine'
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="column"]//div[@class="news-info"]//h4//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="news-detail-title page-title"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('(//div[@class="news-detail-content"] | //div[@class="news-detail-info"])//p//text()').getall())

    def get_date(self, response) -> str:
        date = response.xpath('//p[@class="news-detail-post-time"]//text()').get()
        match = re.search(r"\d{2}/\d{2}/\d{4}", date)
        return match.group(0) if match else ""

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="news-detail-content"]//p//img/@src').getall()  

    def get_document_urls(self, response, entry=None) -> list[str]:
        return []      

    def get_authors(self, response) -> str :
        return response.xpath('//div[@class="news-detail-content"]//p[last()-1]//text()').get()

    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response):
        return response.xpath('//li//a[@class="next page-numbers"]/@href').get()