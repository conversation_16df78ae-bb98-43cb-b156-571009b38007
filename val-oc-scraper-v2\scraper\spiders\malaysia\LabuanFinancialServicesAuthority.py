from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin, unquote
import re

class LabuanFinancialServicesAuthority(OCSpider):
    name = "LabuanFinancialServicesAuthority"

    start_urls_names = {
        ##3rd no href 4th 5th 6th 8th urls can't be scraped
        "https://www.labuanfsa.gov.my/general-info/media" : "media",
        "https://www.labuanfsa.gov.my/general-info/investor-alerts" : "investor alerts",
        # "https://www.labuanfsa.gov.my/about-labuan-fsa/the-regulator/memoranda-of-understanding" : "memorandas between the Labuan area and external organizations", #No child article urls links are there to scrape
        # "https://www.labuanfsa.gov.my/legislation-guidelines/legislation/legislation" : "legislation", #No date present for child article urls
        # "https://www.labuanfsa.gov.my/legislation-guidelines/public-consultation-papers/consultation-paper" : "public consultation paper", ##No date present for child article urls
        # "https://www.labuanfsa.gov.my/legislation-guidelines/public-consultation-papers/exposure-draft" : "exposure draft", ##No date present for child article urls
        "https://www.labuanfsa.gov.my/general-info/general-notification" : "general notification",
        "https://www.labuanfsa.gov.my/amlcft/guidelines-directives-circulars" : "guidelines, directives, circulars"
    }

    start_urls_with_no_pagination_set = {
        "https://www.labuanfsa.gov.my/general-info/media",
        "https://www.labuanfsa.gov.my/general-info/investor-alerts",
        "https://www.labuanfsa.gov.my/general-info/general-notification",
        "https://www.labuanfsa.gov.my/amlcft/guidelines-directives-circulars"
    }   

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_mapping = {}

    article_to_pdf_mapping = {}

    malay_to_eng_month = {
        "Januari": "Jan",
        "Februari": "Feb",
        "Mac": "Mar",
        "April": "Apr",
        "Mei": "May",
        "Jun": "Jun",
        "Julai": "Jul",
        "Ogos": "Aug",
        "September": "Sep",
        "Oktober": "Oct",
        "November": "Nov",
        "Disember": "Dec"
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def get_articles(self, response) -> list: 
        start_url = response.meta.get('start_url')
        press_release_data = {}
        base_url = "https://www.labuanfsa.gov.my/"
        articles = [] 
        if "general-info/general-notification" in start_url:
            containers = response.xpath('//div[contains(@class, "col-cccb4876")]')
            for box in containers:
                link = box.xpath('.//div[contains(@class, "com-ad7088f2")]//a/@href').get()
                title = box.xpath('.//div[contains(@class, "com-ad7088f2")]//div[@class="a-inner-text"]/text()').get()
                date = box.xpath('.//div[contains(@class, "aps-0036-so-wrapper") and contains(@class, "822FA4B9")]//div[@class="a-inner-text"]/text()').get()
                if link:
                    normalized_link = unquote(link).rstrip('/')
                    articles.append(normalized_link)
                    press_release_data[normalized_link] = {
                        'title': title,
                        'pdf': link,    # Keeping relative path
                        'date': date
                    }
    
        for entry in response.xpath('//div[contains(@class, "col-a6105836-b393-4aaa-bb9f-a800b3d511e9")]') or response.xpath('//div[contains(@class, "col-3cabb002-0480-40f8-a8a1-f064f6e52d55")]'):
            url = entry.xpath('.//a/@href').get()
            date = entry.xpath('.//div[contains(@class,"aps-0036-so-wrapper")]//div[@class="a-inner-text"]/text()').get() or entry.xpath('./following-sibling::div[1]//div[@class="a-inner-text"]/text()').get()
            if url and date:
                full_url = urljoin(base_url, url)
                normalized_url = unquote(full_url).rstrip('/')
                press_release_data[normalized_url] = date
                articles.append(normalized_url)
        self.article_to_date_mapping.update(press_release_data)
        self.article_to_pdf_mapping.update(press_release_data)
        print(press_release_data)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title=None
        if 'text/html' in response.headers.get('Content-Type', b'').decode():
        # Try extracting from HTML
            title = response.xpath('//div[contains(@class, "aps-0056-so-wrapper") and contains(@class, "font-2A1BB515-8744-4E04-AD64-A19152A30FB4")]//text()'
            ).get()
        # Fallback to article_to_pdf_mapping
        if not title:
            title = self.article_to_pdf_mapping.get(response.url, {}).get("title", "")
        return title
    
    def get_body(self, response) -> str:
        if 'text/html' not in response.headers.get('Content-Type', b'').decode():
            return ''
        else :
            return body_normalization(response.xpath('//div[@class="aps-0036-so-wrapper  list-gap-s font-D6715655-7F24-45EF-8463-ADB1866B5F1D  aps-0036-so-wrapper-d2248b7f-217f-4694-8d2e-22205ac89ab3"]//div/text()').getall() or response.xpath('//div[@class="col col-ca5cefd0-c97f-4e16-833d-b066f8cabb93"]//text()').getall())

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response) -> str:
        start_url = response.meta.get('start_url', '')
        content_type = response.headers.get('Content-Type', b'').decode()
        date = None
        normalized_url = unquote(response.url).rstrip('/')
        # If it's a PDF and from general-notification
        if 'text/html' not in content_type:
            if "general-info/general-notification" in start_url: 
                date = self.article_to_pdf_mapping.get(normalized_url, {}).get("date")
        # If it's HTML from media or investor alerts
        elif "/general-info/media" in start_url or "/general-info/investor-alerts" in start_url:
            normalized_url = unquote(response.url).rstrip('/')
            date = self.article_to_date_mapping.get(normalized_url)
        return date.strip() if date else ""

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        start_url = response.meta.get('start_url', '')
        content_type = response.headers.get('Content-Type', b'').decode()
        normalized_url = unquote(response.url).rstrip('/')
        pdf=[]
        if 'text/html' not in content_type:
            if "general-info/general-notification" in start_url: 
                pdf = self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
                return pdf
        else :
            return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None