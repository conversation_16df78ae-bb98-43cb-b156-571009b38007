from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re

class GeneralDepartmentofTaxationVietnam(OCSpider):
    name = "GeneralDepartmentofTaxationVietnam"

    start_urls_names = {
        "https://www.gdt.gov.vn/wps/portal/!ut/p/z1/tVPBcoIwEP2VXnpkshEk4aiOo1GsigUlFyYVUNoSsGTU_n2DM_aG2HGay2Yzu_teXl4QRxvEpThmO6GyQopPnYfcjsBymEuD1bw_ZASYP_I8NnFxx8FofSkYjHpji7gA1BoBMKs_fxkPlhiYifg9_dCwetDWHyCO-FaqUu1RuIvV07aQKpHqGSoR6fwSZXKqfjeREue6qdxmMQohSQlNHWzEJCGGlQowhGMTwzZjrE_Im2PGV5LNLPjtO6xrvFqm5XQQTAMt02KoxZktiW0T3KGsey24oWMbSKhJkkaSCxOtj1lyQr4svnL9sqs_ajBuQ-jCgwgt4zv_Ox4_OH7S5lT9lbL3w4H3tF9rj54V2txv2DL3_Zya38aHR0-v6X6XR7Oh2a1D9QOrENrI/dz/d5/L2dBISEvZ0FBIS9nQSEh/": "Tax news",
        "https://www.gdt.gov.vn/wps/portal/!ut/p/z1/tVNNc4IwEP0rvfTIJCTBwBEZR-JHFSwouTCUD6UtQUtG6r9vcMbelDpOc9nszO6-ty8vgIMN4CI5lttElrVIPlUe8UEMicVmZrhaDEeMQhaMfZ9NZjqydLA-Fzhj2yV0BqFJxhAyMly8uI6nQ4YB_0s_vHJs2NcfAg54KuRe7kC0zeRTWguZC_kMmyRW-TmKvG1-L3GuSurq1DXu0zIDESowspBpaAiRQiNZqmtJgQ0tQTilOX1DKS0uRK8z4bf3WHd4nVTe1AmnoZJqOVICzT06GFAdmcy4FNzQsg8kUiTpVZJLDNbHMm9BIOqvSr3u6k4N3D4EAz6I0DMe_e94_cHxkz63qu9Uvh8O3Fae7Xz6LcHmPtPuqyCoTHzSPnyzfS122yqej7DRheYHlHcOpA!!/dz/d5/L2dBISEvZ0FBIS9nQSEh/": "Economic information",
        "https://www.gdt.gov.vn/wps/portal/!ut/p/z1/tVNNc4IwEP0rvfTIZCFA4KiOg1GsXwUlFwYIBdoSsGa0_fcNznhU7DjNZbMzu_vevrwghnaIieRYFYmsGpF8qjxidgymS30n3CyGY0qABt56Tae-brg62p4LRt5gYhIfwDE9AGoOFy-T0UoHihG7px-unAH09YeIIZYJ2coSRQWXT1kjZC7kMxySWOXnKPLT4XyRZSOKNGm6pjarOIpwDi7h3NZIbmPNxNzSUpODhpPMsHlmWWnqXEheZ8Fu77Dt8DqZVrNROAuVTMuxEme-IrZNdMOh1qXgho59IJEiSa6SXGK0PVb5CQWi-arVy27-qMGkD8GCBxF6xhv_O15_cPy0z6nqK1Xv-z0bKL92Hv2WaHe_Yds6CGoH_2gfa-f0-lYWdTwfY6sLh197DaW4/dz/d5/L2dBISEvZ0FBIS9nQSEh/": "Notifications"
    }

    start_urls_with_no_pagination_set = {}

    country = "Vietnam"

    charset = "utf-8"

    


    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                #Using Geoproxy
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:  
        relative_urls = response.xpath('//div[@class="list_news"]//span[@class="newtitle"]/a/@href').getall()
        return [response.urljoin(url) for url in relative_urls]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="news"]//h3[@id="title"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@id='contentBody']//p//text() | //div[@id='contentBody']//em//text()").getall())

    def get_images(self, response) -> list:
        return [response.urljoin(url) for url in response.xpath('//div[@id="contentBody"]//img/@src').getall()]

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        date_text = response.xpath('substring-after(normalize-space(//span[@class="datespan"]/text()), "Ngày ")').get()
        return date_text.strip() if date_text else ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        next_url = response.xpath('//div[@class="page"]//a[@title="Link to next page"]/@href').get()
        return response.urljoin(next_url) if next_url else None
