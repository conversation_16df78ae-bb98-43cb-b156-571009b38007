from scraper.OCSpider import OCSpider 
from urllib.parse import urljoin
import re

class MalaccaCustomaryLandsDevelopmentCorporationStrategicPlan(OCSpider):
    name = "MalaccaCustomaryLandsDevelopmentCorporationStrategicPlan"

    start_urls_names = {
        'https://ptg.melaka.gov.my/en/strategic-plans': 'publications'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://ptg.melaka.gov.my/en/strategic-plans"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list =[]
        articles = response.xpath('//div[@class="container"]//section[@class="page-content"]')
        print(articles)
        for article in articles:
            title = article.xpath('.//p//text()').get()
            url = article.xpath('.//p//a/@href').get()
            date_data = article.xpath('.//p//a/@href').get()
            date = re.findall(r'\d{4}', date_data)
            img = article.xpath('.//img/@src').get()
            if date and url:
                base_url ="https://ptg.melaka.gov.my"
                full_url = urljoin(base_url , url)
                full_img_url = urljoin(base_url , img)
                child_article_list.append(full_url)
                self.article_url_title_date_mapping[full_url]=[title , date[len(date)-1] , full_img_url]
        return child_article_list
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get((response.url),[])[0]

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get((response.url),[])[1]

    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
        return [self.article_url_title_date_mapping.get((response.url),[])[2]]
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None