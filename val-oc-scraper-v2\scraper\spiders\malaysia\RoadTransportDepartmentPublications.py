from scraper.OCSpider import <PERSON><PERSON>pider 
from urllib.parse import urljoin ,  unquote
import re
from scraper.utils.helper import body_normalization

class RoadTransportDepartmentPublications(OCSpider):
    name = "RoadTransportDepartmentPublications"

    start_urls_names = {
        'https://www.jpj.gov.my/en/publishing/': 'Publication'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.jpj.gov.my/en/publishing/"
    }

    charset = "utf-8"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list = []
        articles = response.xpath('//div[@data-element_type="container"]')
        for article in articles:
            title = article.xpath('.//p[@class="header-title"]//text()').get()
            url =article.xpath('.//button[@class="btn"]//a/@href').get()
            if title:
                date_data = re.search(r'\b\d{4}\b' , title)
                if date_data:
                    full_url = urljoin("https://www.jpj.gov.my" , url)
                    child_article_list.append(full_url)
                    self.article_url_title_date_mapping[full_url] = [ title , date_data.group(0)]
        child_article_list = list(set(child_article_list))
        return child_article_list
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url))[0]

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url))[1]

    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None