import re
from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
class AECCPowerCoLtd(OCSpider):
    name = "AECCPowerCoLtd"

    start_urls_names = {
        "http://hfdl.aecc.cn/gsdt.htm": "航发动力",
        "http://hfdl.aecc.cn/xxpl.htm": "航发动力",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath("//table[@class='winstyle1103']//a[@class='c1103']//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h1//text()').get().strip()
    
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath("//div[@class='v_news_content']//p//text()").getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        dates = response.xpath("//div[@class='zwwwzxx']//text()").get()
        match = re.match(r'发布时间：(\d{4})年(\d{2})月(\d{2})日', dates)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month}-{day}"
        else:
            return None

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf=response.xpath("//ul//li//a//@href").get()
        if pdf:
            return f'http://hfdl.aecc.cn/{pdf}'
        
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath("//div[@class='v_news_content']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page = response.xpath("//a[@class='Next']//@href").get()
        if next_page :
            return next_page
        else:
            return None    