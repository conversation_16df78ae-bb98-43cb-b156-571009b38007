from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MinistryOfFinancePressClippings(OCSpider):
    name = "MinistryOfFinancePressClippings"

    start_urls_names = {
        "https://mof.gov.my/portal/ms/berita/akhbar": "News"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
     
    def get_articles(self, response) -> list:  
        return response.xpath('//*[@id="sp-component"]/div/div[2]/div[3]/ul/li/div[2]/a//@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//*[@id="sp-component"]/div/div[2]/div[1]/h1/text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//*[@id="sp-component"]/div/div[2]/div[6]/p/text()').getall())

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//*[@id="sp-component"]/div/div[2]/div[3]/div[1]/span[2]/time/text()').get()
        parsed_date = dateparser.parse(date, languages=['en'])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None 