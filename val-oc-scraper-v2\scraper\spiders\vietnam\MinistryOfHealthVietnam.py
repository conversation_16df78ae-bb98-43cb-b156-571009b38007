from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class MinistryOfHealthVietnam(OCSpider):
    name = "MinistryOfHealthVietnam"

    start_urls_names = {
        "https://moh.gov.vn/web/guest/tin-noi-bat": "FEATURED NEWS"
    }

    start_urls_with_no_pagination_set = {
        "https://moh.gov.vn/web/guest/tin-noi-bat"
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:  
        return response.xpath('//h3[@class="asset-title"]/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return  response.xpath('//h3[@class="text-change-size"]//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath("//div[@class='journal-content-article']//h2[not(@align='center')]//text()").getall())

    def get_images(self, response) -> list:  
        return response.xpath('//div[@class="journal-content-article"]//img/@src').getall()

    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> str:
        date_text = response.xpath(
            'normalize-space(//p[@class="time-post text-change-size"])').get()   
        date_part = date_text.strip().split(' | ')[0]
        for fmt in ("%m/%d/%Y", "%d/%m/%Y"):
            try:
                parsed = datetime.strptime(date_part, fmt)
                return parsed.strftime(self.date_format())
            except ValueError:
                continue
        return ""

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None