from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class MassachusettsDepartmentOfTransportation(OCSpider):
    name = 'MassachusettsDepartmentOfTransportation'
    
    country = "US"

    start_urls_names = {
        'https://www.mass.gov/orgs/massachusetts-department-of-transportation/news': 'News'
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 10000  # 10 Seconds wait time
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//h2[@class= "ma__press-teaser__title"]/span/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title_parts =  response.xpath('//h1[@class="ma__page-header__title"]//text()').getall()
        title = " ".join(part.strip() for part in title_parts if part.strip())
        return title
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "ma__rich-text "]/p//text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> str:
        date_str = response.xpath('//div[@class= "ma__press-status__content"]//div/text()').get()
        date_str = date_str.strip()
        return date_str
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return response.xpath("//div[@class = 'ma__pagination']//a/@href").get()       