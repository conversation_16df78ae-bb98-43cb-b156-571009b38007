from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class ParliamentofSekadauRegency(OCSpider):
    name = "ParliamentofSekadauRegency"
    
    country = "ID"

    start_urls_names = {
        "https://dprd.sekadaukab.go.id/berita": "News" 
    }
    
    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"
        
    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='mb-10 wow fadeInUp group']//h3//a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='lg:col-span-2 xs:col-span-full']//h1//text()").get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="prose max-w-none mt-10 my-10 text-justify"]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='items-center mb-4']//img/@src").getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        date_text = response.xpath("//div[@class='flex flex-row lg:ml-3 mr-3 align-middle']//span[@class='text-gray-700 text-xs lg:text-md lg:border-r-2 lg:border-green-500 lg:pr-4']//text()").get()
        if date_text:
            parsed_date = dateparser.parse(date_text.strip(), languages=['id'])
            if parsed_date:
                return parsed_date.strftime(self.date_format())
    
    def get_authors(self, response):
        return response.xpath("//div[@class='flex flex-col align-middle lg:flex-row items-start mb-6 font-bold uppercase text-danger dark:text-danger-500 space-y-2 lg:space-y-0 lg:space-x-3']//a//text()").getall()
    
    def get_page_flag(self) -> bool:
        return False 
    
    def get_next_page(self, response): 
        # No next page to scrape
        return None