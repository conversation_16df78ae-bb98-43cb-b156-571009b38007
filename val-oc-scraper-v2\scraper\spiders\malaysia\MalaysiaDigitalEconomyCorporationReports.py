
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class MalaysiaDigitalEconomyCorporationReports(OCSpider):
    name = "MalaysiaDigitalEconomyCorporationReports"

    start_urls_names = {
        "https://mdec.my/publications/reports": "News"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:
       
       articles= []
       for article in response.xpath('//div[@id="publication"]//div[@class="grid grid-cols-5 px-4 sm:my-4 my-8"]'):
           img = article.xpath(".//img//@src").get()
           date = article.xpath('.//p[@class="pl-2 text-sm"]//text()').get()
           link = article.xpath('.//a//@href').get()
           title = article.xpath('.//p[@class="font-bold md:line-clamp-3 line-clamp-2"]//text()').get()
           body = article.xpath('.//p[@class="md:py-4 text-xs max-h-20 overflow-hidden line-clamp-4"]//text()').get()
           if link:
               articles.append(link)
               self.article_data_map[link]={
                    "title":title,"link":link,"date":date,"img":img,"body":body
                }
       return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("img", "")]
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date_obj = datetime.strptime(date, "%d.%m.%Y")
        return date_obj.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 