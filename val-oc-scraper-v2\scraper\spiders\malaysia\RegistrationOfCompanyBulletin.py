from scraper.OCSpider import OCSpider
import re

class RegistrationOfCompanyBulletin(OCSpider):
    name = "RegistrationOfCompanyBulletin"

    country = "Malaysia"

    start_urls_names = {
    "https://www.ssm.com.my/Pages/Publication/Bulletin/Bulletin.aspx": "Bulletin"
}
    
    start_urls_with_no_pagination_set = {
        "https://www.ssm.com.my/Pages/Publication/Bulletin/Bulletin.aspx"
    }

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self): 
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//td[.//*[contains(@class, "ms-rteForeColor-9")]]')  
        for row in rows:
            raw_title = row.xpath('.//*[self::span or self::strong][contains(@class, "ms-rteForeColor-9") and not(contains(translate(., "DOWNLOAD", "download"), "download")) and normalize-space()]//text()').getall()
            title = ''.join(raw_title).translate(str.maketrans('', '', '\u200b\u200c\u200d\u00a0')).strip()
            pdf_link = row.xpath('.//p[2]//a/@href').get()
            image_src = row.xpath('.//p[1]//img/@src').get()
            date_match = re.search(r"\b(20\d{2}|19\d{2})\b", title)
            date = date_match.group(1) if date_match else None
            if title and pdf_link and image_src and date:
                pdf_link = response.urljoin(pdf_link.strip())
                image_link = response.urljoin(image_src.strip())
                self.article_title_pdf_mapping[pdf_link] = {"title": title, "pdf_link": pdf_link, "date": date, "image_link": image_link}
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        date = self.article_title_pdf_mapping.get(response.url, {})
        return date.get("date", "")

    def get_images(self, response) -> list:
        return [self.article_title_pdf_mapping.get(response.url, {}).get("image_link", "")]

    def get_document_urls(self, response, entry=None) -> list: 
        return [self.article_title_pdf_mapping.get(response.url, {}).get("pdf_link", "")]

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return None