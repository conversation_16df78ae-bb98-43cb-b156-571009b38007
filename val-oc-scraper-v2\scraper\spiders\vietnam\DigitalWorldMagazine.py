from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class DigitalWorldMagazine(OCSpider):
    name = "DigitalWorldMagazine"

    start_urls_names = {
        'https://tgs.vn/chuyen-doi-so/' : 'Digital WorldMagazine',
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="art-big-detail"]//a[1] | //div[contains(@class,"post-details")]//a /@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="font-Bold text-black"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="editor"]//p//text()').getall())

    def get_date(self, response) -> str:
        return response.xpath('(//span[@class="timeago"])[1]//text()').get()

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('(//div[@class="cover-background"] | //figure)//img/@src').getall()     

    def get_document_urls(self, response, entry=None) -> list[str]:
        return [] 

    def get_authors(self, response) -> str :
        return response.xpath('(//span[@class="padding-5px-left"])[1]//text()').get()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[@class="next page-numbers"]/@href').get()
