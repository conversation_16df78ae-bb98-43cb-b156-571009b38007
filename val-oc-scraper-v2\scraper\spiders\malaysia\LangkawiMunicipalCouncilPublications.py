from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import unquote
import re

class LangkawiMunicipalCouncilPublications(OCSpider):
    name = "LangkawiMunicipalCouncilPublications"

    start_urls_names = {
        'https://pbt.kedah.gov.my/index.php/penerbitan-mplbp/' : 'Penerbitan'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://pbt.kedah.gov.my/index.php/penerbitan-mplbp/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
        hbp.get_proxy(response.url,timeout=20000),
        callback=self.parse, 
        dont_filter=True, 
        meta ={
            "start_url" : response.url
        })

    def get_articles(self, response):
        articles = []
        mapping = {}
        for section in response.xpath('//div[starts-with(@id, "elementor-tab-content-")]'):
            for art in section.xpath('.//ol//a'):
                url = art.xpath('./@href').get()
                title = art.xpath('normalize-space(string())').get()
                date = None
                if title:
                    date_part = re.search(r'\d{4}', title)
                    if date_part:
                        date = date_part.group(0)
                if url and title and date:
                    mapping[url] = {
                        "title": title.strip(),
                        "date": date.strip(),
                        "pdf": url
                    }
                    articles.append(url)
            for row in section.xpath('.//table//tr'):
                a_tag = row.xpath('.//a')
                url = a_tag.xpath('./@href').get()
                title = a_tag.xpath('normalize-space(string())').get()
                date = None
                if title:
                    date_part = re.search(r'\d{4}', title)
                    if date_part:
                        date = date_part.group(0)
                if url and title and date:
                    mapping[url] = {
                        "title": title.strip(),
                        "date": date.strip(),
                        "pdf": url
                    }
                    articles.append(url)
        self.article_to_date_map.update(mapping)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('title')

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('date')
       
    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('pdf')

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None 