from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
from datetime import datetime

class ParliamentOfTasikmalayaRegency(OCSpider):
    name = "ParliamentOfTasikmalayaRegency"

    start_urls_names = {
        # "https://dprd.tasikmalayakab.go.id/jdih/" : "JDIH",  # 404 error is coming
        "https://jdih.dprd-tasikmalayakota.go.id/pencarian?tahun_terbit={current_year}" : "JDIH"
        # "https://dprd.tasikmalayakab.go.id/beranda/berita/" : "Berita",  # 404 error is coming
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        current_year = response.meta.get('current_year', datetime.now().year - 2) # articles from year 2024 to 2020 only
        url =start_url.format(current_year=current_year)
        yield scrapy.Request(
            url=url,
            method='GET',
            headers={'Content-Type': 'application/json'},
            callback=self.parse,
            dont_filter=True, 
            meta={
                'start_url': start_url,
                'current_year': current_year}
        )

    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Sepetember": "September",  # <- typo handled here
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }
    
    def get_articles(self, response) -> list:  
        return response.xpath('//ul[@class="painting"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="row"]/h3/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="row jarakAtas"]//p//text()').getall() or response.xpath('//div[@class="row jarakAtas"]//table//text()).getall())'))
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        raw_date = response.xpath('(//div[@class="row jarakAtas"][1])[2]//label//text()').get()
        raw_date = raw_date.lower().title()
        for indo, eng in self.indonesian_to_english.items():
                raw_date = raw_date.replace(indo, eng)
        return raw_date
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
       current_year=response.meta.get("current_year")
       next_year = str(int(current_year) - 1)
       if response.status ==200:
           return next_year
       return None
    
    def go_to_next_page(self, response,start_url,current_page=None):
        start_url = response.meta.get('start_url')
        next_year = self.get_next_page(response)
        if next_year:
            url = start_url.format(current_year=next_year)
            yield scrapy.Request(
                url=url,
                method='GET',
                headers={'Content-Type': 'application/json'},
                callback=self.parse,
                dont_filter=True, 
                meta={
                    'start_url': start_url,
                    'current_year': next_year}
            )