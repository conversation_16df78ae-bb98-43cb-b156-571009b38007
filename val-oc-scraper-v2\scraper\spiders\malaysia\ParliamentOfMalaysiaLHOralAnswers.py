from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import dateparser

class ParliamentOfMalaysiaLHOralAnswers(OCSpider):
    name = "ParliamentOfMalaysiaLHOralAnswers"
    
    start_urls_names = {
        "https://www.parlimen.gov.my/siaran-media.html?uweb=web&": "News"
    }

    charset = "iso-8859-1"

    custom_settings = {
        "DOWNLOAD_DELAY" : 10,
    }

    def parse_intermediate(self, response):
        articles = response.xpath("//div[@class='irow editable']//tbody//a//@href").getall()
        total_articles = len(articles)
        start_url =  list(self.start_urls_names.keys())[0] 
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': start_url
                    },
                    dont_filter=True
            )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}  # Mapping date and title to child articles from start URL

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='irow editable']//tbody//tr"):
            url = article.xpath(".//td//a//@href").get()
            title = article.xpath(".//td//a//text()").get()
            date = article.xpath(".//td[@style='width:207.6px']//span//text()").get()
            if url and title and date:
                self.article_data_map[url] = {"date":date, "title" : title}
                articles.append(url)
        all_articles = list(set(articles))
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        if '.pdf' not in response.url:
            return body_normalization(response.xpath("//div[@class='WordSection1']//ol//text() | //div[@class='container']//p//text()").getall())
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response): 
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(date, languages=['ms']) 
        return parsed_date.strftime("%Y-%m-%d")

    def get_document_urls(self, response, entry=None) -> list:
        if '.pdf' in response.url:
            return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to crawl
        return None    