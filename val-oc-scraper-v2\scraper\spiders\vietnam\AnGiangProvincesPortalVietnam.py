from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class AnGiangProvincesPortalVietnam(OCSpider):
    name = "AnGiangProvincesPortalVietnam"
    
    start_urls_names = {
        "https://angiang.gov.vn/vi/tin-tuc": "Press Release",
    }

    start_urls_with_no_pagination_set = {
        "https://angiang.gov.vn/vi/tin-tuc"
    }

    charset = "utf-8"

    country = "Vietnam"
    
    @property
    def language(self): 
        return "Vietnamese"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="content-inner"]//h3[@class="post-title"]//a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@id="tieu-de"]/span/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="post-content"]//div[contains(@class, "field--name-body")]//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="node__content clearfix"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> Optional[str]:
        raw_date = response.xpath('//div[@class="post-meta category-background no-print"]//span[@class="post-created"]/text()').get()
        if raw_date:
            date_part = raw_date.split(',')[0].strip()
            return date_part
        return None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None