from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MalaysiaCompetitionCommissionMarketReview(OCSpider):
    name = "MalaysiaCompetitionCommissionMarketReview"

    start_urls_names = {
        'https://www.mycc.gov.my/market-review' : 'Market Review',
    }

    start_urls_with_no_pagination_set = {
        "https://www.mycc.gov.my/market-review"  # Pagination is not suported
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        hrefs = response.xpath('//div[@class="market-body"]//a/@href').getall()
        return [response.urljoin(href.strip()) for href in hrefs if href.strip()]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="page-header"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="NP-body"]//p/text()').getall())
    
    def date_format(self) -> str:
       return "%d %b %Y"

    def get_date(self, response) -> str:
        return response.xpath('(//div[@class="NP-date"]//text())[2]').get()

    def get_images(self, response) -> list[str]:
        img_src = response.xpath('//div[@class="NP-image"]//img/@src').get()
        return [response.urljoin(img_src)]

    def get_document_urls(self, response, entry=None) -> list:
        pdf_href = response.xpath('//div[@class="NP-pdf"]//a/@href').get()
        return [response.urljoin(pdf_href)] if pdf_href else []

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None
    