from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re
from dateutil import parser

class SecuritiesCommissionMalaysia(OCSpider):
    name = "SecuritiesCommissionMalaysia"

    year_offset = 0

    current_year = datetime.now().year
    
    start_urls_names = {
        f"https://www.sc.com.my/resources/media/media-release?yearfilter={current_year}": "Media Releases",
          "https://www.sc.com.my/regulation/guidance-notes-and-guiding-principles": "Guidance Notes and Guiding Principles",
         "https://www.sc.com.my/regulation/consultation-papers": "Consultation Papers",
          "https://www.sc.com.my/resources/media/investor-alert-updates": "Investor Alert Updates",
         "https://www.sc.com.my/resources/speeches": "Speeches",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    article_data_map = {}
    HEADLESS_BROWSER_WAIT_TIME = 30000

    charset = "utf-8"

    article_data_map = {}

    @property
    def language(self):
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Jakarta"

    def get_articles(self, response) -> list:
        article_mapping = self.extract_articles_with_dates(response)
        return list(article_mapping.keys())

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        url = response.url
        article_info = self.article_data_map.get(url)
        if article_info and 'title' in article_info:
            return article_info['title']
        fallback_title = (
            response.xpath('//div[contains(@class, "aps-0056-so-wrapper")]/text()').get() or
            response.xpath('//div[@class="a-inner-text"]/text()').get() or
            response.xpath('//div[@class="a-inner-text" and normalize-space(text())="INVESTOR ALERT LIST"]/text()').get()
        )
        return fallback_title.strip() if fallback_title else "No title found"

    def get_body(self, response) -> str:
        if response.url.lower().endswith(".pdf"):
            return ""
        return body_normalization(response.xpath('//div[@class="a-inner-text"]//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> Optional[str]:
        if not response or not hasattr(response, "url"):
            return None
        url = response.url
        article_info = self.article_data_map.get(url)
        if article_info:
            raw_date = article_info.get("date")
            if raw_date:
                try:
                    parsed = parser.parse(raw_date.strip())
                    return parsed.strftime("%m-%d-%Y")
                except Exception:
                    pass
        dates = response.xpath("//div[@class='a-inner-text']/text()").re(r'\d{1,2} \w+ \d{4}')
        if dates:
            try:
                parsed = parser.parse(dates[0].strip())
                return parsed.strftime("%m-%d-%Y")
            except Exception:
                pass
        return None

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        base_url = "https://www.sc.com.my/resources/media/media-release"   
        if "media-release" not in response.url:
            return None
        self.year_offset += 1
        next_year = self.current_year - self.year_offset
        if next_year < 1994:
            return None
        next_url = f"{base_url}?yearfilter={next_year}"
        return next_url

    def extract_articles_with_dates(self, response):
        mapping = {}
        article_blocks = response.xpath('//div[contains(@class, "list-container")]/div[contains(@class, "") and @data-id]')
        for block in article_blocks:
            date = block.xpath('.//div[contains(@class, "aps-0036-so-wrapper")]//div[@class="a-inner-text"]/text()').get()
            anchor = block.xpath('.//a[contains(@class, "aps-0030-so-wrapper")]')
            url = anchor.xpath('./@href').get()
            title = anchor.xpath('.//div[@class="a-inner-text"]/text()').get()
            if not date:
                continue
            if url and title:
                full_url = response.urljoin(url.strip())
                mapping[full_url] = {
                    "title": title.strip(),
                    "date": date.strip(),
                    "pdf": [full_url]
                }
        for anchor in response.xpath('//div[@class="a-inner-text"]//a'):
            url = anchor.xpath(".//@href").get()
            title = anchor.xpath("normalize-space(.)").get()
            date_elem = anchor.xpath("./following-sibling::i[1]/text()").get()
            if not date_elem:
                date_elem_list = anchor.xpath("./following-sibling::i[1]//text()").getall()
                date_elem = " ".join([d.strip() for d in date_elem_list if d.strip()])
            date_issued = None
            if date_elem:
                match = re.search(r"\(Date Issued:\s*([^)]+)\)", date_elem)
                if match:
                    date_issued = match.group(1).strip()
            if not date_issued:
                continue
            if url and title:
                full_url = response.urljoin(url.strip())
                if full_url not in mapping:
                    mapping[full_url] = {
                        "title": title.strip(),
                        "date": date_issued,
                        "pdf": [full_url]
                    }
        self.article_data_map.update(mapping)
        return self.article_data_map
