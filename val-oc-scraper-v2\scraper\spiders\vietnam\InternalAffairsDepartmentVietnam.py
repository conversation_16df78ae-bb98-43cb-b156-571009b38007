from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import urllib.parse
import scrapy
import json
import re

class InternalAffairsDepartmentVietnam(OCSpider):
    name = "InternalAffairsDepartmentVietnam"

    start_urls_names = {
        "https://noichinh.vn/tin-tuc-su-kien/": "Press Releases",
    }
    
    api_start_urls = {
        "https://noichinh.vn/tin-tuc-su-kien/": {
            "url": "https://noichinh.vn/search/select/?sort=date%20desc&q=siteid:261%20AND%20cateid:2884&start=4&rows=15&r=&wt=json&jsoncallback=getSearchData",
            "payload" : {
                "sort": "date desc",
                "q": "siteid:261 AND cateid:2884",
                "start": 4,
                "rows": 15,
                "r": "",
                "wt": "json",
                "jsoncallback": "getSearchData"
            }
        }
    }

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    start_urls_with_no_pagination_set = {}

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return

        payload = response.meta.get("payload") or api_data["payload"].copy()
        api_url = api_data["url"]

        parts = urllib.parse.urlsplit(api_url)
        base = urllib.parse.urlunsplit((parts.scheme, parts.netloc, parts.path, "", ""))
        existing = dict(urllib.parse.parse_qsl(parts.query, keep_blank_values=True))
        merged = {**existing, **payload}
        full_api_url = f"{base}?{urllib.parse.urlencode(merged)}"

        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_start": int(payload.get("start", 0)),
                "rows": int(payload.get("rows", 15)),
                "is_api_response": True,
            },
        )

    def get_articles(self, response) -> list:
        if response.meta.get("is_api_response") or response.url.startswith("https://noichinh.vn/search/select/"):
            text = response.text.strip()
            if text.startswith("getSearchData("):
                pattern = r'^[^(]+\((.*)\)\s*;?\s*$'
                m = re.match(pattern, text, flags=re.DOTALL)
                if not m:
                    return []
                raw_json = m.group(1)
            else:
                raw_json = text

            try:
                data = json.loads(raw_json)
                response_data = data.get("response", {})
                docs = response_data.get("docs", []) or []
                response.meta["api_docs"] = docs
                return docs
            except Exception:
                return []

        docs = response.meta.get("api_docs")
        if docs is not None:
            return docs

        return []

    def get_href(self, entry) -> str:
        url = entry.get("url", "")
        if url.startswith("http://"):
            url = url.replace("http://", "https://", 1)
        return url

    def get_date(self, response, entry=None) -> str:
        api_date = (entry or {}).get("date", "")
        if api_date:
            try:
                return api_date.split("T")[0]
            except:
                pass
        
        date_text = response.xpath('//div[contains(@class,"date") or contains(@class,"time")]//text()').re_first(r'\d{2}/\d{2}/\d{4}')
        if date_text:
            try:
                from datetime import datetime
                parsed = datetime.strptime(date_text, "%d/%m/%Y")
                return parsed.strftime("%Y-%m-%d")
            except:
                pass
          
        url_date = response.url
        date_match = re.search(r'/(\d{4})(\d{2})/', url_date)
        if date_match:
            year, month = date_match.groups()
            return f"{year}-{month}-01"
            
        return ""

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_title(self, response, entry=None) -> str:
        if entry and entry.get("title"):
            return entry["title"]
        return response.xpath("//title/text()").get("") or ""

    def get_body(self, response, entry=None) -> str:
        body_text = "".join(response.xpath("//div[contains(@class,'detail') or contains(@class,'content')]//text()").getall())
        return body_normalization([body_text]) if body_text else ""

    def get_authors(self, response, entry=None):
        return []

    def get_images(self, response, entry=None):
        images = []
        
        if entry and entry.get("avatar"):
            avatar_url = response.urljoin(entry["avatar"])
            images.append(avatar_url)
        
        page_images = response.xpath("//tbody//tr//td//img/@src").getall()
        
        for img_url in page_images:
            if img_url:
                if img_url.startswith('/'):
                    img_url = response.urljoin(img_url)
                elif not img_url.startswith('http'):
                    img_url = response.urljoin(img_url)
                if img_url not in images:
                    images.append(img_url)     
        return images
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        docs = response.meta.get("api_docs", [])
        rows = response.meta.get("rows", 15)
        current_start = response.meta.get("current_start", 0)
        if len(docs) < rows:
            return None
        next_page = int(current_page) + 1
        return next_page
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = None, current_start: int = None):
        if current_page is None:
            current_page = response.meta.get("current_page", 1)
        next_page = self.get_next_page(response, current_page)
        if next_page is None:
            return
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"]
        payload = response.meta.get("payload", api_data["payload"].copy())
        rows = int(response.meta.get("rows", payload.get("rows", 15)))
        if current_start is None:
            current_start = int(response.meta.get("current_start", payload.get("start", 0)))
        next_start = current_start + rows
        payload["start"] = next_start      
        parts = urllib.parse.urlsplit(api_url)
        base = urllib.parse.urlunsplit((parts.scheme, parts.netloc, parts.path, "", ""))
        existing = dict(urllib.parse.parse_qsl(parts.query, keep_blank_values=True))
        merged = {**existing, **payload}
        full_api_url = f"{base}?{urllib.parse.urlencode(merged)}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_start": next_start,
                "rows": rows,
                "current_page": next_page,
                "is_api_response": True,
            },
        )
