from datetime import datetime
from scraper.OCSpider import OCSpider

class ParliamentOfMalaysiaPressReleases(OCSpider):
    name = "ParliamentOfMalaysiaPressReleases"
    
    country = "Malaysia"
    
    start_urls_names = {
       'https://www.parlimen.gov.my/siaran-media.html?uweb=web&lang=bm':''
    }
    
    charset = "iso-8859-1"
    
    article_data_map = {}

    @property
    def language(self): 
        return "Malaysian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//div[@class="irow editable"]//table//tr'):
            url = article.xpath(".//td//a/@href").get()
            title = article.xpath(".//td//a/span//text()").get()
            date = article.xpath("./td/span//text()").get()
            if url and title and date:
                full_url = url
                title = title.strip()
                clean_date = date.strip()
                self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": full_url}
                articles.append(full_url) 
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        month_map = {
            "JAN": "Jan", "FEB": "Feb", "MAC": "Mar", "APR": "Apr", "MEI": "May",
            "JUN": "Jun", "JUL": "Jul", "OGOS": "Aug", "SEP": "Sep", "OKT": "Oct",
            "NOV": "Nov", "DIS": "Dec",
            "JANUARI": "Jan", "FEBRUARI": "Feb", "MAC": "Mar", "APRIL": "Apr",
            "MEI": "May", "JUN": "Jun", "JULAI": "Jul", "OGOS": "Aug",
            "SEPTEMBER": "Sep", "OKTOBER": "Oct", "NOVEMBER": "Nov", "DISEMBER": "Dec"
        }
        date_str = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if not date_str:
            return ""
        try:
            parts = date_str.strip().split()
            if len(parts) != 3:
                return 
            day, month_raw, year = parts
            month_eng = month_map.get(month_raw.upper(), None)
            if not month_eng:
                return 
            fixed_date_str = f"{day} {month_eng} {year}"
            date_obj = datetime.strptime(fixed_date_str, '%d %b %Y')
            return date_obj.strftime('%Y-%m-%d')
        except Exception as e:
            return ""
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return None