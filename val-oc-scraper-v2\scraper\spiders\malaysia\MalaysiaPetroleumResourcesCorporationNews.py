from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from datetime import datetime

class MalaysiaPetroleumResourcesCorporationNews(OCSpider):
    name = "MalaysiaPetroleumResourcesCorporationNews"

    start_urls_names = {
        'https://mprc.gov.my/resources/news/': 'news'
    }

    start_urls_with_no_pagination_set = {
        "https://mprc.gov.my/resources/news/"
    }

    def parse_intermediate(self, response):
        # Pattern for websites where the landing page alone has JS rendering
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    charset = "utf-8"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        articles =  response.xpath('//a[@class="block text-center border-b-8 shadow-md rounded-2xl border-b-mprc-azureBlue group"]/@href').getall()
        filtered_articles = [url for url in articles if not url.lower().endswith('.jpg')]
        return filtered_articles
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title =  response.xpath('//h1[@class="elementor-heading-title elementor-size-default"]//text()').get()
        if title:
            return title
        title = response.xpath('//h1[@class="mb-2"]//text()').get()
        if title: 
            return title
        title = response.xpath('//div[@class="headline"]//h1//text()').get()
        if title:
            return title
        

    def get_body(self, response) -> str:
        body = response.xpath('//div[@class="single-post"]//p//text()').getall()
        if body:
            return body_normalization(body)
        body = response.xpath('//div[@class="elementor-element elementor-element-99201e1 elementor-widget elementor-widget-theme-post-content"]//text()').getall()
        if body:
            return body_normalization(body)
        body = response.xpath('//div[@class="paragraph"]//text()').getall()
        if body:
            return body_normalization(body)
        return ""
        
    def get_date(self, response) -> str:
        malay_to_english_months = {
            "Januari": "January",
            "Februari": "February",
            "Mac": "March",
            "April": "April",
            "Mei": "May",
            "Jun": "June",
            "Julai": "July",
            "Ogos": "August",
            "September": "September",
            "Oktober": "October",
            "November": "November",
            "Disember": "December"
        }
        date_data = response.xpath('//ul//li//span[@class="elementor-icon-list-text"]//text()').getall()
        for data in  date_data:
            date = re.search(r"\d{1,2} (\w+) \d{4}" , data)
            if date:
                full_date = date.group(0)
                month = date.group(1)
                eng_month = malay_to_english_months.get(month)
                full_date = full_date.replace(month , eng_month)
                return full_date
        date = response.xpath('//p[@class="date"]//text()').get()
        if date:
            date = re.search(r'\d+ \w+ \d+', date)
            return date.group(0)
        date= response.xpath('//li[@class="date"]//text()').get()
        if date:
            dt = datetime.strptime(date.strip(), "%Y-%m-%d %I:%M %p")
            formatted = dt.strftime("%d %B %Y")
            return formatted
        date = response.xpath('//div[@class="post-meta"]//span[3]//text()').get()
        if date:
            dt = datetime.strptime(date.strip(), "%d/%m/%Y %I:%M %p")
            formatted = dt.strftime("%d %B %Y")
            return formatted
       
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_images(self, response) -> list[str]:
        images = response.xpath('//div[@class="col-sm-12 col-md-3 portlet-column row01col01"]//img/@src').getall()
        if images:
           return images
        images = response.xpath('//figure[@class="wp-caption"]//img/@src').getall()
        if images:
           return images
        return[]

    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None