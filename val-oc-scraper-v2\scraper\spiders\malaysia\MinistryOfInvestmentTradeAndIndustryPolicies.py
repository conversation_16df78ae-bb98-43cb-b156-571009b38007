from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class MinistryOfInvestmentTradeAndIndustryPolicies(OCSpider):
    name = "MinistryOfInvestmentTradeAndIndustryPolicies"
    
    start_urls_names = {
        "https://www.miti.gov.my/index.php/pages/view/contentdb28.html": "Policies"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.miti.gov.my/index.php/pages/view/contentdb28.html"
    }

    country = "Malaysia"

    charset = "utf-8"
    
    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        relative_links = response.xpath('//div[@class="editable"]//a/@href').getall()
        full_links = [response.urljoin(link) for link in relative_links]
        return full_links
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="page-title"]/h1/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="editable"]//text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date_text = response.xpath(
            '//div[@id="page_content"]//p[em[contains(text(), "Last Updated")]]/em/text()'
        ).get()   
        if date_text:
            match = re.search(r'Last Updated (\d{4}-\d{2}-\d{2})', date_text)
            if match:
                return match.group(1)
        return None

    def get_authors(self, response) -> str:
        date_text = response.xpath(
            '//div[@id="page_content"]//p[em[contains(text(), "Last Updated")]]/em/text()'
        ).get()
        if date_text:
            match = re.search(r'by (.+)$', date_text)
            if match:
                return match.group(1).strip()
        return None
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No next page to scrape
        return None