from scraper.OCSpider import OCSpider
from typing import Optional

class NationalSafetyCouncilFederalNews(OCSpider):
    name = "NationalSafetyCouncilFederalNews"
    
    start_urls_names = {
        "https://www.mkn.gov.my/web/ms/warta-kerajaan-persekutuan/": "FEDERAL GOVERNMENT GAZETTE"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.mkn.gov.my/web/ms/warta-kerajaan-persekutuan/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_date_map = {}

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return list(self.article_date_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        entry = response.request.meta.get("entry")
        return self.article_date_map.get(entry, {}).get("title", "").strip()

    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> str:
        month_map = {
            "Januari": "Jan", "Februari": "Feb", "Mac": "Mar", "April": "Apr",
            "Mei": "May", "Jun": "Jun", "Julai": "Jul", "Ogos": "Aug",
            "September": "Sep", "Oktober": "Oct", "November": "Nov", "Disember": "Dec",
            "Jan": "Jan", "Feb": "Feb", "Apr": "Apr", "Jul": "Jul",
            "Ogo": "Aug", "Okt": "Oct", "Nov": "Nov", "Dis": "Dec"
        }
        entry = response.request.meta.get("entry")
        raw_date = self.article_date_map.get(entry, {}).get("date", "").strip()
        for malay, eng in month_map.items():
            if malay in raw_date:
                raw_date = raw_date.replace(malay, eng)
                break
        return raw_date

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None

    def extract_articles_with_dates(self, response):
        rows = response.xpath('//tbody/tr')
        for row in rows:
            link = row.xpath('.//td[2]//a/@href').get()
            title_parts = row.xpath('.//td[2]//a//text()[normalize-space()]').getall()
            title = ''.join(title_parts).strip()
            date = row.xpath('.//td[3]//text()[normalize-space()]').get()
            if date:
                date = date.strip()
            else:
                continue
            if link and title and date:
                full_url = response.urljoin(link)
                self.article_date_map[full_url] = {
                    "title": title,
                    "date": date
                }
        return self.article_date_map