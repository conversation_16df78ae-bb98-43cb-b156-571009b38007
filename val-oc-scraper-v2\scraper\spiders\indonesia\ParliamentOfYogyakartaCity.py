from datetime import datetime
import re
from typing import List
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization

class ParliamentOfYogyakartaCity(OCSpider):
    name = "ParliamentOfYogyakartaCity"

    start_urls_names = {
        "https://dprd.jogjakota.go.id/search/category/berita": "Berita",
        # "https://jdih-dprd.jogjakota.go.id/": "PRODUK HUKUM TERBARU"  # Not able to crawl through any ways
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"
    
    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "content"]//header//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h2//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="col-lg-12 blog_details pt-1"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        MONTH_MAP = {
            'Januari': 'January',
            'Februari': 'February',
            'Maret': 'March',
            'April': 'April',
            'Mei': 'May',
            'Juni': 'June',
            'Juli': 'July',
            'Agustus': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Desember': 'December'
        }
        date_text = response.xpath('//div[@class="bn-label"]//text()').get()
        clean_date = re.search(r'(\d{2} \w+ \d{4})', date_text).group(1)
        for indo_month, eng_month in MONTH_MAP.items():
            if indo_month in clean_date:
                clean_date = clean_date.replace(indo_month, eng_month)
                break
        date_obj = datetime.strptime(clean_date, "%d %B %Y")
        return date_obj.strftime("%Y-%m-%d")

    def get_authors(self, response, entry=None) -> List[str]:
        return []
        
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="single-post row"]//div//figure//img//@src').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page = response.xpath('//ul[@class="pagination"]/li[contains(@class, "next")][1]/a/@href').get()
        if next_page :
            return next_page
        return None