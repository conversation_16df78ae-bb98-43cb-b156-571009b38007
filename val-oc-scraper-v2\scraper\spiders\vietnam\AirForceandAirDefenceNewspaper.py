
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class AirForceandAirDefenceNewspaper(OCSpider):
    name = "AirForceandAirDefenceNewspaper"

    start_urls_names = {
        "https://phongkhongkhongquan.vn/1432/tin-tuc/page-1.html": "News",
        }
    
    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//*[@id="content"]/div/div[1]/div[1]/div/div/a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//*[@id="content"]/div/div[1]/div[1]//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//*[@id="content"]/div/div[1]/div[1]/div/div[2]/div/div[5]/div[2]/p//text() | //*[@id="content"]/div/div[1]/div[1]/div/div[2]/div/div[5]/div[1]/p[2]/strong/font/font//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//*[@id="content"]/div/div[1]/div[1]/div/div[2]/div/div[5]/div//span/span/em/img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//*[@id="content"]/div/div[1]/div[1]/div/div[2]/div/div[1]//text()').get()
        clean_text = date.replace("\xa0", " ").strip()
        clean_text = re.sub(r"\d{1,2}\s*giờ:\d{1,2}\s*phút\s*", "", clean_text)
        date_obj = dateparser.parse(clean_text, languages=['vi'])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return response.xpath('//a[normalize-space(text())="Tiếp"]/@href').get()
