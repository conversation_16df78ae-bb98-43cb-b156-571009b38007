from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
import dateparser

class ParliamentOfMalaysiaLHWrittenAnswers(OCSpider):
    name = "ParliamentOfMalaysiaLHWrittenAnswers"
    
    start_urls_names = {
        "https://www.parlimen.gov.my/keratan-akhbar.html?uweb=&": "News"
    }

    charset = "iso-8859-1"

    custom_settings = {
        'DOWNLOAD_DELAY': 10,
    }

    def parse_intermediate(self, response):
        articles = response.xpath("//div[@class='irow editable']//ul//li//a//@href").getall()
        total_articles = len(articles)
        start_url =  list(self.start_urls_names.keys())[0] 
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': start_url
                    },
                    dont_filter=True
            )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}  # Mapping date and title to child articles from start URL

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='irow editable']//ul//li"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//p[2]//text()").get()
            date = article.xpath(".//p[1]//text()").get()
            if url and title and date:
                self.article_data_map[url] = {"date":date, "title" : title}
                articles.append(url)
        all_articles = list(set(articles))
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return [response.url]
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response): 
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date = date.split("|")[0]
        parsed_date = dateparser.parse(date, languages=['ms']) 
        return parsed_date.strftime("%Y-%m-%d")

    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to crawl
        return None    