from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class TheBusinessReview(OCSpider):
    name = "TheBusinessReview"

    start_urls_names = {
        'https://nhipcaudautu.vn/kinh-doanh/': 'Bussiness Review'
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="col-md-4 col-sm-4 col-xs-4 col-tn-12"]//h2//a[position() < 4]/@href | //a[@class="title"]/@href | (//div[@class="row"]//div[@class="content"]//h3//a/@href)[position() < 3] | //p[@class="entry-title post-cats-h3"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="post-detail-title"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="tdInfo"]//p//text() | //div[@class="des-small"]//text()').getall())

    def get_date(self, response) -> str:
        date = response.xpath('//span[@class="date-post"]//text()[2]').get()
        match = re.search(r"\b\d{2}/\d{2}/\d{4}\b", date)
        return match.group(0) if match else ""

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="post-container"]//div[@class="media-body"]//img/@src').getall()     

    def get_document_urls(self, response, entry=None) -> list[str]:
        return [] 

    def get_authors(self, response) -> str :
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        next_page = response.xpath('//div[@id="pagination"]//a[@rel="next"]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        return None