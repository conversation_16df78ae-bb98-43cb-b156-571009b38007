from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
from datetime import datetime
from typing import Optional
import re
import time

class VietnamAgricultureNewspaper(OCSpider):
    name = "VietnamAgricultureNewspaper"

    start_urls_names = {
        "https://nongnghiepmoitruong.vn/thoi-su/": "News"
    }

    start_urls_with_no_pagination_set = {}

    api_start_url = {
        "https://nongnghiepmoitruong.vn/thoi-su/": {
        "url": "https://nongnghiepmoitruong.vn/?mod=news&act=loadmore_cate_detail&category_id=1",
        }
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        unix_milliseconds = int(time.time() * 1000)
        api_url = api_data["url"]
        
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        current_page = response.meta.get("current_page", 1)
        api_url = f"https://nongnghiepmoitruong.vn/?mod=news&act=loadmore_cate_detail&category_id=1&page={current_page}&v={unix_milliseconds}"
        api_data["url"] = api_url
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers=headers,
            dont_filter=True,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "current_page": current_page
            },
        )

    articles_to_date = {}

    def get_articles(self, response) -> list:  
        return list(set(response.xpath('//div[@class="news-info"]//a//@href').getall()))    
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return  response.xpath('//h1[@class="main-title main-title-super detail-title"]//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//div[@class="content"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d-%m-%Y"

    def get_date(self, response) -> str:
        date= response.xpath('//span[@class="time-detail time-detail-mobile"]//text()').get()
        match = re.search(r"\b(\d{2}/\d{2}/\d{4})\b", date)
        date_str = match.group(1)
        dt = datetime.strptime(date_str, "%d/%m/%Y")
        return dt.strftime("%d-%m-%Y")

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return int(response.meta.get("current_page")) + 1

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_url.get(start_url)
        if not api_data:
           self.logger.error("API data not found for start_url")
           return

        api_url = api_data["url"]
        next_page = self.get_next_page(response, current_page)
        

        if next_page:
          yield scrapy.Request(
            url=api_url,
            method="GET",
            callback=self.parse_intermediate,
            meta={"current_page": next_page, "start_url": start_url},
            dont_filter=True,
           )
        else:
         return None