from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class AlbamaDepartmentOfLabour(OCSpider):
    name = 'AlbamaDepartmentOfLabour'
    
    country = "US"

    start_urls_names = {
        'https://www.labor.alabama.gov/news_feed/News_Page.aspx': 'News'
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES" :
        {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY" : 2
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 Seconds wait time
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class = "card-body"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//div[@class= "row"]//h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//table[@id= "table_0"]//p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class= "row"]//div//p/text()').get()
    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//nav[@class="custom-pagination text-center"]/a[contains(@class, "next")]/@href').get()
        if next_page:
            return next_page
        else:
            return None 