Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF91050000 ntdll.dll
7FFF8F720000 KERNEL32.DLL
7FFF8E530000 KERNELBASE.dll
7FFF8F100000 USER32.dll
7FFF8EA30000 win32u.dll
7FFF8FB00000 GDI32.dll
7FFF8EBB0000 gdi32full.dll
7FFF8EA60000 msvcp_win.dll
7FFF8E910000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF8F800000 advapi32.dll
7FFF90F60000 msvcrt.dll
7FFF901C0000 sechost.dll
7FFF8EB00000 bcrypt.dll
7FFF90050000 RPCRT4.dll
7FFF8D860000 CRYPTBASE.DLL
7FFF8EB30000 bcryptPrimitives.dll
7FFF8ED90000 IMM32.DLL
