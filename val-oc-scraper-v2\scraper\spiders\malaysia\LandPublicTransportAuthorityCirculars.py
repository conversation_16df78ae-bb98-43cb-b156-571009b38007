from scraper.OCSpider import OCSpider
from datetime import datetime

class LandPublicTransportAuthorityCirculars(OCSpider):
    name = "LandPublicTransportAuthorityCirculars"
    
    start_urls_names = {
        "https://www.apad.gov.my/sumber-maklumat1/pekeliling?category[0]=8&category_children=1": "Circular"
    }

    start_urls_with_no_pagination_set = {}

    country = "Malaysia"
    
    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    MAX_ARTICLES = 500  # Adjust based on data size

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//table[contains(@class, 'table-striped')]//tbody//tr"):
            if len(articles) >= self.MAX_ARTICLES:
                break
            url = article.xpath(".//a[contains(@href, '/file')]/@href").get()
            title = article.xpath(".//span[@itemprop='name']//text()[normalize-space()][1]").get()
            date = article.xpath(".//time[@itemprop='datePublished']/text()").get()
            if url and title and date:
                title = title.strip()
                date = date.strip()
                self.article_data_map[url] = {"title": title, "date": date}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response):
        date_str = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if not date_str:
            return ""
        month_map = {
            'Jan': 'Jan', 'Feb': 'Feb', 'Mac': 'Mar', 'Apr': 'Apr', 'Mei': 'May', 'Jun': 'Jun',
            'Jul': 'Jul', 'Ogs': 'Aug', 'Sept': 'Sep', 'Okt': 'Oct', 'Nov': 'Nov', 'Dis': 'Dec'
        }
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            return date_obj.strftime('%Y-%m-%d')
        except ValueError:
            pass
        for malay_month, eng_month in month_map.items():
            if malay_month in date_str:
                date_str = date_str.replace(malay_month, eng_month)
                break
        non_iso_format = '%d %b %Y'
        try:
            date_obj = datetime.strptime(date_str, non_iso_format)
            return date_obj.strftime('%Y-%m-%d')
        except ValueError:
            return date_str

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath("//div[contains(@class, 'k-pagination')]//ul[contains(@class, 'k-pagination__pages')]//li/a/@href").get()