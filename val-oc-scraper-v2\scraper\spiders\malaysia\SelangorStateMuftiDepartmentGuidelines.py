from scraper.OCSpider import OCSpider
import re

class SelangorStateMuftiDepartmentGuidelines(OCSpider):
    name = "SelangorStateMuftiDepartmentGuidelines"

    start_urls_names = {
        "https://www.muftiselangor.gov.my/garis-panduan/": "News"
    }
    
    start_urls_with_no_pagination_set = {
        "https://www.muftiselangor.gov.my/garis-panduan/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//h3[@class="elementor-post__title"]'):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//a//text()").get()
            date = None
            if url:
                match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', url)
                if match:
                    year, month, day = match.groups()
                    month = month.zfill(2)
                    day = day.zfill(2)
                    date = f"{year}-{month}-{day}"
            date = None
            if url:
                match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', url)
                if match:
                    year, month, day = match.groups()
                    month = month.zfill(2)
                    day = day.zfill(2)
                    date = f"{year}-{month}-{day}"
            if url and title and date:
                self.article_data_map[url] = {"title": title, "date": date, "pdf": url}
                self.article_data_map[url] = {"title": title, "date": date, "pdf": url}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")    
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")    

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None