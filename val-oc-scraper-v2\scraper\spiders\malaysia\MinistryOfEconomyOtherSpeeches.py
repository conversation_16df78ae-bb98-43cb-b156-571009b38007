from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
import re
from datetime import datetime

class MinistryOfEconomyOtherSpeeches(OCSpider):
    name = "MinistryOfEconomyOtherSpeeches"
    
    start_urls_names = {
        "https://ekonomi.gov.my/ms/media/koleksi-teks-ucapan/ucapan-lain-lain": "News"  # Pagination is not supported
    }
    
    start_urls_with_no_pagination_set = {
        "https://ekonomi.gov.my/ms/media/koleksi-teks-ucapan/ucapan-lain-lain"
    }

    charset = "iso-8859-1"

    country = "Malaysia"
    
    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//tbody//tr"):
            url = article.xpath(".//td//a//@href").get()
            title = article.xpath(".//td//a//text()").get()
            date = article.xpath(".//td[@class='views-field views-field-field-date']//text()").get()
            if url and title and date:
                self.article_data_map[url]={"url":url, "title": title, "date": date}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response):
        raw_date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        cleaned_date = ' '.join(raw_date.strip().split())
        month_replacements = {
            'januari': 'January',
            'februari': 'February',
            'mac': 'March',
            'april': 'April',
            'mei': 'May',
            'jun': 'June',
            'julai': 'July',
            'ogos': 'August',
            'september': 'September',
            'oktober': 'October',
            'november': 'November',
            'disember': 'December'
        }
        for malay, eng in month_replacements.items():
            if malay in cleaned_date.lower():
                cleaned_date = re.sub(malay, eng, cleaned_date, flags=re.IGNORECASE)
                break
        try:
            datetime.strptime(cleaned_date, "%d %B %Y")
            return cleaned_date
        except ValueError:
                date_obj = datetime.strptime(cleaned_date, "%B %d, %Y")
                return date_obj.strftime("%d %B %Y")
           
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
            
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) -> Optional[str]:
        return None