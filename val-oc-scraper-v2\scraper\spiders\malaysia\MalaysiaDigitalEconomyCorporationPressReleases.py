#MalaysiaDigitalEconomyCorporationPressReleases
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import scrapy
import json

class MalaysiaDigitalEconomyCorporationPressReleases(OCSpider):
    name = "MalaysiaDigitalEconomyCorporationPressReleases"

    start_urls_names = {
        "https://mdec.my/media-release/news-press-release": "News"
        }
    

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }


    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
     articles= []
     script_content = response.xpath('//script[@id="__NEXT_DATA__" and @type="application/json"]/text()').get()
     if script_content:
            try:
                data = json.loads(script_content)
                posts = data.get("props", {}).get("pageProps", {}).get("posts", []) 
                for article in posts:
                  slug = article.get("Slug")
                  id = article.get("Id")
                  title = article.get("Title")
                  date = article.get("Date")
                  title = article.get("Title")
                  content = article.get("Content")
                  image = article.get("Image")
                  if slug and id and date:
                    url = f"https://mdec.my/media-release/news-press-release/{id}/{slug}"
                    self.article_data_map[url]={
                    "date":date,"title":title,"content":content,"image":image
                     }
                  articles.append(url) 

                self.logger.info("No article with slug found")

            except json.JSONDecodeError:
                self.logger.error("Failed to decode JSON")

     return list(set(articles))

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//div/main/div[1]/div[2]/div/div[1]/div[3]/p/p/span//text()').getall())

    def get_images(self, response) -> list:
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("img", "")]
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date_obj = datetime.strptime(date, "%Y-%m-%d")  # Correct format!
        return date_obj.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 