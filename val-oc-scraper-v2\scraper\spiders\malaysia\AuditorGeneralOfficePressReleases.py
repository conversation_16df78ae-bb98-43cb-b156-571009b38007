from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class AuditorGeneralOfficePressReleases(OCSpider):
    name = "AuditorGeneralOfficePressReleases"

    start_urls_names = {
        "https://www.audit.gov.my/index.php/senarai-kenyataan-media/1406-kenyataan-akhbar-2024": "News/PressNote" # pagination is not supported
    }
    
    start_urls_with_no_pagination_set = {
        "https://www.audit.gov.my/index.php/senarai-kenyataan-media/1406-kenyataan-akhbar-2024"
    }
   
    charset = "iso-8859-1"
   
    country = "Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
        
    article_data_map = {}

    def get_articles(self, response) -> list:  
        articles= []
        for article in response.xpath('//table[@class="table table-striped styled-table"]//tbody//tr'):
            url = article.xpath(".//td//a//@href").get()
            date=article.xpath(".//td[1]//text()").get(),
            title= body_normalization(article.xpath(".//th//text() | .//ol//li//text()").getall())
            if url and date and title:
                articles.append(url)
                self.article_data_map[url]={
                    "date":date,"title":title
                }
        return list(set(articles))

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
           
    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
         
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date=  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date=str(date)
        date = date.replace("('","").replace("',)","")
        parsed_date = dateparser.parse(date, languages=['ms']) 
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return [response.url]

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 