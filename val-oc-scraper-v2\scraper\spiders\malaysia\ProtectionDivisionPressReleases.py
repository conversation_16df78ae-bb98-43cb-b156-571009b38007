from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import urljoin, unquote

class ProtectionDivisionPressReleases(OCSpider):
    name = "ProtectionDivisionPressReleases"

    start_urls_names = {
        "https://www.bp.gov.my/ms/muat-turun/kenyataan-media" : "Media Statement" # pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.bp.gov.my/ms/muat-turun/kenyataan-media"
    }

    charset = "iso-8859-1"

    country = "malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(response.url, timeout=10000),
            callback=self.parse,
            meta = {
                "start_url" : start_url,
            }
        )

    def get_articles(self, response) -> list: 
        mapping = {}
        base_url = "https://www.bp.gov.my"
        articles = [] 
        containers = response.xpath('//tbody/tr')
        for row in containers:
            date = row.xpath('./td[1]//div[@class="sppb-addon-content"]/text()').get()
            title = row.xpath('./td[2]//a//text()').get().strip()
            link = row.xpath('./td[2]//a/@href').get()
            if link:
                full_url = urljoin(base_url,link)
                normalized_link = unquote(full_url).rstrip('/')
                articles.append(normalized_link)
                mapping[normalized_link] = {
                    'title': title,
                    'pdf': full_url,    
                    'date': date
                }
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title=None
        normalized_url = unquote(response.url).rstrip('/')
        if 'text/html' not in response.headers.get('Content-Type', b'').decode():
            title = self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")
        return title
    
    def get_body(self, response) -> str:
        if 'text/html' not in response.headers.get('Content-Type', b'').decode():
            return ''
        
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        content_type = response.headers.get('Content-Type', b'').decode()
        date = None
        normalized_url = unquote(response.url).rstrip('/')
        if 'text/html' not in content_type:
            date = self.article_to_pdf_mapping.get(normalized_url, {}).get("date")
        return date.strip() if date else ""

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        content_type = response.headers.get('Content-Type', b'').decode()
        normalized_url = unquote(response.url).rstrip('/')
        pdf=[]
        if 'text/html' not in content_type:
            pdf = self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
            return [pdf]
        else :
            return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None