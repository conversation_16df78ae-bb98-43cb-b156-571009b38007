from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class NewMexicoStateDepartmentOfTransportation(OCSpider):
    name = 'NewMexicoStateDepartmentOfTransportation'

    country = "US"
    
    start_urls_names = {
        'https://www.dot.nm.gov/contact-us/news-announcements/': "News"
    }
            
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        return response.xpath("//h2//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='et_pb_blurb_content']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='et_pb_blurb_content']//p//img//@src").getall()
    
    def date_format(self) -> str:
        return '%b %d, %Y'

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='et_post_meta_wrapper']//span//text()").get()
    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='alignleft']//a//@href").get()
        if next_page:
            return next_page
        else:
            return None