from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ParliamentOfEastTanjungJabungRegency(OCSpider):
    name = "ParliamentOfEastTanjungJabungRegency"

    start_urls_names = {
        "https://dprd.tanjabtimkab.go.id/berita": "News",
        "https://dprd.tanjabtimkab.go.id/kategori/sidang-paripurna" : "News"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"
        
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="content-info-short clearfix"]//h3//a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='single-page']//h3//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='single-page']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="post-wrap"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str: 
        indonesian_to_english = {
            "Januari": "January",
            "Februari": "February",
            "Maret": "March",
            "April": "April",
            "Mei": "May",
            "Juni": "June",
            "Juli": "July",
            "Agustus": "August",
            "September": "September",
            "Oktober": "October",
            "November": "November",
            "Desember": "December",
        }
        date = response.xpath("//div[@class='single-page']//p[@class='rs post-by']//text()").get() 
        date = date.split(",")[1] 
        date = date.split("|")[0]
        date = date.strip()
        for indo, eng in indonesian_to_english.items():
            if indo in date:
                date = date.replace(indo, eng)
                break 
        try:
            date_obj = datetime.strptime(date.strip(), "%d %B %Y")
            formatted_date = date_obj.strftime("%Y-%m-%d")
            return formatted_date
        except ValueError as e:
            if date=="00  0000":
                today = datetime.today()
                return today.strftime("%Y-%m-%d")
            return None

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath("//ul//li[@class='next page']//a//@href").get()