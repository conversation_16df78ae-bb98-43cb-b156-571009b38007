from scraper.OCSpider import <PERSON>CSpider
from typing import Optional
from urllib.parse import urljoin

class MinistryOfInvestmentTradeAndIndustryReports(OCSpider):
    name = "MinistryOfInvestmentTradeAndIndustryReports"
    
    start_urls_names = {
        "https://www.miti.gov.my/index.php/pages/view/contentc826.html": "Ministry of Investment, Trade and Industry Report"
    }

    start_urls_with_no_pagination_set = {
        "https://www.miti.gov.my/index.php/pages/view/contentc826.html"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_map = {}

    def get_articles(self, response) -> list:
        self.article_map = self.extract_reports_with_years(response)
        pdf_urls = []
        for report in self.article_map.values():
            pdf_urls.extend(report.get("pdf_links", []))
        return pdf_urls

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        entry = response.request.meta.get("entry")
        for report in self.article_map.values():
            if entry in report.get("pdf_links", []):
                return report.get("title", "").strip()
        return ""

    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        entry = response.request.meta.get("entry")
        for year, report in self.article_map.items():
            if entry in report.get("pdf_links", []):
                return year
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None

    def extract_reports_with_years(self, response):
        base_url = "https://www.miti.gov.my"
        report_map = {}
        rows = response.xpath('//table//tr')
        for row in rows:
            tds = row.xpath('.//td')
            if len(tds) != 2:
                continue
            for td in tds:
                title_parts = td.xpath('.//strong/text()').getall()
                title = next((t.strip() for t in title_parts if "REPORT" in t.upper()), None)
                year_match = td.xpath('.//text()').re(r'\b(20\d{2}|19\d{2})\b')
                year = year_match[0] if year_match else None
                raw_links = td.xpath('.//a[contains(@href, ".pdf")]/@href').getall()
                pdf_links = [urljoin(base_url, link) for link in raw_links]
                if title and year:
                    report_map[year] = {
                        "title": title,
                        "pdf_links": pdf_links
                    }
        return report_map