from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class StateSecuritiesCommissionOfVietnam(OCSpider):
    name = "StateSecuritiesCommissionOfVietnam"

    start_urls_names = {
        "https://ssc.gov.vn/webcenter/portal/ubck/pages_r/m/tintc-skin/thngtincngb": "Thông tin công bố",
        "https://ssc.gov.vn/webcenter/portal/ubck/pages_r/m/tintc-skin/thngbo-choiuhnh":"Thông báo chỉ đạo - điều hành",
        "https://ssc.gov.vn/webcenter/portal/ubck/pages_r/m/tintc-skin/hotngkinhdoanhck":"Hoạt động kinh doanh chứng khoán",
        "https://ssc.gov.vn/webcenter/portal/ubck/pages_r/m/tintc-skin/thanhtra-gimst":"Thanh tra - gi<PERSON>m sát",
        "https://ssc.gov.vn/webcenter/portal/ubck/pages_r/m/tintc-skin/hptcquct":"Hoạt động hợp tác quốc tế",
        "https://ssc.gov.vn/webcenter/portal/ubck/pages_r/m/tintc-skin/dnhngmcututhumuasmcng":"Dự án, hạng mục đầu tư, đấu thầu, mua sắm công",
        "https://ssc.gov.vn/webcenter/portal/ubck/pages_r/m/tintc-skin/chinlcphttrinngnh":"Chiến lược phát triển ngành",
        "https://ssc.gov.vn/webcenter/portal/ubck/pages_r/m/tintc-skin/danhsachnguoihanhnghechungkhoan":"Danh sách người hành nghề chứng khoán"

    }

    start_urls_with_no_pagination_set = {}

    HEADLESS_BROWSER_WAIT_TIME = 1000 

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,            
	}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:  
        return response.xpath('//h3[@class="title pr-0"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="content-voice"]//span//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y" 

    def get_date(self, response) -> str:
        return response.xpath('//h1[@class="detail-title"]/small/text()').re_first(r"(\d{1,2}/\d{1,2}/\d{4})")

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None)->list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[normalize-space(text())=">"]//@href').get()