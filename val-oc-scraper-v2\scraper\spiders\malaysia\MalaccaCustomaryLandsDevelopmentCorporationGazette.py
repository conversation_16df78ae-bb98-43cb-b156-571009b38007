from scraper.OCSpider import <PERSON><PERSON>pider 
from urllib.parse import urljoin, unquote
import re

class MalaccaCustomaryLandsDevelopmentCorporationGazette(OCSpider):
    name = "MalaccaCustomaryLandsDevelopmentCorporationGazette"

    start_urls_names = {
        'https://ptg.melaka.gov.my/en/gazette': 'GAZETTE'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://ptg.melaka.gov.my/en/gazette"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list = []
        month_map = {
            "JAN": "JANUARY", "JANUARI": "JANUARY",
            "FEB": "FEBRUARY", "FEBRUARI": "FEBRUARY",
            "MAC": "MARCH", "MARCH": "MARCH",
            "APRIL": "APRIL",
            "MEI": "MAY", "MAY": "MAY",
            "JUN": "JUNE", "JUNE": "JUNE",
            "JULAI": "JULY", "JULY": "JULY",
            "OGOS": "AUGUST", "AUG": "AUGUST",
            "SEPTEMBER": "SEPTEMBER",
            "OKTOBER": "OCTOBER", "OCT": "OCTOBER",
            "NOVEMBER": "NOVEMBER",
            "DISEMBER": "DECEMBER", "DEC": "DECEMBER"
        }
        articles = response.xpath('//tbody//tr')
        for article in articles:
            title = article.xpath('.//td[contains(@style,"width: 78.8346%;")]//h5//text()').get()
            url = article.xpath('.//td//a//@href').get()         
            if title:
                date_match = re.search(r'\(([^)]+)\)', title)
                if date_match:
                    date_text = date_match.group(1)
                    month_match = re.search(r'\b[A-Z]+', date_text.upper())
                    year_match = re.search(r'\d{4}', date_text)                   
                    if month_match and year_match and url:
                        month_key = month_match.group(0)
                        mapped_month = month_map.get(month_key)                      
                        if mapped_month:
                            base_url = "https://ptg.melaka.gov.my"
                            full_url = urljoin(base_url, url)
                            decoded_url = unquote(full_url)
                            child_article_list.append(decoded_url)               
                            self.article_url_title_date_mapping[decoded_url] = [
                                title.strip(),
                                f"{mapped_month} {year_match.group(0)}"
                            ]
        return child_article_list

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[0]

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[1]

    def date_format(self) -> str:
        return "%B %Y"

    def get_images(self, response) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return unquote(response.url)
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None