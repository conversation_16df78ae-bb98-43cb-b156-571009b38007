from typing import Optional
from scraper.OCSpider import OCSpider

class MinistryOfHumanResourcesArchive(OCSpider):
    name = "MinistryOfHumanResourcesArchive"

    start_urls_names = {
        "https://www.mohr.gov.my/index.php/media-berita/arkib": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for link in response.xpath("//div[contains(@class, 'article-details')]//li"):
            url = link.xpath(".//a[contains(@href, '.pdf')]//@href").get()
            title = link.xpath(".//a//text()").get()
            if url and title:
                full_url = response.urljoin(url)
                self.article_data_map[full_url] = {"title": title.strip()}
                articles.append(full_url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response):
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        date = title.split(" ")[-1].replace(")", "")
        if date[1]=="0":
            return date
        else:
            date=title.split(" ")[2]
            return date 
        
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return response.xpath("//nav[@class='pagenavigation']//li[@class='previous']//a//@href").get()