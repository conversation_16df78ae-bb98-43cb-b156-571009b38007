from scraper.OCSpider import OCSpider
import re

class MalaccaIslamicReligiousCouncilAnnualReports(OCSpider):
    name = "MalaccaIslamicReligiousCouncilAnnualReports"

    start_urls_names = {
        'https://www.maim.gov.my/index.php/ms/sumber/laporan-tahunan' : 'Annual Reports',
    }

    start_urls_with_no_pagination_set = {
        'https://www.maim.gov.my/index.php/ms/sumber/laporan-tahunan'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}
    
    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//div[contains(@class, "sppb-carousel-extended-item")]')
        title = response.xpath('//h1[@itemprop="headline"]//text()').get().strip()
        print(title)
        for row in rows:
            book_link = row.xpath('.//a/@href').get()
            img_link = row.xpath('.//img/@src').get()
            match = re.search(r'20\d{2}(?=\.(png|jpg))', img_link)
            year = match.group(0)
            if book_link and img_link and year:
                book_link = response.urljoin(book_link.strip()).replace("http://", "https://")
                img_link = response.urljoin(img_link.strip())
                self.article_title_pdf_mapping[book_link] = (img_link, book_link, year,title)
                articles.append(book_link)
        return articles
    
    def get_href(self, entry) -> str:
        return entry.split('#')[0]

    def get_title(self, response) -> str:
        url = response.url.replace("http://", "https://")
        entry = self.article_title_pdf_mapping.get(url)
        if entry:
            return entry[3]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
       return "%Y"

    def get_date(self, response) -> str:
        url = response.url.replace("http://", "https://")
        for book_link, (_, _, year,_) in self.article_title_pdf_mapping.items():
            if book_link == url:
                return year
    
    def get_images(self, response) -> list[str]:
        url = response.url.replace("http://", "https://")
        for book_link, (img_link, _, _,_) in self.article_title_pdf_mapping.items():
            if book_link == url:
                return [img_link]

    def get_document_urls(self, response, entry=None):
        url = response.url.replace("http://", "https://")
        for book_link, (_, link, _,_) in self.article_title_pdf_mapping.items():
            if book_link == url:
                return [link]

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None