from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ParliamentOfJembranaRegency(OCSpider):
    name = "ParliamentOfJembranaRegency"

    start_urls_names = {
        "https://dprd.jembranakab.go.id/kegiatan": "News",
        "https://jdih-dprd.jembranakab.go.id/berita/index": "News"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        urls_from_title = response.xpath('//div[@class="entry-title"]/h2/a/@href').getall()
        urls_with_id = response.xpath("//a[contains(@href, 'id=')]/@href").getall()
        combined_urls = set(urls_from_title + urls_with_id)
        full_urls = []
        for url in combined_urls:
            if url:
                full_url = response.urljoin(url.strip())
                full_urls.append(full_url)
        return full_urls

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="post-title"]/h5/text()|//div[@class="entry-title"]/h2/text() |//div[@class="entry-title"]/h2/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="post"]//div[contains(@class, "margin-30px-bottom")]/p/text() | //div[@class="entry-content notopmargin"]/p/span/text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="post"]//img/@src | //div[@class="entry-image"]//img/@src').getall()
    
    def date_format(self) -> str:
        return"%m-%d-%Y"

    def get_date(self, response) -> Optional[str]:
        ID_MONTH_TO_NUMBER = {
            "Januari": "01", "Februari": "02", "Maret": "03", "April": "04",
            "Mei": "05", "Juni": "06", "Juli": "07", "Agustus": "08",
            "September": "09", "Oktober": "10", "November": "11", "Desember": "12"
        }
        current_url = response.url
        raw_date = getattr(self, "article_date_map", {}).get(current_url)
        if not raw_date:
            raw_date = response.xpath('//ul[@class="entry-meta clearfix"]/li[1]/text()').get()
            if raw_date:
                raw_date = raw_date.strip()
            else:
                return None
        raw_date = raw_date.strip()
        try:
            parts = raw_date.split()
            if len(parts) == 3 and parts[1] in ID_MONTH_TO_NUMBER:
                day, month_id, year = parts
                month_num = ID_MONTH_TO_NUMBER[month_id]
                formatted = f"{month_num}-{int(day):02d}-{year}"
                return formatted
        except Exception:
            pass
        try:
            dt = datetime.strptime(raw_date, "%d %b, %Y")
            formatted = dt.strftime("%m-%d-%Y")
            return formatted
        except Exception:
            pass
        return None

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page =  response.xpath('//ul[@class="pager"]/li/a[@rel="next"]/@href').get()
        if next_page:
            return next_page
        return None
    
    def extract_articles_with_dates(self, response):
        if hasattr(self, "article_date_map") and self.article_date_map:
           return
        self.article_date_map = {}
        articles = response.xpath('//div[contains(@class, "col-lg-4") and contains(@class, "margin-30px-bottom")]')
        for article in articles:
            href = article.xpath('.//a[contains(@href, "/berita/view?id=")]/@href').get()
            raw_date = article.xpath('.//div[contains(@class, "margin-10px-bottom")]/span/text()').get()
            if href and raw_date:
                full_url = response.urljoin(href.strip())
                clean_date = raw_date.strip()
                self.article_date_map[full_url] = clean_date
