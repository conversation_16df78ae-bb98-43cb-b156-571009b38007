from scraper.OCSpider import OCSpider 
import scrapy
from scraper.utils.helper import body_normalization
from datetime import datetime

class VietNamGovernmentPortalHaNoiCapitalVietnam(OCSpider):
    name = "VietNamGovernmentPortalHaNoiCapitalVietnam"

    start_urls_names = {
        'https://thanglong.chinhphu.vn/thoi-su.htm': 'news'
    }

    charset = "utf-8"
    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return 'unknown'

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        # Extract all article URLs from the listing page
        return response.xpath('//div[@class="box-stream-content"]//h2//a//@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="detail-title"]//text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@data-role="content"]//p//text()').getall())
        
    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="detail-cmain"]//img/@src').getall()
    
    def date_format(self) -> str:
        # ISO format
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        # Extract date text from article page
        date_text = response.xpath('//div[@class="detail-time"]/text()').get()
        if not date_text:
            raise ValueError(f"Date not found for url = {response.url}")
        date_text = date_text.strip()
        try:
            # Convert to ISO format YYYY-MM-DD
            return datetime.strptime(date_text, "%d/%m/%Y").date().isoformat()
        except Exception:
            raise ValueError(f"Date parsing failed for url = {response.url}, raw = {date_text}")

    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response):
        # Get current page number from meta
        return response.meta.get('page_no', 1)
    
    def go_to_next_page(self, response, start_url, current_page=None):
        page_no = self.get_next_page(response)
        page_no += 1
        if page_no:
            start_url = response.meta.get('start_url')
            yield scrapy.Request(
                url=f"https://thanglong.chinhphu.vn/timelinelist/10317/{page_no}.htm",
                method="GET",
                callback=self.parse, 
                meta={
                    'page_no': page_no,
                    'start_url': start_url
                }
            )
