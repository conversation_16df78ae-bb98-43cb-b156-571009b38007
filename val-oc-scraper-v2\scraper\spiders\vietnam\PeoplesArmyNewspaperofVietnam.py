from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy

class PeoplesArmyNewspaperofVietnam(OCSpider):
    name = "PeoplesArmyNewspaperofVietnam"

    start_urls_names = {
        "https://www.qdnd.vn/chinh-tri/tin-tuc": "News"
    }

    start_urls_with_no_pagination_set = {}

    custom_settings = {
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self,response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page",1)
        url = f"{start_url}/p/{current_page}"
        yield scrapy.Request(
            url=url,
            callback = self.parse,
            meta = {
                "start_url" : start_url,
                "current_page" : current_page
            }
        )

    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="articleContent"]//h3//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="post-title"]//text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="post-summary"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return response.xpath('//span[@class="post-subinfo"]//span//text()').re_first("\d{1,2}/\d{1,2}/\d{4}")

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = int(response.meta.get("current_page")) + 1
        if response.status !=200:
            return
        return str(next_page)
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        next_page = self.get_next_page(response)
        if next_page :
            url = f"{start_url}/p/{next_page}"
            yield scrapy.Request(
                url=url,
                callback = self.parse,
                meta = {
                    "start_url" : start_url,
                    "current_page" : next_page
                }
            )