from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class AtlanticCouncil(OCSpider):
    name = "AtlanticCouncil"

    country="US"

    start_urls_names = {
        "https://www.atlanticcouncil.org/category/news/page/1/" : "Press Releases",
    }

    charset = "utf-8"

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='gta-embed--content gta-post-embed--content']//a[contains(@href, 'https://www.atlanticcouncil.org/news/')]//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h2//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//article[@class='ac-single-post--content']/p//text()").getall())

    def get_images(self, response, entry=None) :
        return response.xpath("//article[@class='ac-single-post--content']//img//@src").getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return response.xpath("//div[@class='gta-site-banner--column gta-post-site-banner--column']//p//span[@class='gta-embed--tax--expert gta-post-embed--tax--expert']//text()").getall()

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//p[@class='gta-site-banner--heading--date gta-post-site-banner--heading--date']//text() | //span[@class='ac-single-post--marquee--heading--date']//text()").get()
    
    def get_page_flag(self) -> bool:
        return False
 
    def get_next_page(self, response) -> str:
        if not self.get_articles(response):
            return None
        url = response.url.rstrip("/")
        base_url, current_page = (url.rsplit("/page/", 1) + [None])[:2]
        try:
            current_page = int(current_page)
        except (TypeError, ValueError):
            base_url, current_page = url, 1
        return f"{base_url}/page/{current_page + 1}/"