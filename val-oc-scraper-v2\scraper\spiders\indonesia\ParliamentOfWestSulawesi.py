from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfWestSulawesi(OCSpider):
    name = "ParliamentOfWestSulawesi"

    start_urls_names = {
        "https://dprd.sulbarprov.go.id/category/berita-utama/": "ALLNEWS",
        "https://dprd.sulbarprov.go.id/category/foto/" : "FOTO",
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
        
    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }
    
    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="post_info"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="entry-title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="post-wrap"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="post-wrap"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%b %d, %Y"
    
    def get_date(self, response) -> str:
        raw_date = response.xpath('//span[@class="updated"]//text()').get().strip()
        for indo, eng in self.indonesian_to_english.items():
                raw_date = raw_date.replace(indo, eng)
        return raw_date
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None