import json
import scrapy.selector
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaAssociationOfShippingAgencies(OCSpider):
    name = "ChinaAssociationOfShippingAgencies"

    start_urls_names = {
        "http://www.casa.org.cn/col/col18045/index.html" : "近期要闻",                          
        "http://www.casa.org.cn/col/col18046/index.html" : "上海办事处直通车",
        "http://www.casa.org.cn/col/col18048/index.html" : "广州办事处直通车", 
        "http://www.casa.org.cn/col/col18050/index.html" : "热点聚焦",                          
        "http://www.casa.org.cn/col/col18051/index.html" : "政策看台",
        "http://www.casa.org.cn/col/col18053/index.html" : "船东资讯"
    }

    charset = "utf-8"

    custom_settings = {
        "DOWNLOAD_DELAY": 7,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    article_to_date_mapping = {}  # Variable to store the article URL to date mapping

    api_start_urls = {
        "http://www.casa.org.cn/col/col18045/index.html": {
            "url" : "http://www.casa.org.cn/api-gateway/jpaas-publish-server/front/page/build/unit?webId=132&pageId=18045&parseType=bulidstatic&pageType=column&tagId=%E5%85%AC%E5%91%8A%E6%A0%8F1&tplSetId=3CFuv9eT416L1LodJ0MPR&paramJson=%7B%22pageNo%22%3A2%2C%22pageSize%22%3A%2215%22%7D",
            "payload": {
                "webId": "132",
                "pageId": "18045",
                "parseType": "bulidstatic",
                "pageType": "column",
                "tagId": "公告栏1",
                "tplSetId": "3CFuv9eT416L1LodJ0MPR",
                "paramJson": {
                    "pageNo":"1",
                    "pageSize":"15"
                    }
            },
        },
         "http://www.casa.org.cn/col/col18046/index.html": {
            "url" : "http://www.casa.org.cn/api-gateway/jpaas-publish-server/front/page/build/unit?webId=132&pageId=18046&parseType=bulidstatic&pageType=column&tagId=%E5%85%AC%E5%91%8A%E6%A0%8F1&tplSetId=3CFuv9eT416L1LodJ0MPR&paramJson=%7B%22pageNo%22%3A2%2C%22pageSize%22%3A%2215%22%7D",
            "payload": {
                "webId": "132",
                "pageId": "18046",
                "parseType": "bulidstatic",
                "pageType": "column",
                "tagId": "公告栏1",
                "tplSetId": "3CFuv9eT416L1LodJ0MPR",
                "paramJson": {
                    "pageNo":"1",
                    "pageSize":"15"
                    }
            },
        },
         "http://www.casa.org.cn/col/col18048/index.html": {
        },
         "http://www.casa.org.cn/col/col18050/index.html": {
            "url" : "http://www.casa.org.cn/api-gateway/jpaas-publish-server/front/page/build/unit?webId=132&pageId=18050&parseType=bulidstatic&pageType=column&tagId=%E5%85%AC%E5%91%8A%E6%A0%8F1&tplSetId=3CFuv9eT416L1LodJ0MPR&paramJson=%7B%22pageNo%22%3A2%2C%22pageSize%22%3A%2215%22%7D",
            "payload": {
                "webId": "132",
                "pageId": "18050",
                "parseType": "bulidstatic",
                "pageType": "column",
                "tagId": "公告栏1",
                "tplSetId": "3CFuv9eT416L1LodJ0MPR",
                "paramJson": {
                    "pageNo":"1",
                    "pageSize":"15"
                    }
            },
        },
         "http://www.casa.org.cn/col/col18051/index.html": {
            "url": "http://www.casa.org.cn/api-gateway/jpaas-publish-server/front/page/build/unit?webId=132&pageId=18051&parseType=bulidstatic&pageType=column&tagId=%E5%85%AC%E5%91%8A%E6%A0%8F1&tplSetId=3CFuv9eT416L1LodJ0MPR&paramJson=%7B%22pageNo%22%3A2%2C%22pageSize%22%3A%2215%22%7D",
            "payload": {
                "webId": "132",
                "pageId": "18051",
                "parseType": "bulidstatic",
                "pageType": "column",
                "tagId": "公告栏1",
                "tplSetId": "3CFuv9eT416L1LodJ0MPR",
                "paramJson": {
                    "pageNo":"1",
                    "pageSize":"15"
                    }
            },
        },
         "http://www.casa.org.cn/col/col18053/index.html": {
            "url": "http://www.casa.org.cn/api-gateway/jpaas-publish-server/front/page/build/unit?webId=132&pageId=18053&parseType=bulidstatic&pageType=column&tagId=%E5%85%AC%E5%91%8A%E6%A0%8F1&tplSetId=3CFuv9eT416L1LodJ0MPR&paramJson=%7B%22pageNo%22%3A2%2C%22pageSize%22%3A%2215%22%7D",
            "payload": {
                "webId": "132",
                "pageId": "18053",
                "parseType": "bulidstatic",
                "pageType": "column",
                "tagId": "公告栏1",
                "tplSetId": "3CFuv9eT416L1LodJ0MPR",
                "paramJson": {
                    "pageNo":"1",
                    "pageSize":"15"
                    }
            },
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data=self.api_start_urls.get(start_url)
        if not api_data:
            hbp = HeadlessBrowserProxy()
            request = scrapy.Request(hbp.get_proxy(response.url, timeout=30000), callback=self.parse)
            request.meta['start_url'] = response.request.meta['start_url']
            yield request
        else:
            payload = api_data["payload"]
            pageId = payload.get("pageId")
            pageno=payload.get("paramJson").get("pageNo")
            pageSize=payload.get("paramJson").get("pageSize")
            url=f"http://www.casa.org.cn/api-gateway/jpaas-publish-server/front/page/build/unit?webId=132&pageId={pageId}&parseType=bulidstatic&pageType=column&tagId=%E5%85%AC%E5%91%8A%E6%A0%8F1&tplSetId=3CFuv9eT416L1LodJ0MPR&paramJson=%7B%22pageNo%22%3A{pageno}%2C%22pageSize%22%3A%2215%22%7D"
            yield scrapy.Request(
                url = url,
                method = "GET",
                headers={
                    'Content-Type': 'application/json',
                },
                body = json.dumps(payload),
                callback = self.parse,   
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "pageId": pageId,
                    "currentpage":pageno,
                    "max_page":pageSize
                },
            )
        
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
        
    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response):
        start_url = response.meta.get("start_url")
        article_urls=[]
        api_data=self.api_start_urls.get(start_url)
        hbp = HeadlessBrowserProxy()
        mapping={}
        if not api_data:
            for item in response.xpath('//*[@id="公告栏1"]/div[1]//tbody/tr[1]'):
                url=item.xpath('.//a/@href').get()
                full_url=f"http://www.casa.org.cn{url}"
                date=item.xpath('.//td[3]/text()').get()
                if date:
                    date=date.strip()
                if  url and date:
                    article_urls.append(full_url)
                    mapping[full_url] = date
            self.article_to_date_mapping.update(mapping)
        else:
            # Mapping url with dates as there are no Dates in child article
            data = json.loads(response.text)
            html = data.get('data',{}).get('html',{})
            selector=scrapy.Selector(text=html)   # Using scrapy Selector to get content from html
            for data in selector.xpath('//tr'):
                    url=data.xpath('.//a/@href').get()
                    fullurl=hbp.get_proxy(f"http://www.casa.org.cn{url}",timeout=0000)
                    date=data.xpath('.//td[@width="100"]//text()').get()
                    if date:
                        date=date.strip()
                    if  fullurl and date:
                        article_urls.append(fullurl)
                        mapping[fullurl] = date
            self.article_to_date_mapping.update(mapping)
        return article_urls

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//td[@class="title"]/b//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="zoom"]//p//text()').getall())
    
    def get_images(self, response, entry=None):
        return response.xpath('//div[@id="zoom"]//img/@src').getall()
    
    def get_document_urls(self, response, entry=None):
        return response.xpath('//td[@class="bt_content"]//a/@href').getall()
    
    def get_authors(self, response):
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self,response):
        article_url = response.url
        article_date = self.article_to_date_mapping.get(article_url, None)
        if article_date:
            return article_date
        else:
            return None

    def get_page_flag(self):
        return False
    
    def get_next_page(self, response,current_page ):
        current_page = int(response.meta.get('currentpage'))+1
        max_pages=int(response.request.meta['max_page'])
        return str(current_page) if current_page<max_pages else None
    
    def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.info("API URL not found in meta data.")
            return None
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload["paramJson"]["pageNo"] = next_page
            request = response.request.replace(
                url=api_url,
                callback=self.parse_intermediate   
            )
            request.meta.update({
                'start_url': start_url,
                'current_page': str(int(current_page) + 1),
                'api_url': response.meta.get('api_url'),
            })
            yield request
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")