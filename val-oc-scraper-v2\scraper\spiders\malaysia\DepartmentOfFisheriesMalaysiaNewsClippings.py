from scraper.OCSpider import OCSpider
import scrapy
import re

class DepartmentOfFisheriesMalaysiaNewsClippings(OCSpider):
    name = "DepartmentOfFisheriesMalaysiaNewsClippings"

    start_urls_names = {
        "https://www.dof.gov.my/en/resources/newspaper-clippings/": "News Clippings"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.dof.gov.my/en/resources/newspaper-clippings/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='et_pb_row_inner et_pb_row_inner_0_tb_body']//tbody//tr//a//@href").getall()
            
    def get_href(self, entry) -> str:
        return entry
    
    # def get_title(self, response) -> str:
    #     return response.xpath("//div[@id='footable_parent_295915']//table//tbody//tr/td/text()").get()

    def get_title(self, response) -> str:
        if "text/html" in response.headers.get("Content-Type", b"").decode():
            return response.xpath("//div[@id='footable_parent_295915']//table//tbody//tr/td//text()").get(default="").strip()
        return response.url.split("/")[-1]  # fallback to filename if it's a PDF


    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> str:
        match = re.search(r'(\d{2})-(\w+)-(\d{4})', response.url)
        if match:
            day, month, year = match.groups()
            month_map = {
                'Januari': 'Jan', 'Februari': 'Feb', 'Mac': 'Mar', 'April': 'Apr',
                'Mei': 'May', 'Jun': 'Jun', 'Julai': 'Jul', 'Ogos': 'Aug',
                'September': 'Sep', 'Oktober': 'Oct', 'November': 'Nov', 'Disember': 'Dec',
                'JAN': 'Jan', 'FEB': 'Feb', 'MAC': 'Mar', 'APR': 'Apr', 'MEI': 'May', 'JUN': 'Jun',
                'JULAI': 'Jul', 'OGOS': 'Aug', 'SEPTEMBER': 'Sep', 'OKTOBER': 'Oct',
                'NOVEMBER': 'Nov', 'DISEMBER': 'Dec'
            }
            month_norm = month.capitalize()
            month_eng = month_map.get(month_norm, month.capitalize())
            return f"{day} {month_eng} {year}"
        return ""

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None