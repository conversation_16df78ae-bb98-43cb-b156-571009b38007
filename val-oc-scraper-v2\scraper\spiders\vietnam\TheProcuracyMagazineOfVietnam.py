from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class TheProcuracyMagazineOfVietnam(OCSpider):
    name = "TheProcuracyMagazineOfVietnam"

    start_urls_names = {
        "https://kiemsat.vn/su-kien-van-de": "News",
        } 

    start_urls_with_no_pagination_set = {
        "https://kiemsat.vn/su-kien-van-de"  # Pagination is not suported
    }  

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//div[@class="detail"]//a[@class="title"]//@href').getall()
     
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="noidung"]//p//text()').getall())
   
    def get_images(self, response) -> list:
        return response.xpath('//table//td//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//div[@class="time"]//text()').get()
        if date :
            date = date.replace(" - ", " ").strip()
        dt = datetime.strptime(date, "%d/%m/%Y %H:%M")
        formatted = dt.strftime("%Y-%m-%d")
        return formatted
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None)->list:
        return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None