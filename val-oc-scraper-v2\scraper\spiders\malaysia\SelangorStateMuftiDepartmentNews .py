from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urlparse

class SelangorStateMuftiDepartmentNews(OCSpider):
    name = "SelangorStateMuftiDepartmentNews"

    start_urls_names = {
        'https://www.muftiselangor.gov.my/berita/': 'ministry'
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = 'Malaysia'

    @property
    def language(self): 
        return "Malay"
    
    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="elementor-post__text"]//a[@class="elementor-post__read-more"]/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization( response.xpath('//div[@class="entry-content clear"]//p/text()').getall())

    def get_date(self, response) -> str:
        try:
            parsed_url = urlparse(response.url)
            parts = parsed_url.path.split('/')
            if len(parts) >= 4:
                year, month, day = parts[1:4]
                date_str = f"{year}-{month}-{day}"
                return date_str
        except Exception as e:
            self.logger(f"Error extracting date from URL {response.url}: {e}")
        return None

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[contains(@class, "ast-post-format-")]//img').getall()

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[@class="page-numbers next"]/@href').get()  