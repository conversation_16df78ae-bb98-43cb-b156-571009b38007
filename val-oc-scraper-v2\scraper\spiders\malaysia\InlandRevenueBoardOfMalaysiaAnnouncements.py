from typing import Optional
from scraper.OCSpider import OCSpider
from datetime import datetime

class InlandRevenueBoardOfMalaysiaAnnouncements(OCSpider):
    name = "InlandRevenueBoardOfMalaysiaAnnouncements"
    
    start_urls_names = {
        "https://www.hasil.gov.my/pengumuman/?tajuk=&page=1": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"
    
    article_data_map = {}

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='recordContainer']//tbody//tr"):
            url = article.xpath(".//td[2]//a//@href").get()
            title = article.xpath(".//td[2]//a//text()").get()
            date =article.xpath(".//td[1]//text()").get()
            if url and title and date:
                self.article_data_map[url]={"url":url, "title": title, "date": date}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response):
        month_map = {
            'JAN': 'January', 'JANUARI': 'January',
            'FEB': 'February', 'FEBRUARI': 'February',
            'MAC': 'March',  
            'APR': 'April', 'APRIL': 'April',
            'MEI': 'May',
            'JUN': 'June',
            'JUL': 'July', 'JULAI': 'July',
            'OGO': 'August',  
            'SEP': 'September', 'SEPTEMBER': 'September',
            'OKT': 'October', 'OKTOBER': 'October',
            'NOV': 'November', 'NOVEMBER': 'November',
            'DIS': 'December', 'DISEMBER': 'December'
        }
        raw_date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "").strip().upper()
        for malay_month, eng_month in month_map.items():
            if malay_month in raw_date:
                formatted_date = raw_date.replace(malay_month, eng_month)
                try:
                    date_obj = datetime.strptime(formatted_date, "%d %B %Y")
                    return date_obj.strftime("%d %B %Y")  
                except ValueError:
                    return formatted_date.title()  
        return raw_date.title()  
    
    def get_document_urls(self, response, entry=None) -> list:
        if '.pdf' in response.url:
            return [response.url]
        return []
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) -> Optional[str]:
        return response.xpath("//li[@class='page-item next']//a[@class='page-link']//@href").get()