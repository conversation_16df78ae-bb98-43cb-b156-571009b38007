from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import re
from datetime import datetime

class NewYorkStateDepartmentOfTransportation(OCSpider):
    name = 'NewYorkStateDepartmentOfTransportation'
    
    country = "US"
    
    start_urls_names = {
        "https://www.dot.ny.gov/news/press-releases/2025": "Press"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def parse_intermediate(self, response):
        all_articles = list(set(response.xpath("//table[@class='content-table']//td[1]//a[contains(@href,'/news/press-releases/')]//@href").getall()))
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url")
        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx,
                    'articles': all_articles,
                    'start_url': start_url
                },
                dont_filter=True
            )

    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title = response.xpath("//h5/text() | //h5//a//text()").get()
        if title:  # Some child articles can not be scraped as they are not redirecting to other page
            return title
    
    def get_body(self, response) -> str:
        body = body_normalization(response.xpath( "//td//p//text() | //td//ul//li//text() | //div//font//text()").getall())
        if body:  # Some child articles can not be scraped as they are not redirecting to other page
            return body
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%B %d, %Y'


    def get_date(self, response) -> str:
        url = response.url
        date_string = response.xpath("//h5/preceding::text()[1]").get(default="").strip()

        # Extract raw date if it exists
        raw_date = ""
        if date_string:
            parts = date_string.split(": ", 1)
            if len(parts) > 1:
                raw_date = parts[1].strip().strip(',')

        # Fallback to date from URL if needed
        if not raw_date or ',' not in raw_date:
            match = re.search(r'/(\d{4})-(\d{2})-(\d{2})', url)
            if match:
                try:
                    # This builds date from URL
                    date_obj = datetime.strptime(match.group(0).strip("/"), "%Y-%m-%d")
                    return date_obj.strftime(self.date_format())
                except ValueError:
                    return None  # Invalid date in URL

        # Try parsing the final raw_date with a year
        if raw_date:
            try:
                # Add year from URL if it's missing
                if ',' not in raw_date:
                    year_match = re.search(r'/(\d{4})-(\d{2})-(\d{2})', url)
                    if year_match:
                        raw_date += f", {year_match.group(1)}"

                date_obj = datetime.strptime(raw_date, self.date_format())
                return date_obj.strftime(self.date_format())
            except ValueError:
                return None  # Can't parse
        return None
        
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        start_url = response.meta.get('start_url', response.url)
        try:
            current_year_in_url = int(start_url.rstrip('/').split('/')[-1])
        except ValueError:
            return
        if response.status == 200:
            next_year = current_year_in_url - 1
            return next_year
        else:
            return None
        
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url", response.url)
        next_year = self.get_next_page(response)
        if start_url:
            next_page_url = f"https://www.dot.ny.gov/news/press-releases/{next_year}"
            yield scrapy.Request(
                url=next_page_url,
                callback=self.parse_intermediate,
                meta={
                    'start_url': next_page_url
                    },
                dont_filter=True
            )
        else:
            return None