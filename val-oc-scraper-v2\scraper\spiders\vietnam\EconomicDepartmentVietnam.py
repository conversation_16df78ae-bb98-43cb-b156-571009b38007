from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import json
import scrapy

class EconomicDepartmentVietnam(OCSpider):
    name = "EconomicDepartmentVietnam"

    country = "Vietnam"

    start_urls_names = {
        "https://kinhtetrunguong.vn/thoi-su" : " Thời sự",
    }
    
    api_start_urls = {
        "https://kinhtetrunguong.vn/thoi-su": {
            "url": "https://kinhtetrunguong.vn/?module=Content.Listing&moduleId=24&cmd=redraw&site=104765&url_mode=rewrite&submitFormId=24&moduleId=24&page=Article.News.list&site=104765",
            "payload": {
                "layout": "Content.Article.News.listType11",
                "filters": "",
                "orderBy": "publishTime DESC",
                "type": "Article.News",
                "pageNo": "1",
                "itemsPerPage": "12",
                "hasInnerActions": "0",
                "hasHeaderInnerActions": "0",
                "service": "Content.Article.selectAll",
                "widgetCode": "5b72a94b9218655475508114",
                "parentId": "14575359",
                "_entityTitle": "Tin tức",
                "categoryId": "14575359",
                "_entityTitleLCF": "tin tức",
                "widgetTemplateId": "60545b9851296c0ae956d5a7",
                "imageSizeRatio": "3:2",
                "hiddenReadMore": "1",
                "hiddenAuthor": "1",
                "inheritBlockParentId": "B60d6a9f1cb814",
                "page": "Article.News.list",
                "modulePosition": "21",
                "moduleParentId": "10",
                "keyword": "",
                "phpModuleName": "Content.Listing",
                "_startTime": "1755682877684",
                "_t": "1755683016904",
            }
        }
    }

    charset = "utf-8"

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"]
        current_page = response.meta.get("current_page",1)
        payload = api_data["payload"]
        payload["pageNo"] = str(current_page)
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            formdata = payload,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            }
        )

    def get_articles(self, response) -> list:
        links = response.xpath('//section[contains(@class,"section-list")]''//div[contains(@class,"article-title")]//a[@href]/@href').getall()
        unique_links = list(dict.fromkeys(links))
        final_links = [response.urljoin(h) for h in unique_links]
        return final_links

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="article-title common-title-detail title-detail title-lg"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article-content font-size-text clearfix common-content mb-20"]//text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        date_str = response.xpath('substring-before(substring-after(normalize-space(string(//span[@class="post-date left"])), ","), "|")').get()
        if date_str:
            date_str = date_str.replace('\xa0', ' ').strip()
        return date_str

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        next_page = int(current_page) + 1
        return str(next_page)
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        api_url = api_data["url"]
        next_page = self.get_next_page(response, current_page)
        if next_page :
                payload = api_data["payload"]
                payload["pageNo"] = next_page
                yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                formdata = payload,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
