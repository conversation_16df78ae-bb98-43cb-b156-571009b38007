import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import json

class LabuanFinancialServicesAuthorityPressReleases(OCSpider):
    name = "LabuanFinancialServicesAuthorityPressReleases"

    charset = "UTF-8"
    
    start_urls_names = {
        "https://www.labuanibfc.com/resources-events/media/press-releases" : "通知公告", 
    }

    api_start_urls = {
        'https://www.labuanibfc.com/resources-events/media/press-releases': {
            "url": "https://www.labuanibfc.com/data_molecule_source/contentMS_rmo.ashx",
            "payload": {
                "tid": "c0bc27de-8168-450a-b0bc-eef0aa6fec65",
                "idx": "0",
            },
        }
    }
   
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload["idx"]
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers={"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"},
            formdata=payload,
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
        )


    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    articles_to_date_map ={}

    def get_articles(self, response) -> list:
        articles =[]
        map={}
        payload = response.meta.get('payload')
        print(payload)
        tid = payload["tid"]
        page = payload["idx"]
        try :
            json_data = json.loads(response.text)
            for item in json_data.get('pagination', {}).get(tid, {}).get('pages',{}).get(page,[]):
                url = f"https://www.labuanibfc.com/{item['Page Address']}"
                title = item["Page Name"]
                date = item["Display Date"]
                if url and title and date :
                    articles.append(url)
                    map[url]={"title":title,"date":date}
            self.articles_to_date_map.update(map)
            return articles if articles else []
        except json.JSONDecodeError:
            self.logger.info(f"Non-JSON response for tid={tid}, page={page} — stopping pagination.")
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.articles_to_date_map.get(response.url,{}).get('title',{})

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='a-inner-text']/div//text()").getall())
        
    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response):
       return self.articles_to_date_map.get(response.url,{}).get('date',{})

    def get_authors(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def get_document_urls(self, response, entry=None)  -> list:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        payload = response.meta.get("payload")
        tid = payload["tid"]
        current_page = int(response.meta.get("current_page"))

        try:
            json_data = json.loads(response.text)
        except json.JSONDecodeError:
            self.logger.info(f"Non-JSON (HTML) response at page {current_page} — stopping pagination.")
            return None

        pages = json_data.get('pagination', {}).get(tid, {}).get('pages', {}).get(str(current_page), [])
        if not pages:
            self.logger.info(f"No articles found on page {current_page} — stopping pagination.")
            return None

        return str(current_page + 1)

    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        next_page = self.get_next_page(response)
        payload["idx"] = next_page
        if next_page:
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded;"},
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}") 