from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import scrapy
import json
from datetime import datetime

class MontanaDepartmentOfCommerce(OCSpider):
    name = 'MontanaDepartmentOfCommerce'
    
    country = "US"

    start_urls_names = {
        'https://commerce.mt.gov/News/': 'Montana'
    }
    
    api_start_url = {
        'https://commerce.mt.gov/News/':'https://commerce.mt.gov/News/news_source.json?_=1743411573145'
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_url = self.api_start_url.get(start_url)
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_url
                },
            )
        
    charset = "utf-8"
    
    @property
    def language(self):
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "US/Mountain"

    def get_articles(self, response) -> list:
        data = json.loads(response.text)
        return [item.get("link") for item in data if "link" in item]
 
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@style="padding: 10px; margin-top: 55px;"]//h1/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='news-article-container']//text()").getall())

    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date_text = response.xpath("//ul[@class='meta list-inline text-muted']//li[@class='date']/text()").get()
        if not date_text:
            return None
        date_text = date_text.strip()
        try:
            date_obj = datetime.strptime(date_text, self.date_format())
            return date_text
        except ValueError:
            try:
                date_obj = datetime.strptime(date_text, "%B %d %Y")
                return date_obj.strftime("%Y-%m-%d")
            except ValueError:
                return None

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No next page to scrape
        return None