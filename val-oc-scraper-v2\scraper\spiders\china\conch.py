from scraper.OCSpider import <PERSON>CSpider
from typing import List
import scrapy
import re
from scraper.utils.helper import body_normalization

class conch(OCSpider):
    name = "conch"

    article_data_map = {}  # Mapping Title, date and PDF from start URL with child articles

    custom_settings = {
    "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    start_urls_names = {
        "http://www.conch.cn/yjbg/list_lcid_5_page_1.html" : "海螺水泥",
        "http://www.conch.cn/yjbg/list_lcid_4_page_1.html":"海螺水泥",
        "http://www.conch.cn/ggjth/list_lcid_9_page_1.html":"海螺水泥",
        "http://www.conch.cn/ggjth/list_lcid_10_page_1.html":"海螺水泥",
        "http://www.conch.cn/xxgs/list_page_1.html":"海螺水泥",
        "http://www.conch.cn/NewsCop/list_page_1.html":"海螺水泥",
        "http://www.conch.cn/gsyw/list_page_1.html":"海螺水泥"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response):
        if "/xxgs/" in response.url:
            article_urls = response.xpath('//li/a[starts-with(@href, "/xxgs/info_itemid_")]/@href').getall()
            full_urls = [response.urljoin(url) for url in article_urls]
            for url in full_urls:
                if url not in self.article_data_map:
                    self.article_data_map[url] = {"title": None, "date": None, "pdf": []}
            return full_urls
        elif "/NewsCop/" in response.url:
            article_urls = response.xpath('//li/a[starts-with(@href, "/NewsCop/info_itemid_")]/@href').getall()
            full_urls = [response.urljoin(url) for url in article_urls]
            for url in full_urls:
                if url not in self.article_data_map:
                    self.article_data_map[url] = {"title": None, "date": None, "pdf": []}
            return full_urls
        elif "/gsyw/" in response.url:
            article_urls = response.xpath('//li/a[starts-with(@href, "/gsyw/info_itemid_")]/@href').getall()
            full_urls = [response.urljoin(url) for url in article_urls]
            for url in full_urls:
                if url not in self.article_data_map:
                    self.article_data_map[url] = {"title": None, "date": None, "pdf": []}
            return full_urls
        else:
            self.extract_articles_with_dates(response)
            return list(self.article_data_map.keys())

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        entry = response.request.meta.get('entry')
        if entry and entry in self.article_data_map and self.article_data_map[entry].get("title"):
            return self.article_data_map[entry]["title"]
        title = response.xpath('//div[@class="tit3"]/h3/font/font/text()').get()
        if not title:
            title = response.xpath('//div[@class="tit3"]/h3/text()').get()
        if entry and entry in self.article_data_map:
            self.article_data_map[entry]["title"] = title
        return title if title else ""

    def get_body(self, response) -> str:
        body = response.xpath('//div[@class="cont"]//text()').getall()
        if body:
            body = ' '.join(body).strip()
        else:
            body = ""
        return body

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> List[str]:
        entry = response.request.meta.get('entry')
        if entry and entry in self.article_data_map:
            return self.article_data_map[entry].get("pdf", [])
        return []

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        entry = response.request.meta.get('entry')
        if entry and entry in self.article_data_map and self.article_data_map[entry].get("date"):
            return self.article_data_map[entry]["date"]
        paragraphs = response.xpath('//p//text()').getall()
        for text in paragraphs:
            match = re.search(r'(发布时间|Release\s*time)[:：]?\s*(\d{4}-\d{2}-\d{2})', text)
            if match:
                date_text = match.group(2) 
                if entry and entry in self.article_data_map:
                    self.article_data_map[entry]["date"] = date_text
                return date_text
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        current_url = response.url
        match = re.search(r'page_(\d+)', current_url)
        if '/xxgs/' in current_url:
            next_page_link = response.xpath("//a[@class='next']/@href").get()
            if next_page_link:
                return response.urljoin(next_page_link)
            return None
        elif '/NewsCop/' in current_url:
            next_page_link = response.xpath("//a[@class='next']/@href").get()
            if next_page_link:
                return response.urljoin(next_page_link)
            return None
        elif '/gsyw/' in current_url:
            next_page_link = response.xpath("//a[@class='next']/@href").get()
            if next_page_link:
                return response.urljoin(next_page_link)
            return None
        elif match:
            current_page = int(match.group(1))
            next_page = current_page + 1
            items = response.xpath("//ul[@class='list']//li")
            if not items:
                return None
            if 'lcid_5' in current_url:
                next_page_url = f"/yjbg/list_lcid_5_page_{next_page}.html"
            elif 'lcid_4' in current_url:
                next_page_url = f"/yjbg/list_lcid_4_page_{next_page}.html"
            elif 'lcid_9' in current_url:
                next_page_url = f"/ggjth/list_lcid_9_page_{next_page}.html"
            elif 'lcid_10' in current_url:
                next_page_url = f"/ggjth/list_lcid_10_page_{next_page}.html"
            else:
                return None
            return response.urljoin(next_page_url)
        return None
    
    def extract_articles_with_dates(self, response):
        articles = response.xpath("//ul[@class='list']//li") 
        for article in articles:
            full_url = None 
            onclick_value = article.xpath(".//a/@onclick").get()
            if onclick_value and onclick_value.startswith("show('"):
                try:
                    match = re.search(r"show\('([^']+)'\)", onclick_value)
                    if match:
                        url = match.group(1)
                        pdf_path = url.strip()
                        clean_pdf_url = response.urljoin(f"/uploadfiles/{pdf_path}")
                        self.article_data_map[clean_pdf_url] = {
                            "title": title.strip(),
                            "date": clean_date,
                            "pdf": [clean_pdf_url] if clean_pdf_url.lower().endswith(".pdf") else []
                        }
                except Exception:
                    continue  
            title = article.xpath(".//a/text()").get()
            date_text = article.xpath(".//span/text()").get()
            if full_url and title and date_text:
                clean_date = date_text.strip()
                self.article_data_map[full_url] = {
                    "title": title.strip(),
                    "date": clean_date,
                    "pdf": [full_url] if full_url.lower().endswith(".pdf") else []
                }