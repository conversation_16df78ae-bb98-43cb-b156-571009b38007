from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization

class IndianaEconomicDevelopmentCorporation(OCSpider):
    name = 'IndianaEconomicDevelopmentCorporation'

    country = "US"

    start_urls_names = {
        "https://www.iedc.in.gov/events/news": "Press Releases",
    }

    api_start_urls = {
        'https://www.iedc.in.gov/events/news': {
            "url": "https://www.iedc.in.gov/events/news/Paging/",
            "payload": {
                "tag": "00000000-0000-0000-0000-000000000000",
                "page": "1"  
            }
        }
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload["page"]
        payload["page"] = str(current_page)
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers={"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"},
            formdata=payload,
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
        )

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "America/Chicago"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return list(set(response.xpath("//h2//a//@href").getall()))
              
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='story-long-head']//h2//text() | //div[@class='story-head']//h2[@class='copy title']//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='copy body']//p//text() | //div[@class='copy body']//ul//li//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='story-long-head']//div[@class='copy date']//text() | //div[@class='story-head']//div[@class='copy date']//text()").get().strip()

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        current_page = int(response.meta.get("current_page"))
        if current_page:
            next_page = current_page + 1
            return str(next_page)  
        else:
            return None

    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        next_page = self.get_next_page(response)
        if next_page:
            payload["page"] = str(next_page)  
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"},
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
        else:
           return