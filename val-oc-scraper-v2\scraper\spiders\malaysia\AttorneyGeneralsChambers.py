from typing import Optional
from scraper.OCSpider import OCSpider

class AttorneyGeneralsChambers(OCSpider):
    name = "AttorneyGeneralsChambers"

    start_urls_names = {
        "https://www.agc.gov.my/agcportal/frontend/web/index.php?r=portal%2Fleft-list&menu=WTc4b0VCbWUzcUtIS1pPRTlZOUJSZz09&id=cCtDMkExYWFaK2lEL21nbkRkYzFtUT09": "News"
    }
    
    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='mb-20']//a"):
            url = article.xpath(".//@href").get()
            title = article.xpath(".//h5/text()").get()
            date = article.xpath("//div[@class='mb-20']//li[i[contains(@class, 'fa-calendar')]]/text()").get()
            if url and title and date:
                self.article_data_map[url] = {"date":date.strip(), "title" : title}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath("//li[contains(@class, 'next-item')]//a/@href").get()
        if next_page:
            return response.urljoin(next_page)
        return None