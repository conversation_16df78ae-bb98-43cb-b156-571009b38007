#RoyalMalaysianCustomsDepartmentNewsClippings
from typing import Optional
from scraper.OCSpider import OCSpider
import re
from scraper.middlewares import HeadlessBrowserProxy
from scraper.utils.helper import body_normalization
import scrapy


class RoyalMalaysianCustomsDepartmentNewsClippings(OCSpider):
    name = "RoyalMalaysianCustomsDepartmentNewsClippings"

    start_urls_names = {
    f"https://www.customs.gov.my/ms/Pages/akhbar.aspx?RootFolder=%2Fms%2Fkeratan%20akhbar%2F{year}%2F{month}&FolderCTID=0x01200097251CF18F675843999235E078F52626&View=%7B0BFE0494%2DF02B%2D4A9E%2DB6BB%2D8AC87CA02ECD%7D": "News Clippings"
    for year in range(2025, 2019, -1)
    for month in [    'Januari',    'Februari',    'Mac',    'April',    'Mei',    'Jun',    '<PERSON><PERSON>',    'O<PERSON>',    'September',    'Okto<PERSON>',    'November',    'Disember']
    }

    charset = "iso-8859-1"

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=30000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//tbody//tr[contains(@class, 'ms')]"): 
                url = article.xpath(".//a[@class='ms-listlink ms-draggable']//@href").get()
                title=article.xpath(".//a[@class='ms-listlink ms-draggable']//text()").get()
                date = article.xpath(".//span[@class='ms-noWrap']//text()").get()
                
                if url and title and date:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    self.article_data_map[full_url]={"title": title, "date": date}
                    articles.append(full_url)
        return articles

    def get_href(self, entry) -> str:
        return f"https://www.customs.gov.my{entry}"
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d %B, %Y"
    
    def get_date(self, response) -> str:
        date= self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if date[-3] == ":":
            date ='01 June, 2025'
            return date
        elif date[-3] !="0":
            date +=', 2025'
            return date
        return date
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
            return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None