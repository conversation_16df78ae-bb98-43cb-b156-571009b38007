from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import json
from datetime import datetime
import re


class YTOExpress(OCSpider):
    name = "YTOExpress"
    

    start_urls_names = {
        "https://www.yto.net.cn/about/news/ytonews.html/": "",
    }

    api_start_urls = {
        "https://www.yto.net.cn/about/news/ytonews.html/": {
            "url": "https://www.yto.net.cn/api/news/bytype",
            "payload": {
                "typeKey": "6e39e013be400d5130a2d8d30bcbedb3",
                "page": "1",
                "pageSize": "10",
                "total": "1903",
            }
        }
    }

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
        "Referer": "https://www.yto.net.cn/about/news/ytonews.html/",
        "Accept": "application/json, text/plain, */*",
        "X-Requested-With": "XMLHttpRequest",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)

        if not api_data:
            self.logger.error("No API data found for start_url")
            return

        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]

        self.logger.debug(f"[DEBUG] Sending API POST with payload: {payload}")

        yield scrapy.FormRequest(
            url=api_url,
            formdata=payload,  # ✅ Correct way to send form data
            method="POST",
            headers={
                **self.headers,
                "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",  # ✅ correct
            },
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["page"]
            }
        )


    import re

    def get_articles(self, response) -> list:
        self.logger.debug(f"[DEBUG] Response status: {response.status}")
        self.logger.debug(f"[DEBUG] Response text (first 500 chars): {response.text[:10000]}")

        # Extract JSON from inside the HTML <body>...</body>
        try:
            match = re.search(r'<body>(.*?)</body>', response.text, re.DOTALL)
            if match:
                embedded_json = match.group(1).strip()
                data = json.loads(embedded_json)
                self.logger.debug(f"[DEBUG] Parsed embedded JSON: {data}")
                articles = data.get("data", [])
                return articles
            else:
                self.logger.error("[ERROR] Failed to extract JSON from HTML body.")
                return []
        except json.JSONDecodeError as e:
            self.logger.error(f"[ERROR] JSON decoding error: {e}")
            return []



    def get_href(self, entry) -> str:
        if isinstance(entry, dict):
            article_id = entry.get("id")
            if article_id:
                return f"https://www.yto.net.cn/about/news/ytonews/detail.html/?id={article_id}"
        return ""

    def get_title(self, response) -> str:
        title = response.xpath('//p[@class="title-return"]/span/text()').get()
        subtitle = response.xpath('//p[@class="subtitle-name"]/text()').get()
        return f"{title} - {subtitle}" if subtitle else (title or "")

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article"]//text()').getall())

    def get_images(self, response) -> list:
        image_urls = response.xpath('//div[@class="article"]//img/@src').getall()
        return [response.urljoin(url) for url in image_urls]

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        article = response.meta.get("article", {})

        timestamp_ms = article.get("createTime")
        if timestamp_ms:
            try:
                timestamp_s = int(timestamp_ms) / 1000
                return datetime.fromtimestamp(timestamp_s).strftime(self.date_format())
            except Exception as e:
                self.logger.warning(f"[WARNING] Failed to parse createTime: {timestamp_ms} - {e}")

        api_date = article.get("createTimeFormat")
        if api_date:
            try:
                datetime.strptime(api_date.strip(), self.date_format())
                return api_date.strip()
            except Exception as e:
                self.logger.warning(f"[WARNING] createTimeFormat invalid: {api_date} - {e}")

        html_date = response.xpath('//p[@class="release-time"]/text()').get()
        if html_date:
            try:
                datetime.strptime(html_date.strip(), self.date_format())
                return html_date.strip()
            except Exception as e:
                self.logger.warning(f"[WARNING] HTML date invalid: {html_date} - {e}")

        return datetime.now().strftime(self.date_format())  # fallback

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return int(current_page) + 1

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        api_url = api_data["url"]
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)
        payload["page"] = str(next_page)

        self.logger.info(f"Going to Page {next_page}")

        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            formdata=payload,
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            }
        )

    def construct_article_url(self, article: dict) -> str:
        return f"https://www.yto.net.cn/about/news/ytonews/detail.html/?id={article.get('id')}"

    def get_already_scraped_articles(self, hrefs):
        clean_hrefs = []
        for entry in hrefs:
            if isinstance(entry, dict):
                article_id = entry.get("id")
                if article_id:
                    clean_hrefs.append(f"https://www.yto.net.cn/about/news/ytonews/detail.html/?id={article_id}")
            elif isinstance(entry, str):
                clean_hrefs.append(entry)
        return super().get_already_scraped_articles(clean_hrefs)
