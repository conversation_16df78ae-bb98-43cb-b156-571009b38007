from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
from urllib.parse import urljoin
from datetime import datetime

class LegislatureResearchReview(OCSpider):
    name = "LegislatureResearchReview"

    start_urls_names = {
        "https://lapphap.vn/Pages/ChuyenMuc/3587//Nha-nuoc-va-phap-luat.html": "News"
    }

    start_urls_with_no_pagination_set = {}

    api_start_url = {
        "https://lapphap.vn/Pages/ChuyenMuc/3587//Nha-nuoc-va-phap-luat.html": {
        "url": "https://lapphap.vn/Pages/tintuc/loadds.aspx",
        "params": {
            "pageId": 1,
            "pageSize": 6,
            "totalPage": 5,
            "totalItem": 1430,
            "SubSite": ""
        },
        "data": {
            "IDChuyenMuc": '3587',
            "NgayThang": ""
            }
        }
    }


    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_<PERSON>_Minh"
    
    def parse_intermediate(self,response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            self.logger.error(f"No API config found for {start_url}")
            return
        params = api_data["params"]
        params["pageId"] = response.meta.get("current_page",params["pageId"])
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        full_url = f'{api_data["url"]}?{query_string}'

        yield scrapy.FormRequest(
        url=full_url,
        method="POST",
        formdata=api_data["data"],     
        callback=self.parse,
        meta={
            "start_url": start_url,
            "params": params,
            "api_data" : api_data,
            "data": api_data["data"],
            "current_page" : params["pageId"]
        }
    )
    
    articles_to_date = {}

    def get_articles(self, response) -> list:  
        articles = []
        map={}
        base_url = "https://lapphap.vn/"
        for node in response.xpath("//div[contains(@class, 'box-news-xx')]"):
            href = node.xpath(".//a/@href").get()
            title = node.xpath(".//h2[contains(@class, 'h2-title-xx')]/a/text()").get()
            raw_date = node.xpath(".//div[contains(@class, 'hot-news-tol')]/text()").get().strip()
            date = None
            if raw_date:
                raw_date = raw_date.replace("SA", "AM").replace("CH", "PM")
                try:
                    date = datetime.strptime(raw_date, "%d/%m/%Y %H:%M:%S %p").strftime("%d/%m/%Y")
                except ValueError:
                    # fallback in case date is not in expected format
                    date = raw_date  
            if href and title and date:
                full_url =urljoin(base_url,href)
                articles.append(full_url)
                map[full_url] = {'title': title,'date': date}
        self.articles_to_date.update(map)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.articles_to_date.get(response.url,{}).get('title',"")
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article"]//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return self.articles_to_date.get(response.url,{}).get('date',"")

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = (int(response.meta.get("current_page"))+1)
        if response.status !=200:
            return 
        return str(next_page)
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        api_data = response.meta.get("api_data")
        params = response.meta.get("params")
        data = response.meta.get("data")
        next_page = self.get_next_page(response)
        if next_page:
            params["pageId"] = next_page
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            full_url = f'{api_data["url"]}?{query_string}'

            yield scrapy.FormRequest(
            url=full_url,
            method="POST",
            formdata=data,     
            callback=self.parse,
            meta={
                "start_url": start_url,
                "params": params,
                "data": api_data["data"],
                "api_data": api_data,
                "current_page" : next_page
            }
        )

