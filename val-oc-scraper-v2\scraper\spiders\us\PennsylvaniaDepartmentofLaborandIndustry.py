from typing import Optional, List
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime
from scrapy import FormRequest
import scrapy

class PennsylvaniaDepartmentOfLaborAndIndustry(OCSpider):
    name = "PennsylvaniaDepartmentOfLaborAndIndustry"

    country = "US"

    start_urls_names = {
        "https://pacast.com/": "Press Release"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def parse_intermediate(self, response):
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "en-US,en;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded",
            "Host": "pacast.com",
            "Origin": "https://pacast.com",
            "Pragma": "no-cache",
            "Referer": "https://pacast.com/",
            "Sec-Ch-Ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }
        viewstate = response.xpath('//input[@name="__VIEWSTATE"]/@value').get()
        viewstategenerator = response.xpath('//input[@name="__VIEWSTATEGENERATOR"]/@value').get()
        eventvalidation = response.xpath('//input[@name="__EVENTVALIDATION"]/@value').get()
        if not (viewstate and viewstategenerator and eventvalidation):
            return
        form_data = {
            "__EVENTTARGET": "ctl00$MainContent$lvDataPager1$ctl02$ctl02",
            "__EVENTARGUMENT": "",
            "__VIEWSTATE": viewstate,
            "__VIEWSTATEGENERATOR": viewstategenerator,
            "__EVENTVALIDATION": eventvalidation,
        }
        yield FormRequest(
            url=response.url,
            formdata=form_data,
            callback=self.parse,
            headers=headers,
            meta={'start_url': response.url}
        )
        if self.get_page_flag():
            yield from self.get_next_page(response)

    def get_articles(self, response) -> List[str]:
        return response.xpath('//li/a[@class="button2" or @class="button3"]/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2/span/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p/span[@id="MainContent_ListView1_event_descriptionLabel_0"]//text()').getall()) 
        
    def get_images(self, response) -> List[str]:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//span[@id="MainContent_ListView1_event_dateLabel_0"]/text()').get()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y") 
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        current_articles = self.get_articles(response)
        if not current_articles:
            return
        next_page = response.xpath("//input[@value='>']/@name").get()
        if not next_page:
            return
        viewstate = response.xpath('//input[@name="__VIEWSTATE"]/@value').get()
        viewstategenerator = response.xpath('//input[@name="__VIEWSTATEGENERATOR"]/@value').get()
        eventvalidation = response.xpath('//input[@name="__EVENTVALIDATION"]/@value').get()
        if not (viewstate and viewstategenerator and eventvalidation):
            return
        current_article_urls = set(response.urljoin(url) for url in current_articles)
        seen_urls = response.meta.get('seen_article_urls', set())
        if current_article_urls.issubset(seen_urls):
            return
        seen_urls.update(current_article_urls)
        form_data = {
            "__EVENTTARGET": next_page,
            "__EVENTARGUMENT": "",
            "__VIEWSTATE": viewstate,
            "__VIEWSTATEGENERATOR": viewstategenerator,
            "__EVENTVALIDATION": eventvalidation,
        }
        yield scrapy.FormRequest(
            url=response.url,
            formdata=form_data,
            callback=self.parse_intermediate,
            meta={
                'start_url': response.request.meta.get('start_url', response.url),
                'seen_article_urls': seen_urls
            }
        )