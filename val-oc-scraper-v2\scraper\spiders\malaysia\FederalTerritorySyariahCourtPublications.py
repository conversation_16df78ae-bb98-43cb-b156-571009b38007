from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class FederalTerritorySyariahCourtPublications(OCSpider):
    name = "FederalTerritorySyariahCourtPublications"

    start_urls_names = {
        'https://www.mswp.gov.my/portal-main/publication-list': 'publication-list'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.mswp.gov.my/portal-main/publication-list'
    }

    charset = "utf-8"

    country ='Malaysia'

    @property
    def source_type(self) -> str:
        return 'justice'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    malay_months = {
        'Januari': 'January',
        'Februari': 'February', 
        'Mac': 'March',
        'April': 'April', 
        'Mei': 'May', 
        'Jun': 'June', 
        'Julai': 'July',
        'Ogos': 'August', 
        'September': 'September', 
        'Oktober': 'October',
        'November': 'November', 
        'Disember': 'December'
        }

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="entry-button"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="blog-entry mb-50"]/h5/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('string(//table[@class="table table-bordered"])').get())

    def get_date(self, response) -> str: 
        date_part = response.xpath('//div[@class="entry-meta mb-30"]//ul//li//text()').getall()[1]
        for malay , eng in self.malay_months.items():
             if malay in date_part:
                date = date_part.replace(malay, eng)
                break
        return date.strip()

    def date_format(self) -> str:
        return "%d %B %Y"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//a[@class="btn btn-danger btn-sm mr-2"]/@href').getall()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None