from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MinistryOfHealthActs(OCSpider):
    name = "MinistryOfHealthActs"

    start_urls_names = {
        "https://www.moh.gov.my/index.php/database_stores/store_view/11?mid=289": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
     
    def get_articles(self, response) -> list:  
        return response.xpath("//td[@class='thin']//a//@href").getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='database_store_page_view_div']//tr[1]//td//text()").get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath("//div[@class='database_store_page_view_div']//text()").getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        date =response.xpath("//div[@class='database_store_page_view_div']//tr[3]//td//text()").get() 
        if " " in date:
            date=date.split(" ")[-1]
        return date
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        pdfs=response.xpath("//div[@class='attachment']//a//@href").getall()
        pdf=[]
        for i in pdfs:
            if 'http' not in i:
                i = f'https://www.moh.gov.my{i}'
            else:
                i=i
            pdf.append(i)
        return pdf
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath("//div[@class='dataTables_paginate paging_simple_numbers']//a[text()='Next']//@href").get()