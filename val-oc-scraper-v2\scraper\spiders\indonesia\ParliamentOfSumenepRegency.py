from scraper.OCSpider import OCSpider

class ParliamentOfSumenepRegency(OCSpider):
    name = "ParliamentOfSumenepRegency"

    start_urls_names = {
        "https://dprd.sumenepkab.go.id/jdih-user": "PPID"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:   
        return response.xpath('//div[@class="post-content mb-5"]/div//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="post-header"]/h1//text()').get().strip()
    
    def get_body(self, response) -> str:
        #Only images
        return ""
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="classic-view"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%d %b %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//li[@class="post-date"]/span/text()').get()
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        #1 page only
        return None
        