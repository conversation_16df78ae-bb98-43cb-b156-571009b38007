
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class MalaysiaHighwayAuthorityNewsClippings(OCSpider):
    name = "MalaysiaHighwayAuthorityNewsClippings"

    start_urls_names = {
     f"https://www.llm.gov.my/announcement/newspaper/{i}": "News clipping"  #pagination not supportd 
     for i in range(0, 1490, 12)   
 }
     
    start_urls_with_no_pagination_set={
        "https://www.llm.gov.my/announcement/newspaper/"
      }
    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
     articles= []
     links = response.xpath('//a[@data-reveal-id]')

     for link in links:
      id_value = link.xpath('./@data-reveal-id').get()
      title = link.xpath('normalize-space(string())').get()
      date = link.xpath('./ancestor::td/following-sibling::td[1]/text()').get()
      if date:
         date = date.strip()
      img_src = response.xpath(f'//div[@id="{id_value}"]//img/@src').get()
      articles.append(img_src)
      self.article_data_map[img_src]={ "date":date,"title":title,"imgsrc":img_src}
     return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
     return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return [response.url]
       
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response) -> str:
       date= self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
       clean_date_str = date.strip() 
       date_obj = datetime.strptime(clean_date_str, "%d/%m/%y")  # Parse the cleaned string
       formatted_date = date_obj.strftime("%d-%m-%Y")
       return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 