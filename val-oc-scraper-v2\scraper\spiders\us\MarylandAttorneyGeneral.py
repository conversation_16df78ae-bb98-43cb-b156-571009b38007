import scrapy
from scraper.OCSpider import OCSpider
import json
from typing import List, Union
from datetime import datetime
from urllib.parse import urlencode

class MarylandAttorneyGeneral(OCSpider):
    name = 'MarylandAttorneyGeneral'
     
    start_urls_names = {
        'https://www.marylandattorneygeneral.gov/Pages/News/All-News.aspx': 'News',
    }
    
    country = "us"

    api_start_url = {
        'https://www.marylandattorneygeneral.gov/Pages/News/All-News.aspx': {
            'url': 'https://www.marylandattorneygeneral.gov/_layouts/15/inplview.aspx?List=%7B0D07A630-7217-4C4E-831D-6E62F27E5CE8%7D&View=%7BDD9A8149-55A6-4F60-8592-8F2A2FAF6965%7D&ViewCount=7&IsXslView=TRUE&IsCSR=TRUE&ListViewPageUrl=https%3A%2F%2Fwww.marylandattorneygeneral.gov%2FPages%2FNews%2FAll-News.aspx&FolderCTID=0x012001&GroupString=%3b%232025%3b%23&IsGroupRender=TRUE&WebPartID={DD9A8149-55A6-4F60-8592-8F2A2FAF6965}',
            'payload': {
                'List': '{0D07A630-7217-4C4E-831D-6E62F27E5CE8}',
                'View': '{DD9A8149-55A6-4F60-8592-8F2A2FAF6965}',
                'ViewCount': '4',
                'IsXslView': 'TRUE',
                'IsCSR': 'TRUE',
                'ListViewPageUrl': 'https://www.marylandattorneygeneral.gov/Pages/News/All-News.aspx',
                'FolderCTID': '0x012001',
                'GroupString': ';#2025;#',
                'IsGroupRender': 'TRUE',
                'WebPartID': '{DD9A8149-55A6-4F60-8592-8F2A2FAF6965}',
            }
        }
    } 

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_url.get(start_url)
        url = api_data["url"]
        for year in range(2015, 2026):
            payload = api_data["payload"].copy()
            payload["GroupString"] = f";#{year};#"
            encoded_group_string = urlencode({"GroupString": payload["GroupString"]}).split('=')[1]
            new_url = url.replace('%3b%232025%3b%23', encoded_group_string)
            yield scrapy.FormRequest(
                url=new_url,
                method="POST",
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"},
                callback=self.parse,
                dont_filter=True,
                meta={
                    "start_url": start_url,
                    "api_url": new_url,
                    "payload": payload,
                    "year": year,
                },
            )

    charset = "iso-8859-1"

    article_data_map = {}  # Mapping title, date and PDF with corresponding articles

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self): 
        return "US/Eastern"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            articles = data.get("Row", [])
            extracted_articles = set()
            for article in articles:
                url = article.get("Summary", "").strip()
                full_url = f'https://www.marylandattorneygeneral.gov{url}' if url else ""
                title = article.get("Summary.desc", "").strip()
                date = article.get("Release_x0020_Date", "").strip()
                if full_url:
                    self.article_data_map[full_url] = {
                        "title": title,
                        "date": date,
                        "pdf": [full_url]
                    }
                    extracted_articles.add(full_url)
            return list(extracted_articles)
        except json.JSONDecodeError as e:
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        entry = response.request.meta.get('entry')
        if entry and entry in self.article_data_map:
            return self.article_data_map[entry].get("title", "").strip()
        return ""

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> List[str]:
        return []

    def get_authors(self, response):
        return []

    def date_format(self) -> str:
        return '%m/%d/%Y'

    def get_date(self, response) -> str:
        entry = response.request.meta.get('entry')
        if entry and entry in self.article_data_map:
            return self.article_data_map[entry].get("date", "").strip()
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        if entry in self.article_data_map:
            return self.article_data_map[entry].get("pdf", "")
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Union[None, str]:
        try:
            data = json.loads(response.text)
            next_page = data.get("Row", [])
            if next_page:
                last_entry = next_page[-1]
                p_ID = last_entry.get("ID", "").strip()
                p_Release_x0020_Date = last_entry.get("Release_x0020_Date", "").strip()
                formatted_date = datetime.strptime(p_Release_x0020_Date, '%m/%d/%Y').strftime('%Y%m%d')
                p_Title = last_entry.get("Title", "").strip()
                PageFirstRow = data.get("FirstRow", "")
                Row = str(int(PageFirstRow)+30)    
                api_url = response.meta.get("api_url")
                if api_url:
                    for i in range(2015,2026):
                        next_page_url = (
                        f"{api_url}&Paged=TRUE&p_Title={p_Title}&p_Release_x0020_Date={formatted_date}%2005%3a00%3a00&p_ID={p_ID}&PageFirstRow={Row}"
                    )
                    return next_page_url
            return None
        except Exception as e:
            return None

    def go_to_next_page(self, response, start_url=None, current_page=None):
        next_page_url = self.get_next_page(response)
        if next_page_url:
            payload = response.meta.get("payload", {})
            yield scrapy.FormRequest(
                url=next_page_url,
                method='POST',
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                callback=self.parse,  
                meta={
                    **response.meta,
                    "current_page": current_page,
                    "payload": payload,  
                },
                dont_filter=True  
            )