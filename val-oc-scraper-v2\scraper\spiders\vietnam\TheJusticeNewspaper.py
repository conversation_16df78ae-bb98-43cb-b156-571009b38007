#VietnamAcademyofScienceandTechnology
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import dateparser
import scrapy
import re
from scraper.middlewares import HeadlessBrowserProxy
from datetime import datetime

class TheJusticeNewspaper(OCSpider):
    name = "TheJusticeNewspaper"

    start_urls_names = {
        "https://congly.vn/phap-luat": "News",
        }   #Check once if pagination is working then leave it if not then create variable page no 

    charset = "iso-8859-1"

    country = "Vietnam"

    # custom_settings = {
    #       "DOWNLOADER_MIDDLEWARES": {
    #           'scraper.middlewares.HeadlessBrowserProxy': 100
    #       },
    #       #"DOWNLOAD_DELAY": 5,
    #       "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    #   }
    
    # HEADLESS_BROWSER_WAIT_TIME = 700

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('/html/body/div[6]/div/div/div[10]/div[1]/div/div[2]/div[1]/ul/li/div/div[2]/div[1]/h3/a//@href').getall()
       
     
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('/html/body/div[6]/div/div/div/div[1]/div[2]/div[1]/div/p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('/html/body/div[6]/div/div/div/div[1]/div[2]/div/div/figure/img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//span[@class="sc-longform-header-date block-sc-publish-time"]//text()').get()
        #print(date,"Date is ================================")
        date = date.replace(" - ", " ")
        clean_date = date.strip()   # "26/02/2024"
        dt = datetime.strptime(clean_date, "%d/%m/%Y %H:%M")
        formatted = dt.strftime("%Y-%m-%d")
        return formatted
        

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None