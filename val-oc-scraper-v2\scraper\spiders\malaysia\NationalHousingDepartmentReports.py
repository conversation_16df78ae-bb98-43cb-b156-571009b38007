from scraper.OCSpider import <PERSON>CSpider
from typing import Optional
from urllib.parse import urljoin
from dateutil import parser
import re

class NationalHousingDepartmentReports(OCSpider):
    name = "NationalHousingDepartmentReports"

    country = "Malaysia"
    
    start_urls_names = {
        "https://ehome.kpkt.gov.my/index.php/pages/view/36?mid=344": "Perangkaan Perumahan"
    }

    start_urls_with_no_pagination_set = {
        "https://ehome.kpkt.gov.my/index.php/pages/view/36?mid=344"
    }   

    article_data_map = {}

    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_date_map = {}
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return list(self.article_data_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        url = response.url
        article_info = self.article_data_map.get(url)
        if article_info and 'title' in article_info:
            return article_info['title'].strip()

    def get_body(self, response) -> str:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        if "application/pdf" in content_type:
            return "[PDF content not extracted]"
        try:
            return response.text
        except AttributeError:
            return ""

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response) -> Optional[str]:
        article_url = response.url
        article_info = self.article_data_map.get(article_url)   
        if not article_info:
            return None   
        raw_date = article_info.get("date")
        if not raw_date:
            return None
        try:
            date_obj = parser.parse(raw_date.strip(), dayfirst=True)
            return date_obj.strftime(self.date_format())
        except (ValueError, TypeError):
            return None

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        article_info = self.article_data_map.get(response.url)
        if article_info and "pdf" in article_info:
            return article_info["pdf"]
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None

    def extract_articles_with_dates(self, response):
        base_url = response.url
        mapping = {}
        links = response.xpath('//div[@class="accordion_header group"]//a')
        if not links:
            return mapping
        for link in links:
            title = link.xpath('normalize-space(string())').get()
            href = link.xpath('./@href').get()
            if href and title:
                full_url = urljoin(base_url, href.strip())
                year_match = re.search(r'(\d{4})', title)
                year = year_match.group(1) if year_match else None
                mapping[full_url] = {
                    "title": title,
                    "date": year,
                    "pdf": [full_url]
                }
        self.article_data_map.update(mapping)
        return mapping