from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re 
from datetime import datetime

class ArkansasDivisionOfWorkforceServices(OCSpider):
    name = 'ArkansasDivisionOfWorkforceServices'
    
    country = "US"
    
    start_urls_names = {
        'https://dws.arkansas.gov/news-feed/': 'News Releases'
    }
    
    custom_settings = {
    'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36'
}
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return [url for url in response.xpath("//div[@class='elementor-widget-container']//h2//a/@href").getall()
        if "news_post/" in url]

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = response.xpath("//h2/strong/text()").get() or \
                response.xpath("//h2/text()").get() or \
                response.xpath("//h3/strong/text()").get() or \
                response.xpath("//h3/text()").get()
        return title.strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("///body//p").getall())

    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"  

    def get_date(self, response) -> str:
        date_elements = response.xpath('//div[@class="elementor-widget-container"]/text()').getall()
        for date_str in date_elements:
            date_str = date_str.strip()
            if re.match(r'\d{2}/\d{2}/\d{4}', date_str):
                try:
                    return datetime.strptime(date_str, "%m/%d/%Y").strftime("%Y-%m-%d")
                except ValueError:
                    continue

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//a[contains(@class, "next")]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None