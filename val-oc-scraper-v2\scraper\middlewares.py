# Define here the models for your spider middleware
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/spider-middleware.html

from scrapy import signals
import requests
import os
from scrapy.http import HtmlResponse
from urllib.parse import urlencode, urlparse
from pathlib import Path
from typing import List


# useful for handling different item types with a single interface
from itemadapter import is_item, ItemAdapter
from urllib.parse import urlencode

def get_oxylabs_supported_countries() -> List[str]:
    """Function to return a list of supported countries by Oxylabs.
    This is used to validate if the country is supported by Oxylabs or not.

    Returns:
        List[str]: list of supported countries by Oxylabs
    """
    # List of countries supported by Oxylabs
    # https://files.gitbook.com/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FzrXw45naRpCZ0Ku9AjY1%2Fuploads%2FrnerIwIXqbkIZbpUL8v0%2Funiversal-supported-geo_location-values.json?alt=media&token=d66d2208-02b0-47a5-bcd2-2518e34070d3
    countries_txt_file_path = (
        Path(__file__).parent / "conf" / "oxylabs_supported_countries.txt"
    )
    with open(countries_txt_file_path, "r", encoding="UTF-8") as file:
        countries = [line.strip() for line in file.readlines() if line.strip()]
    return countries


class ScraperSpiderMiddleware:
    # Not all methods need to be defined. If a method is not defined,
    # scrapy acts as if the spider middleware does not modify the
    # passed objects.

    @classmethod
    def from_crawler(cls, crawler):
        # This method is used by Scrapy to create your spiders.
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_spider_input(self, response, spider):
        # Called for each response that goes through the spider
        # middleware and into the spider.

        # Should return None or raise an exception.
        return None

    def process_spider_output(self, response, result, spider):
        # Called with the results returned from the Spider, after
        # it has processed the response.

        # Must return an iterable of Request, or item objects.
        for i in result:
            yield i

    def process_spider_exception(self, response, exception, spider):
        # Called when a spider or process_spider_input() method
        # (from other spider middleware) raises an exception.

        # Should return either None or an iterable of Request or item objects.
        pass

    def process_start_requests(self, start_requests, spider):
        # Called with the start requests of the spider, and works
        # similarly to the process_spider_output() method, except
        # that it doesn’t have a response associated.

        # Must return only requests (not items).
        for r in start_requests:
            yield r

    def spider_opened(self, spider):
        spider.logger.info('Spider opened: %s' % spider.name)


class ScraperDownloaderMiddleware:
    # Not all methods need to be defined. If a method is not defined,
    # scrapy acts as if the downloader middleware does not modify the
    # passed objects.

    @classmethod
    def from_crawler(cls, crawler):
        # This method is used by Scrapy to create your spiders.
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_request(self, request, spider):
        # Called for each request that goes through the downloader
        # middleware.

        # Must either:
        # - return None: continue processing this request
        # - or return a Response object
        # - or return a Request object
        # - or raise IgnoreRequest: process_exception() methods of
        #   installed downloader middleware will be called
        return None

    def process_response(self, request, response, spider):
        # Called with the response returned from the downloader.

        # Must either;
        # - return a Response object
        # - return a Request object
        # - or raise IgnoreRequest
        return response

    def process_exception(self, request, exception, spider):
        # Called when a download handler or a process_request()
        # (from other downloader middleware) raises an exception.

        # Must either:
        # - return None: continue processing this exception
        # - return a Response object: stops process_exception() chain
        # - return a Request object: stops process_exception() chain
        pass

    def spider_opened(self, spider):
        spider.logger.info('Spider opened: %s' % spider.name)


class GeoProxyMiddleware(object):
    # more information ref: https://scrapeops.io/docs/web-scraping-proxy-api-aggregator/advanced-functionality/country-geotargeting/
    countries = [
        "br",
        "ca",
        "cn",
        "in",
        "it",
        "jp",
        "fr",
        "de",
        "ru",
        "es",
        "us",
        "uk"
    ]
    
    def get_proxy(self, url, country):
        if country not in self.countries:
            raise Exception(f"Country {country} not supported")
        
        scrapeops_key = os.environ["SCRAPEROPS_API_KEY"]
        _url = f'https://proxy.scrapeops.io/v1/?api_key={scrapeops_key}&url={url}&country={country}'
        return _url
    

    def process_request(self, request, spider, timeout=None):
        # proxy_country is a variable that needs to be set in the spider class
        # eg: proxy_country = "cn"
        url = self.get_proxy(request.url, spider.proxy_country)

        if timeout:
            response = requests.get(url, timeout=(timeout, None))
        else:
            response = requests.get(url)

        new_response = HtmlResponse(
            url=request.url,
            body=response.content,
            encoding='utf-8',
            status=response.status_code
        )
        return new_response
    


class ProxyMiddleware(object):    
    
    def get_proxy(self):
        scrapeops_key = os.environ["SCRAPEROPS_API_KEY"]
        proxy = f'http://scrapeops:{scrapeops_key}@proxy.scrapeops.io:5353'
        return proxy

    def process_request(self, request, spider):
        if spider.name == "CentralCommissionForDisciplineInspection" or spider.name == "tribunnews":
            proxy = self.get_proxy()
            print(f"Assiging proxy {proxy}")
            request.meta['proxy'] = proxy
        
        
class HeadlessBrowserProxy(object):
    """
    In order to use this middleware, you need to add it to the middlewares in the spider class
    vars:
        HEADLESS_BROWSER_WAIT_TIME in the spider class : it defaults to 30000 milliseconds
    """
    def get_proxy(self, url, timeout): # timeout in milliseconds
        
        scrapeops_key = os.environ["SCRAPEROPS_API_KEY"]
        
        proxy_params = {
            'api_key': scrapeops_key,
            'url': url, 
            "wait": timeout
        }
        return f'https://proxy.scrapeops.io/v1/?{urlencode(proxy_params)}'
        
    def process_request(self, request, spider, timeout=None):
        
        HEADLESS_BROWSER_WAIT_TIME = getattr(spider, "HEADLESS_BROWSER_WAIT_TIME", 40000)

        url = self.get_proxy(request.url, HEADLESS_BROWSER_WAIT_TIME)

        if timeout:
            response = requests.get(url, timeout=(timeout, None))
        else:
            response = requests.get(url)

        new_response = HtmlResponse(
            url=request.url,
            body=response.content,
            encoding='utf-8',
            status=response.status_code
        )
        return new_response

class OxylabsProxyMiddleware:

    supported_countries = get_oxylabs_supported_countries()

    def get_proxy(self):
        username = os.environ["OXYLABS_USERNAME"]
        password = os.environ["OXYLABS_PASSWORD"]
        return {
            "http": f"http://{username}:{password}@unblock.oxylabs.io:60000",
            "https": f"https://{username}:{password}@unblock.oxylabs.io:60000",
        }

    def process_request(self, request, spider):
        # check if the spider has the variables
        require_js_rendering = getattr(spider, "require_js_rendering", False)
        require_geo_proxy = getattr(spider, "require_geo_proxy", False)
        proxy_country = getattr(spider, "proxy_country", "")

        if require_geo_proxy and not proxy_country:
            raise ValueError(
                "'require_geo_proxy' requires 'proxy_country' to be set. "
                "Please set the 'proxy_country' attribute in the spider class."
            )
        elif require_geo_proxy and proxy_country not in self.supported_countries:
            raise ValueError(
                f"'{proxy_country}' is not a supported country. "
                f"Supported countries are: {', '.join(self.supported_countries)}"
            )

        headers = {}
        if require_js_rendering:
            headers["x-oxylabs-render"] = "html"
        if require_geo_proxy:
            headers["x-oxylabs-geo-location"] = proxy_country

        # set the proxy in the request
        site_scheme = urlparse(request.url).scheme
        request.meta["proxy"] = self.get_proxy()[site_scheme]

        # create a new Request with the headers
        meta = {}
        meta["proxy"] = self.get_proxy()[site_scheme]
        request.headers.update(headers)
        request.meta.update(meta)

        return None
