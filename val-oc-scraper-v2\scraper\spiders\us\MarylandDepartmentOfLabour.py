import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import datetime
import re
from urllib.parse import urljoin
import scrapy
from urllib.parse import urlparse
from urllib.parse import unquote

class MarylandDepartmentOfLabour(OCSpider):
    name = 'MarylandDepartmentOfLabour'
    
    country = "US"

    start_urls_names = {
        'http://www.dllr.state.md.us/whatsnews/': 'News',
    }
    
    article_date_map = {}
   
    def parse_intermediate(self, response):
        base_url = response.url
        articles = response.xpath('//div[contains(@id, "accordion")]//ul/li')
        raw_articles = []

        for article in articles:
            href = article.xpath('./a/@href').get()
            date_text = article.xpath('normalize-space(.)').get()

            if href:
                full_url = unquote(urljoin(base_url, href.strip()).rstrip('/'))
                raw_articles.append(full_url)

                if date_text:
                    match = re.search(r'([A-Za-z]+ \d{1,2}, \d{4})', date_text)
                    if match:
                        self.article_date_map[full_url] = match.group(1).strip()

        # ✅ Remove duplicates and filter out non-matching domains/paths
        all_articles = list({url for url in raw_articles if self.filter_url(url)})
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page):
            chunk = all_articles[start_idx:start_idx + articles_per_page]
            for article_url in chunk:
                self.crawler.stats.inc_value("articles_crawled") 
                yield scrapy.Request(
                    url=article_url,
                    callback=self.parse_article,
                    meta={
                        'start_url': start_url,
                        'articles': all_articles,
                        'start_idx': start_idx
                    },
                    dont_filter=True
                )

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return [
            url for url in all_articles[start_idx:end_idx]
            if self.filter_url(url)
        ]
    
    def filter_url(self, url):
        parsed = urlparse(url)
        return (
            parsed.netloc == "www.dllr.state.md.us"
            and parsed.path.startswith("/whatsnews/")
            and parsed.path.endswith(".shtml")
        )
 
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//*[@id="mdgovMain"]/h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//*[@id="mdgovMain"]/p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response):
        from urllib.parse import unquote
        article_url = unquote(response.url.rstrip('/'))
        return self.article_date_map.get(article_url)

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return None