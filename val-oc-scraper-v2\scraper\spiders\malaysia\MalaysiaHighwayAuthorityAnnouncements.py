#MalaysiaHighwayAuthorityAnnouncements

from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class MalaysiaHighwayAuthorityAnnouncements(OCSpider):
    name = "MalaysiaHighwayAuthorityAnnouncements"

    start_urls_names = {
    f"https://www.llm.gov.my/announcement/announcement_list/{i}": "News" for i in range(0, 1069, 6)  #pagination is not supported
}
    
    start_urls_with_no_pagination_set={
      "https://www.llm.gov.my/announcement/announcement_list"
     }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
     return response.xpath('//*[@id="announcement"]//h4/a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
     return response.xpath('//*[@id="details"]/div[2]/h4//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//*[@id="details"]/div[2]/p//text() |//div[@dir="auto"]/text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="panel-image"]//img//@src').getall()
       
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response) -> str:
       date= response.xpath('//h5[@class="clear-margin"]//text()').get()
       cleaned = date.strip()
       parsed_date = datetime.strptime(cleaned, "%d/%m/%Y")
       formatted_date = parsed_date.strftime("%d-%m-%Y")
       #return parsed_date.strftime("%Y-%m-%d")
       return formatted_date 
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 