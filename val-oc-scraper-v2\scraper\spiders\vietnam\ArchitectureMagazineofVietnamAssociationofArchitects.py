from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re

class ArchitectureMagazineofVietnamAssociationofArchitects(OCSpider):
    name = "ArchitectureMagazineofVietnamAssociationofArchitects"

    start_urls_names = {
        'https://www.tapchikientruc.com.vn/category/tin-tuc/tin-hoi-kts-viet-nam': 'Architects'
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    exclude_rules: list[str] = ["youtube.com", "video", "ads"]
    include_rules: list[str] = [] 

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        links = response.xpath('//main[@id="main"]//a[@class="entry-title"]/@href').getall()
        filtered = []
        for link in links:
            if any(rule in link for rule in self.exclude_rules):
                continue
            if self.include_rules and not any(rule in link for rule in self.include_rules):
                continue
            filtered.append(link)
        return filtered

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title = response.xpath('//h1[@class="entry-title"]//text()').get()
        if title:
            return title.strip()
        return ""

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath('(//div[@class="entry-content"]//p | //div[@class="entry-content"]//li)//text()').getall())

    def get_date(self, response) -> str:
        date_text = response.xpath('//span[@class="updated published"]//text()').get()
        if date_text:
            date_text = date_text.strip()
            if date_text:
                return date_text
        return datetime.now().strftime(self.date_format())

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="entry-content"]//img[contains(@decoding, "async")]/@src').getall()        

    def get_document_urls(self, response, entry=None) -> list[str]:
        return []

    def get_authors(self, response) -> str:
        return response.xpath('//h2[@class="author-title"]/a[@class="author-link f0"]/text()').get()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[@class="nextpostslink"]/@href').get()