from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization

class TheYouthNewspaperofVietnam(OCSpider):
    name = "TheYouthNewspaperofVietnam"

    start_urls_names = {
        'https://thanhnien.vn/thoi-su.htm': 'news'
    }

    start_urls_with_no_pagination_set = {
        'https://thanhnien.vn/thoi-su.htm'  #Pagination is not supported
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def language(self):
        return "Vietnamese"

    @property
    def source_type(self) -> str:
        return 'unknown'

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return list(set(response.xpath('//a[@class="box-category-link-title"]/@href').getall()))
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//span[@data-role="title"]//text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detail-cmain"]//p/text()').getall() )
        
    def get_date(self, response) -> str:
        date_data = response.xpath('//div[@data-role="publishdate"]//text()').get()
        date = re.search(r"\d{2}/\d{2}/\d{4} \d{2}:\d{2}", date_data or '')
        if date:
            return date.group(0)
        else:
            ValueError(f"Date not found for url = {response.url}")
       
    def date_format(self) -> str:
        return "%d/%m/%Y %H:%M"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="detail-cmain"]//img/@src').getall()

    def get_document_urls(self, response, entry=None)->list:
        return []
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None