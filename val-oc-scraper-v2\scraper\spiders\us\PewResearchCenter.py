from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class PewResearchCenter(OCSpider):
    name = "PewResearchCenter"

    country="US"

    start_urls_names = {
        "https://www.pewresearch.org/publications/" : "Press Releases",
    }

    charset = "utf-8"

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h2//a//@href").getall()
       
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='entry-content wp-elements-61965727119a064f24745c828df033dc wp-block-post-content is-layout-flow wp-block-post-content-is-layout-flow']//text() | //div[@class='wp-block-group is-layout-flow wp-block-group-is-layout-flow']//p//text()").getall())

    def get_images(self, response, entry=None) :
        return response.xpath("//div[@class='entry-content wp-elements-61965727119a064f24745c828df033dc wp-block-post-content is-layout-flow wp-block-post-content-is-layout-flow']//img//@src").getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return response.xpath("//div[@class='wp-elements-73966845157d3bc186be5b140d45c499 wp-block-prc-block-bylines-display has-small-label-font-size is-layout-flex wp-container-prc-block-bylines-display-is-layout-65a8c4df wp-block-prc-block-bylines-display-is-layout-flex']//a//text()").getall()

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='has-link-color wp-elements-0ebc8bd266fb39786d774446257bd21b wp-block-post-date has-text-color has-ui-gray-very-dark-color']//time//text()").get()
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page=response.xpath("//a[contains(text(),'Next Page')]//@href").get()
        if next_page:
            return next_page
        else:
            return None