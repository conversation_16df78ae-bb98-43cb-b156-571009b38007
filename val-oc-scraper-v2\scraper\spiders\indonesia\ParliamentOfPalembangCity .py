from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfPalembangCity(OCSpider):
    name = "ParliamentOfPalembangCity"

    start_urls_names = {
        "https://dprdkota.palembang.go.id/berita.php": "PPID"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_to_date_mapping = {}  # Mapping date with child articles from start URL
    
    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }
    
    def get_articles(self, response) -> list:  
        articles=[]
        map={} 
        for item in response.xpath('//div[@class="berita1"]'):
            url= item.xpath('.//a/@href').get()
            date = item.xpath('.//span/text()').get()
            if url and date:
                full_url = response.urljoin(url)
                articles.append(full_url)
                map[full_url] = date.strip()
        self.article_to_date_mapping.update(map)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="section-title"]/div//text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[contains(@class, "container")]//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="container aos-init aos-animate"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        article_url = response.url
        mapping_date = self.article_to_date_mapping.get(article_url, "")
        if mapping_date:
            for indo, eng in self.indonesian_to_english.items():
                mapping_date = mapping_date.replace(indo, eng)
        return mapping_date.strip()
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None