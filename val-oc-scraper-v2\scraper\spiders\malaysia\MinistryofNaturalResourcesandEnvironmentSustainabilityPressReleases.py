from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
from scraper.middlewares import HeadlessBrowserProxy

class MinistryofNaturalResourcesandEnvironmentSustainabilityPressReleases(OCSpider):
    name = "MinistryofNaturalResourcesandEnvironmentSustainabilityPressReleases"
    
    start_urls_names = {
        "https://www.nres.gov.my/ms-my/PustakaMedia/Pages/KenyataanMedia.aspx#InplviewHash42a08367-65a8-45bd-8330-a843ede4b9f3=": "News"
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request 
    
    charset = "iso-8859-1"

    country = "Malaysia"
    
    article_data_map = {}

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
   
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//tbody//tr"):
            url = article.xpath(".//a[@class='ms-listlink']//@href").get()
            title = article.xpath(".//a[@class='ms-listlink']//text()").get()
            date = article.xpath(".//span[@class='ms-noWrap']//text()").get()
            if url and title and date:
                self.article_data_map[url]={"url":url, "title": title, "date":date}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return f"https://www.nres.gov.my/{entry}"
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
    
    def get_document_urls(self, response, entry=None) -> list:
        if '.pdf' in response.url:
            return [response.url]
        return []
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) -> Optional[str]:
        return None #pagination not working