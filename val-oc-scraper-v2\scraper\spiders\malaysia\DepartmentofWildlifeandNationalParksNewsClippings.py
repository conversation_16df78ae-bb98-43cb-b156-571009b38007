#RegistrationofCompanyAnnualReports
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class DepartmentofWildlifeAndNationalParksNewsClippings(OCSpider):
    name = "DepartmentofWildlifeAndNationalParksNewsClippings"
    #I Add 3 Article Here If need More then add 
    start_urls_names = {
        "https://www.wildlife.gov.my/index.php/penerbitan/557-keratan-akhbar-bagi-tahun-2025":"2025 Akbars",
        "https://www.wildlife.gov.my/index.php/mutakhir/530-keratan-akhbar-bagi-tahun-2024": "2024 Akbars",
        "https://www.wildlife.gov.my/index.php/penerbitan/492-keratan-akhbarbagi-tahun-2023":"2023 Akbars"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//*[@id="tm-content"]/article/table/tbody/tr/td[2]//a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
       return response.xpath("//h1[1]//text()").get()

    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return response.xpath("//table/tbody/tr/td[2]/a//@href").getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str: 
       
        date= response.xpath('//*[@id="tm-content"]/article/p[1]/time//text()').get() 
        #year= "2021"
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y-%m-%d")

        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        paths= response.xpath("//table/tbody/tr/td[2]/a//@href").getall()
        url_paths=[]
        for path in paths:
            if ".pdf" in path:
                url_paths.append(path)
        return url_paths

        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 