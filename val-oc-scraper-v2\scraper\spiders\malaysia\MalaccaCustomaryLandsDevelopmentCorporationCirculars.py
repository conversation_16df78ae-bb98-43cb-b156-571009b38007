from scraper.OCSpider import OCSpider 
from urllib.parse import urljoin, unquote
import re

class MalaccaCustomaryLandsDevelopmentCorporationCirculars(OCSpider):
    name = "MalaccaCustomaryLandsDevelopmentCorporationCirculars"

    start_urls_names = {
        'https://ptg.melaka.gov.my/en/circular': 'circular'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://ptg.melaka.gov.my/en/circular"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list = []
        articles = response.xpath('//tbody//tr')
        for article in articles:
            titles = article.xpath('.//td[contains(@style,"width: 59.482%;")]//text()').getall()
            title = ' '.join(t.strip() for t in titles if t.strip())
            url = article.xpath('.//td//a//@href').get()         
            if title:
                date_match = re.search(r'\b(19[7-9]\d|20\d{2})\b', title)
                if date_match:
                        base_url = "https://ptg.melaka.gov.my"
                        full_url = urljoin(base_url, url)
                        decoded_url = unquote(full_url)
                        child_article_list.append(decoded_url)               
                        self.article_url_title_date_mapping[decoded_url] = [title.strip(), date_match.group(0)]
        return child_article_list

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[0]

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[1]

    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return unquote(response.url)
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None