from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ParliamentOfPurbalinggaRegency(OCSpider):
    name = "ParliamentOfPurbalinggaRegency"

    start_urls_names = {
        "https://jdih-dprd.purbalinggakab.go.id/berita/index" : "Articles",
        "https://dprd.purbalinggakab.go.id/category/berita/":"Articles"
    }
    
    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_data_map = {}  # Mapping title and date to child articles from start URL
    
    def get_articles(self, response) -> list:
        articles=[]
        if "jdih-dprd" in response.url:
            for article in response.xpath("//div[@class='card-body padding-30px-all']"):
                url = article.xpath(".//h5//a[contains(@href,'view')]//@href").get()
                title=article.xpath(".//h5//a[contains(@href,'view')]//text()").get()
                date =article.xpath(".//div[@class='margin-10px-bottom']//span//text()").get()
                if url and title and date:
                    self.article_data_map[url]={"url":url, "title": title, "date": date}
                    articles.append(url)
            return articles
        else:
          return response.xpath('//h2[@class="post-title entry-title"]//a//@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        if "jdih-dprd" in response.url:
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        else:
            return response.xpath("//h1[@class='post-title entry-title']//text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='margin-30px-bottom']//text() | //div[@class='entry-content']//p//text() | //div[@class='entry themeform']//p//text()").getall()) 

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='bs-header']//img//@src | //div[@class='entry-content']//img//@src").getall()
  
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response, entry=None) -> str:
            date_text=self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
            if date_text:
                date_text = date_text.strip()
                indonesian_months = {
                    'Januari': 'January', 'Februari': 'February', 'Maret': 'March',
                    'April': 'April', 'Mei': 'May', 'Juni': 'June',
                    'Juli': 'July', 'Agustus': 'August', 'September': 'September',
                    'Oktober': 'October', 'November': 'November', 'Desember': 'December'
                }
                for indo_month, eng_month in indonesian_months.items():
                    if indo_month in date_text:
                        date_text = date_text.replace(indo_month, eng_month)
                        break
                return date_text
            else:
                date =response.xpath("//time//text()").get().strip()
                if date:
                    indonesian_months = {
                        'Januari': 'January', 'Februari': 'February', 'Maret': 'March',
                        'April': 'April', 'Mei': 'May', 'Juni': 'June',
                        'Juli': 'July', 'Agustus': 'August', 'September': 'September',
                        'Oktober': 'October', 'November': 'November', 'Desember': 'December'
                    }
                    for indo_month, eng_month in indonesian_months.items():
                        if indo_month in date:
                            date = date.replace(indo_month, eng_month)
                            break
                    date_obj = datetime.strptime(date, "%d %B %Y")
                    date_text = date_obj.strftime("%d %B %Y")
                    return date_text

    def get_authors(self, response):
        return response.xpath("//span[@class='bs-author']//a[@class='ms-1']//text()").get()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        if "jdih-dprd" in response.url:
            return response.xpath("//ul[@class='pagination']//li[@class='next']//a//@href").get()
        else:
            return response.xpath("//*[@id='content']/div[@class='hu-pad group']/nav/ul/li[@class='next right']/a/@href").get()
