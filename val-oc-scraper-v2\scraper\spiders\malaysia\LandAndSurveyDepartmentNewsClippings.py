from typing import Optional, List
from scraper.OCSpider import OCSpider
import scrapy

class LandAndSurveyDepartmentNewsClippings(OCSpider):
    name = "LandAndSurveyDepartmentNewsClippings"

    country = "Malaysia"

    start_urls_names = {
        "https://landsurvey.sarawak.gov.my/web/subpage/newspaper_cutting_list": "Press Releases",
    }
    
    api_start_urls = {
    "https://landsurvey.sarawak.gov.my/web/subpage/newspaper_cutting_list": {
        "url": "https://landsurvey.sarawak.gov.my/web/subpage/newspaper_cutting_list_ajax",
        "payload": {
            "page": "1",
            "sort": "",
            "order_by": "date",
            "s": "",
            "m": "",
            "y": "",      
        }
    }
}
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_urls[start_url]
        api_url = api_data["url"]
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["page"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }

        yield scrapy.FormRequest(
            url=api_url,
            method="GET",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )

    article_data_map ={}  # Mapping title, date and PDF with child articles from start URL

    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        articles = []
        for i, article in enumerate(response.xpath("//div[@id='resp-table-body']/figure"), 1):
            # Extract image URL from img tag with class 'myImages'
            img_url = article.xpath(".//img[contains(@class, 'myImages')]/@src").get()
            # Extract title from figcaption > span:first-child (the blue bold text)
            title = article.xpath(".//figcaption/span[1]/text()").get()
            # Extract date from figcaption > span[2]
            date = article.xpath(".//figcaption/span[2]/text()").get()
            if img_url and title and date:
                img_url = response.urljoin(img_url)  # make sure URL is absolute
                title = title.strip()
                date = date.strip()
                # Store article_url along with title and date
                self.article_data_map[img_url] = {
                    "title": title,
                    "date": date,
                    "article_url": img_url
                }
                articles.append(img_url)
        return articles



    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        
        
    def get_body(self, response) -> str:
        return ""
        
    
    def get_images(self, response) -> List[str]:
        url = self.article_data_map.get(response.request.meta.get('entry'), {}).get('article_url', "")
        return [url] if url else []


    def date_format(self) -> str:
        return '%d %b %Y'

    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        
        
    def get_authors(self, response):
        return ""
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        next_page = int(current_page) + 1
        return next_page

    def get_page_flag(self) -> bool:
        return True

    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={
                    'current_page': next_page, 
                    'start_url': start_url, 
                    'api_url': api_url
                }
            )
        else:
            yield None
