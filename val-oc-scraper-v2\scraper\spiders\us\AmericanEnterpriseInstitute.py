from datetime import datetime
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class AmericanEnterpriseInstitute(OCSpider):
    name = "AmericanEnterpriseInstitute"

    country = "US"
    
    start_urls_names = {
        "https://www.aei.org/search-results/?wpsolr_fq%5B0%5D=type:press": "Press",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 seconds wait time

    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"

    def get_articles(self, response) -> list:    
        return response.xpath('//div[contains(@class, "post-search")]//h4[@class="title"]/a/@href').getall()
                  
    def get_href(self, entry):
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h1[@class="entry-title"]/text()').get()
        
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content"]/p/text()').getall())
               
    def get_images(self, response, entry=None) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response, entry=None) -> str:
        date_raw = response.xpath('//div[@class="date"]/text()').get(default="").strip()
        date_obj = datetime.strptime(date_raw, "%B %d, %Y")
        return date_obj.strftime(self.date_format())
            
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//li[@class="page-item"]/a[text()="Next"]/@href').get()    