import re
from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class StateRecordsandArchivesDepartmentofVietnamMinistryofHomeAffairsVietnam(OCSpider):
    name = "StateRecordsandArchivesDepartmentofVietnamMinistryofHomeAffairsVietnam"

    start_urls_names = {
        "https://luutru.gov.vn/tin-tuc.htm": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def language(self):
        return "Vietnamese"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//div[@class="col-md-12 col-xs-10 padding-left-20"]'):
            url = article.xpath(".//p//a/@href").get()
            title = article.xpath(".//p//a/text()").get()
            date_text = " ".join(article.xpath('.//div[@class="text-muted"]//text()').getall()).strip()
            match = re.search(r"(\d{2}/\d{2}/\d{4})", date_text)
            pub_date = match.group(1) if match else None
            if url and title:
                self.article_data_map[url] = {
                    "title": title.strip(),
                    "date": pub_date
                }
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get("entry"), {}).get("title", "")

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath('//div[@class="row"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> Optional[str]:
        return self.article_data_map.get(response.request.meta.get("entry"), {}).get("date")

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return response.xpath('//div[@class="pagination"]//a[contains(text(),"Sau")]/@href').get()
