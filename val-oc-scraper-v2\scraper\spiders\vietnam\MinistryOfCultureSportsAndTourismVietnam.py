from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MinistryOfCultureSportsAndTourismVietnam(OCSpider):
    name = "MinistryOfCultureSportsAndTourismVietnam"

    start_urls_names = {
        "https://bvhttdl.gov.vn/tin-tuc-va-su-kien.htm": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:  
        return response.xpath("//div[@class='slideshow_items']//a/@href | //div[@class='article article-bordered-bottom margin-not-top']//a/@href").getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('normalize-space(//h2[@class="title" and @data-role="title"])').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='entry-body' and @data-role='content']//text() | //div[@class='entry-body' and @data-role='content']//p/text()").getall())

    def get_images(self, response) -> list:
        src_links = response.xpath('//div[@class="entry-body" and @data-role="content"]//img/@src').getall()
        data_original_links = response.xpath('//div[@class="entry-body" and @data-role="content"]//img/@data-original').getall()
        a_href_links = response.xpath('//div[@class="entry-body" and @data-role="content"]//a/@href').getall()
        all_links = list(set(src_links + data_original_links + a_href_links))
        return all_links

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return response.xpath('//span[@class="time margin-bottom" and @data-role="publishdate"]/text()').get().strip().split(' | ')[0]

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.urljoin(response.xpath('//li[@class="pager_next "]/a/@href').get())