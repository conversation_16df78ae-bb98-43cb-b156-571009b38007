from scraper.utils.helper import body_normalization
from scraper.OCSpider import OCSpider
import scrapy
from datetime import datetime

class NevadaAttorneyGeneral(OCSpider):
    name = 'NevadaAttorneyGeneral'

    country = "US"

    start_urls_names = {
        "https://ag.nv.gov/News/Press_Releases": "News",
        "https://ag.nv.gov/News/PR/2024_PR/": "News",
    }

    def parse_intermediate(self, response):
        all_articles = list(set(response.xpath("//ul[@class='main_list']//li//a[not(contains(@href, '.pdf'))]//@href").getall()))
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx, 
                    'articles': all_articles, 
                    'start_url': start_url
                },
                dont_filter=True
            )
        

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Central"
    
    @property
    def language(self):
        return "English"
    
    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='row']//div//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%b. %d, %Y"
    
    def get_date(self, response) -> str:
        date = response.xpath("//h2//text()").get()
        date_formats = ["%b. %d, %Y", "%b.%d, %Y", "%B %d, %Y"]
        for date_format in date_formats:
            if date:
                try:
                    return datetime.strptime(date, date_format).strftime("%b. %d, %Y")
                except ValueError:
                    continue 
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.xpath("//p//a[contains(@href,'.pdf')]//@href").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        current_year = int(response.url.split('/')[-2].replace("_PR", ""))
        if current_year > 2019:
            return f"https://ag.nv.gov/News/PR/{current_year - 1}_PR/"
        else:
            return None
    
    def go_to_next_page(self, response, start_url, current_page=None):
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=next_page,
                callback=self.parse_intermediate,
                dont_filter=True
            )