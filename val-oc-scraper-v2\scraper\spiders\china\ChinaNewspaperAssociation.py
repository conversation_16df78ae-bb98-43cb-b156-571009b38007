from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaNewspaperAssociation(OCSpider):
    name = "ChinaNewspaperAssociation"

    start_urls_names = {
        "http://zgbx.people.com.cn/GB/415415/index.html": "协会要闻",
        "http://zgbx.people.com.cn/GB/415416/index.html": "报业动态"
    }

    charset = "gb2312"
    
    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='pic_2j_box2']/ul/li//a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="text_c"]//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='show_text']//p/text()").getall())
    
    def date_format(self) -> str:
        return '%Y年%m月%d日%H:%M'
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='text_c']/p[2]/text()").re_first(r'(\d{4}年\d{2}月\d{2}日\d{2}:\d{2})') 
    
    def get_images(self, response) -> list:
        images = []
        img_tags = response.xpath("//div[@class='show_text']//img/@src").getall()
        for img_src in img_tags:
            if img_src:
                images.append(response.urljoin(img_src))
        if not images:
            self.logger.warning(f"No images found in article body on: {response.url}")
        return images
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    # This website does not have pagination, 
    # so there is no "next page" to navigate to.
    def get_next_page(self, response):
        return None