from typing import List, Union
import scrapy
from scraper.OCSpider import <PERSON>CSpid<PERSON>
from scraper.utils.helper import body_normalization
from datetime import datetime

class CiatongSecurities(OCSpider):
    name = "CiatongSecurities"

    start_urls_names = {
        "https://www.ctsec.com/company/news/6": "公司动态",
        "https://www.ctsec.com/investor/files/1": "公司公告",
        "https://www.ctsec.com/investor/report/3": "定期报告"
    }
    
    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    api_start_urls = {
        'https://www.ctsec.com/company/news/6': {
            "url": "https://www.ctsec.com/company/get-list",
            "payload": {"page": "1", "size": "5", "type": "6"},
        },
        'https://www.ctsec.com/investor/files/1': {
            "url": "https://www.ctsec.com/investor/get-flies",
            "payload": {"page": "1", "size": "10", "type": "1"},
        },
        'https://www.ctsec.com/investor/report/3': {
            "url": "https://www.ctsec.com/investor/get-flies",
            "payload": {"page": "1", "size": "5", "type": "3"},
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_urls[start_url]
        api_url = api_data["url"]
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["page"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
        
    article_data_map ={}  # Mapping title, date and PDF with child articles from start URL

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = []
        try:
            data = response.json()
            rows = data.get("data", {}).get("rows", [])
            if not rows:
                return []
            for row in rows:
                relative_url = row.get("url") or f"/company/detail/{row.get('id')}"
                title = row.get("title", "").strip()
                updated_at = row.get("updated_at", "") or row.get("created_at", "")
                date = updated_at.split("T")[0] if "T" in updated_at else updated_at
                if not relative_url or not title or not date:
                    continue
                full_url = response.urljoin(relative_url)
                pdf = full_url if any(ext in full_url.lower() for ext in [".pdf", ".doc", ".docx"]) else "None"
                self.article_data_map[full_url] = {
                    "title": title,
                    "date": date,
                    "pdf": pdf
                }
                articles.append(full_url)
            return articles
        except Exception:
            pass
        html_links = response.xpath('//div[@class="content"]/a[@class="list"]/@href').getall()
        if html_links:
            return html_links
        for article in response.xpath('//ul[@class="inv-listcon"]/li | //div[@class="content"]/a[@class="list"]'):
            url = article.xpath('./@href').get()
            title = article.xpath('.//span[1]/text()').get()
            date = article.xpath('.//span[@class="days icon_calendar"]/text()').get()
            if url and title and date:
                full_url = response.urljoin(url)
                title = title.strip()
                clean_date = date.strip()
                pdf = full_url if ".pdf" in full_url.lower() else "None"
                self.article_data_map[full_url] = {
                    "title": title,
                    "date": clean_date,
                    "pdf": pdf
                }
                articles.append(full_url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        title = response.xpath('//div[@class="container_a"]/h1/text()').get()
        if title:
            return title.strip()
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "") 
            
    def get_body(self, response) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization( response.xpath('//div[@class="container_a"]/pre[@class="cnt"]//p//text()').getall())
    
    def get_images(self, response) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath('//div[@class="container_a"]/pre[@class="cnt"]//img/@src').getall()
       
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        datetime_text = response.xpath("//span[@class='time']/text()").get()
        if datetime_text:
            try:
                dt = datetime.strptime(datetime_text.strip(), "%a %b %d %Y %H:%M:%S GMT%z (%Z)")
                return dt.strftime(self.date_format())
            except ValueError:
                pass  
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
    def get_next_page(self, response, current_page) -> Union[None, int]:
        try:
            data = response.json()
            rows = data.get("data", {}).get("rows", [])
            if len(rows) == 0:
                return None
            return current_page + 1
        except Exception:
            return None
            
    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page=1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                formdata={**self.api_start_urls[start_url]["payload"], "page": str(next_page)},
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "current_page": next_page
                },
                dont_filter=True
            )