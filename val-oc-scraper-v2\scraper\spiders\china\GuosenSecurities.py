import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class GuosenSecurities(OCSpider):
    name = 'GuosenSecurities'

    start_urls_names = {
        # 'https://www.guosen.com.cn/gs/tzzgx/pc/dqbg.html': 'Periodic Reports',
        # 'https://www.guosen.com.cn/gs/tzzgx/pc/xxpl.html': 'Information Disclosure',
        'https://www.guosen.com.cn/gs/about_guosen/info_list.html': 'All News' 
    }
    
    custom_settings = {
             "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    
    api_start_url = {
        'https://www.guosen.com.cn/gs/tzzgx/pc/dqbg.html': {
            'url': 'https://www.guosen.com.cn/xcgswz-web/info/list/1.0',
            'payload': {
                "channelid": "0001000100090004",
                "pageSize": "10",
                "pageNo": "1",
                "title": "",
                "all": "1"
            },
        },
        'https://www.guosen.com.cn/gs/tzzgx/pc/xxpl.html': {
            'url': 'https://www.guosen.com.cn/xcgswz-web/info/list/1.0',
            'payload': {
                "channelid": "0001000100090005",
                "pageSize": "10",
                "pageNo": "1",
                "title": "",
                "all": "1"
            },
        },
        'https://www.guosen.com.cn/gs/about_guosen/info_list.html': {
            'url': 'https://www.guosen.com.cn/gswz-web/info/list/1.0',
            'payload': {
                "channelid": "00010001000500020001,00010001000500020002",
                "pageSize": "20",
                "pageNo": "1",
            },
        },
        
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            return
        else:
            current_page = response.meta.get("current_page", 1)
            api_data["payload"]["pageNo"] = str(current_page)
            payload = api_data["payload"]
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers=headers,
                dont_filter=True,
                formdata=payload,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "current_page": current_page
                },
            )
    
    article_data_map ={}

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:   
        data = response.json()
        articles = []
        for item in data.get("data", []):
            attachment_list = item.get("attachmentList", [])
            url = attachment_list[0].get("url") if attachment_list else None
            title = item.get("title")
            date = item.get("publishtime")
            if url and title and date:
                full_url = url
                clean_title = title.strip()
                clean_date = date.strip()
                if ".pdf" in full_url.lower():
                    pdf = full_url
                else:
                    pdf = "None"
                self.article_data_map[full_url] = {
                    "title": clean_title,
                    "date": clean_date,
                    "pdf": pdf
                }
                articles.append(full_url)
        if articles:
            return articles
        else:
            data = response.json()
            article = data.get("data", [])
            articles =[]
            ids = [item.get("id") for item in article if "id" in item]
            for i in ids:
                article = f'https://www.guosen.com.cn/gs/about_guosen/info_detail.html?channelid=00010001000500020002&id={i}'
                articles.append(article)
            return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        return response.xpath("//div[@class='source-const']//h1//text()").get()
    
    def get_body(self, response) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath("//div[@class='source-const']//p//text()").getall())
    
    def get_images(self, response) -> list[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath("//div[@class='source-const']//p//img//@src").getall()
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        return response.xpath("//div[@class='source-const']//span[@class='time']//text()").get().strip()
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        if response.status != 200:
            return None
        else:
            return str(int(current_page) + 1)
    
    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url, 'api_url': api_url}
            )
        else:
            yield None