from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class RhodeIslandAttorneyGeneral(OCSpider):
    name = "RhodeIslandAttorneyGeneral"

    country = "US"
    
    start_urls_names = {
        "https://riag.ri.gov/press-releases" : "News Releases",
        }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 500
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000

    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//a[@class="qh__teaser-article__link"]/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//article[@class="node node--type-press-release node--view-mode-full"]/h1/text()').get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//article[@class="node node--type-press-release node--view-mode-full"]/p[not(@class="publish-date")]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_with_day = response.xpath('//p[@class="publish-date"]/text()').get().replace("Published on ", "").strip()
        date_clean = " ".join(date_with_day.split()[1:])
        return datetime.strptime(date_clean, "%B %d, %Y").strftime("%m-%d-%Y")
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        return response.xpath('//li[@class="pager__item pager__item--next"]/a/@href').get()
