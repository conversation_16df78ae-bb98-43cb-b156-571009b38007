from scraper.OCSpider import OCSpider
import re

class MalaysiaDepartmentofInsolvencyActs(OCSpider):
    name = "MalaysiaDepartmentofInsolvencyActs"

    start_urls_names = {
        'https://www.mdi.gov.my/rujukan-undang-undang/' : 'Acts',  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.mdi.gov.my/rujukan-undang-undang/'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//a[contains(@href, ".pdf")]')
        for row in rows:
            title = row.xpath('.//h3/span/text()').get()
            pdf_link = row.xpath('./@href').get()
            year_match = re.search(r'(20\d{2})$', title.strip() or "")
            year = year_match.group(1) if year_match else ""
            if title and pdf_link and year:
                title = title.strip()
                pdf_link = response.urljoin(pdf_link.strip())
                self.article_title_pdf_mapping[pdf_link] = (title, pdf_link, year)
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[2]

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None) -> list[str]:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[1]

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None