from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re
from urllib.parse import urljoin

class CentralDrugsStandardControlOrganizationCENTRALVietnam(OCSpider):
    name = "CentralDrugsStandardControlOrganizationCENTRALVietnam"

    start_urls_names = {
        "https://cdsco.gov.in/opencms/opencms/en/PvPI/": "News",
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    article_data_map = {}

    def get_articles(self, response) -> list:
        return response.xpath('//table[@id="example"]//tbody//tr//td[4]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//table[@id="example"]/tbody/tr/td[2]/text()').get()
    
    def get_body(self, response) -> str:
        return []

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        raw_date = response.xpath('//table[@id="example"]/tbody/tr/td[3]/text()').get()
        if not raw_date:
            return None
        clean_text = raw_date.replace("\xa0", " ").strip()
        date_obj = dateparser.parse(clean_text, languages=['en'])
        if date_obj:
            return date_obj.strftime("%Y-%m-%d")
        return None

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        links = response.xpath('//table[@id="example"]//tbody//tr//td[4]//a/@href').getall()
        return [urljoin("https://cdsco.gov.in", link) for link in links if link]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None
