from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class MissouriDepartmentOfLaborAndIndustrialRelations(OCSpider):
    name = "MissouriDepartmentOfLaborAndIndustrialRelations"

    country = "US"

    start_urls_names = {
        "https://labor.mo.gov/news/press-releases": "Press Releases",
    }

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        return response.xpath('//tbody//td[@class="views-field views-field-title"]/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="page-title"]/span/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[contains(@class, "field--name-body")]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return [] 

    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str =  response.xpath('//div[@class="field field--name-field-news-date field--type-datetime field--label-hidden field__item"]/time/text()').get()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="pager__item pager__item--next"]/a/@href').get()
        if not next_page:
            return None
        else:
            return response.urljoin(next_page)