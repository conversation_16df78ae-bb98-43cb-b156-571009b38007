from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
import json
from parsel import Selector

class ParliamentOfTangerangCity(OCSpider):
    name = "ParliamentOfTangerangCity"

    start_urls_names = {
        # "https://dprd.tangerangkota.go.id/dprd-artikel-page": "artikel",
        # "https://dprd-tangerangkota.jdihn.go.id/": "JDIH"
    }

    api_start_urls = {
        "https://dprd.tangerangkota.go.id/dprd-artikel-page": {
            "url" : "https://dprd.tangerangkota.go.id/web2/load_artikel/{current_page}"
        }
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def parse_intermediate(self, response):
       start_url =response.meta.get('start_url')
       api_data = self.api_start_urls.get(start_url)
       api_url = api_data.get("url")
       current_page = response.meta.get('current_page', 1)
       url=api_url.format(current_page=current_page)
       yield scrapy.Request(
            url=url,
            method='GET',
            headers={'Content-Type': 'application/json'},
            callback=self.parse,
            dont_filter=True, 
            meta={
                'start_url': start_url,
                "api_url": api_url,
                'current_page': current_page}
                )
    
    def get_articles(self, response) -> list:  
        data= json.loads(response.text)
        print(data)
        grid_html = data.get("grid", "")
        selector = Selector(text=grid_html)
        hrefs = selector.xpath('//div[@class="title"]/a/@href').getall()
        return hrefs
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2[@class="title"]//text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="container"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="classic-view"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="container"]//h4/text()').get().replace("»", "").strip()
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response):
       current_page = response.meta.get('current_page', 1)
       next_page = current_page + 1
       if (response.status ==200):
           return next_page
       else:
           return None
       
    def go_to_next_page(self, response, start_url, current_page=None):
        api_url = response.meta.get("api_url")
        next_page = self.get_next_page(response)
        url=api_url.format(current_page=next_page)
        if next_page:
            yield scrapy.Request(
                url=url,
                method='GET',
                dont_filter=True,
                headers={'Content-Type': 'application/json'},
                callback=self.parse_intermediate,
                meta={
                    'start_url': start_url,
                    "api_url": api_url,
                    'current_page': next_page}
            )
        else:
            self.logger.info("No more pages to scrape.")
        