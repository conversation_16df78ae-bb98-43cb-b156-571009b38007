from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import logging
import re
class DelawareDepartmentOfTransportation(OCSpider):
    name = "DelawareDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://deldot.gov/About/news/": "News Room",
        }
        
    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {'scraper.middlewares.HeadlessBrowserProxy': 350},
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    HEADLESS_BROWSER_WAIT_TIME = 30000

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="topics deldot-highlights"]//div[@class="news-feed"]/h4//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="releasePageTitle"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-body"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response):
        date_str = response.xpath('//div[@class="post-date"]/small/text()').get()  # Adjust XPath accordingly
        match = re.search(r'([A-Za-z]+ \d{1,2}, \d{4})', date_str)  # Extracts "February 25, 2025"
        if match:
            clean_date = match.group(1)
            return datetime.strptime(clean_date, "%B %d, %Y").strftime("%m-%d-%Y")
        else:
            logging.error(f"Date format mismatch: {date_str}")
            return None
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//div[@class="pager-inner"]//span[@class="pager-right"]/a/@href').get()
        if not next_page:
           return None
        return next_page
