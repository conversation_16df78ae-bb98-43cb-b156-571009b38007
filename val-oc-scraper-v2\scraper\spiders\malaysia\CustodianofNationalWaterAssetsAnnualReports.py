from typing import Optional
from scraper.OCSpider import OCSpider

class CustodianofNationalWaterAssetsAnnualReports(OCSpider):
    name = "CustodianofNationalWaterAssetsAnnualReports"
    
    start_urls_names = {
        "https://www.paab.my/annual-reports/": "News"
    }

    start_urls_with_no_pagination_set = {
        "https://www.paab.my/annual-reports/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
   
    def get_articles(self, response) -> list:
        return response.xpath("//a[@class='pp-post-link']//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//span[@class='fl-heading-text']//text()").get()
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response):
        return response.xpath("//span[@class='fl-heading-text']//text()").get()
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) -> Optional[str]:
        return None