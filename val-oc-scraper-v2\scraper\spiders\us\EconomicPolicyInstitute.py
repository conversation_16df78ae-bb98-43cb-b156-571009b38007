import scrapy
from scrapy import Selector
import urllib.parse
import json
from datetime import datetime
from html import unescape
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from typing import Optional

class EconomicPolicyInstitute(OCSpider):
    name = "EconomicPolicyInstitute"
    country = "US"

    start_urls_names = {
        "https://www.epi.org/publications/": "Publications",
    }

    article_date_map = {}

    api_start_urls = {
        "https://www.epi.org/publications/": {
            "url": "https://www.epi.org/wp-admin/admin-ajax.php",
            "payload": {
                "action": "epi_handle_search",
                "keywords": "",
                "offset": 0,
                "post_types": "publication",
            }
        }
    }

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/New_York"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return

        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]

        self.logger.info("[INIT] Requesting page 0")

        yield scrapy.Request(
            url=api_url,
            method="POST",
            headers={**self.headers, "Content-Type": "application/x-www-form-urlencoded"},
            body=urllib.parse.urlencode(payload),
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["offset"]
            }
        )

    def parse(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page", 0)
        payload = response.meta.get("payload", {}).copy()

        article_urls = self.get_articles(response)
        for url in article_urls:
            yield scrapy.Request(
                url=url,
                headers=self.headers,
                callback=self.parse_article,
                meta={"start_url": start_url}
            )

        # Trigger next page
        next_page_request = self.go_to_next_page(response, start_url, current_page)
        if next_page_request:
            yield from next_page_request

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            html_fragment = data.get("data", "")
            if not html_fragment.strip():
                self.logger.warning("No HTML found in 'data' field")
                return []

            selector = Selector(text=html_fragment)
            articles = selector.xpath('//li[contains(@class, "loop-item")]')

            article_urls = []
            for article in articles:
                url = article.xpath('.//h4/a/@href').get()
                date_raw = article.xpath('.//div[contains(@class, "loop-meta")]/span[1]/text()').get()

                if url:
                    cleaned_url = unescape(url).strip()
                    article_urls.append(cleaned_url)

                    if date_raw:
                        try:
                            date_obj = datetime.strptime(date_raw.strip(), "%B %d, %Y")
                            formatted_date = date_obj.strftime(self.date_format())
                            self.article_date_map[cleaned_url] = formatted_date
                        except ValueError:
                            self.article_date_map[cleaned_url] = date_raw.strip()

            return article_urls

        except json.JSONDecodeError:
            self.logger.error("Failed to decode JSON in get_articles()")
            return []

    def get_title(self, response):
        title = response.xpath('//h1[@class="entry-title"]/text() | //h1[@class="page-title"]/span[@class="title-presub"]/text()').get()
        return title.strip() if title else ''

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> str:
        date_raw = self.article_date_map.get(response.url)
        return date_raw if date_raw else ""

    def get_authors(self, response):
        return response.xpath('//p[@class="authors"]/a/text()').getall()

    def get_page_flag(self) -> bool:
        return False  # Set to False to disable pagination

    def get_next_page(self, response, current_page: int) -> Optional[int]:
        if not self.get_page_flag():
            self.logger.info("[PAGINATION] Skipped due to flag.")
            return None
        if current_page >= 145:
            self.logger.info("[PAGINATION] Reached page limit.")
            return None
        return current_page + 1

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 0):
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return

        next_page = self.get_next_page(response, current_page)
        if next_page is None:
            return

        payload = response.meta.get("payload", {}).copy() or api_data["payload"].copy()
        payload["offset"] = next_page

        yield scrapy.Request(
            url=api_data["url"],
            method="POST",
            headers={**self.headers, "Content-Type": "application/x-www-form-urlencoded"},
            body=urllib.parse.urlencode(payload),
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": next_page
            }
        )
