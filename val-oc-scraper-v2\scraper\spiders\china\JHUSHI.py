from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urlparse
import scrapy

class JHUSHI(OCSpider):
    name = "JHUSH<PERSON>"

    start_urls_names = {
        "https://www.jushi.com/notice/page/1": "",
        "https://www.jushi.com/news/page/1": "",
        "https://www.jushi.com/news/announcement/page/1": "",
    } 
   
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000

    article_map = {}
    article_date_map = {}

    DOWNLOAD_TIMEOUT = 180

    charset = "utf-8"
    # charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        # Call mapping extractor
        self.extract_articles_with_titles(response)

        relative_urls = response.xpath(
            '//div[@class="col-page-news2"]//li//h2/a/@href | '
            '//ul[@class="clearfix"]/li//div[@class="col-news2-img"]/a/@href | '
            '//ul[@class="clearfix"]/li//div[@class="pull-left"]/a/@href'
        ).getall()
        full_urls = [response.urljoin(url) for url in relative_urls]
        return full_urls

        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        for link, data in self.article_map.items():
            if link in response.url:
                return data.get("title", "").strip()
        return ""
    
    def get_body(self, response) -> str:
        encoding = "iso-8859-1" if response.url.lower().endswith(".pdf") else "utf-8"
        
        if response.url.lower().endswith(".pdf"):
            return ""  # PDFs skipped
        response_body = response.body.decode(encoding, errors="ignore")
        
        # Now pass it to the normalizer if needed
        return body_normalization(response.xpath('//div[@style="min-height:100px;"]//text()').getall())

    
    def get_images(self, response) -> list:
        return ""
    
 
    def date_format(self) -> str:
        return"%Y-%m-%d" 
    
    def get_date(self, response) -> str:
        # Normalize the current URL to match the keys in article_map
        current_url = response.url.replace("https://www.jushi.com", "").split("?")[0]

        if response.url.lower().endswith(".pdf"):
            for link, data in self.article_map.items():
                normalized_link = link.strip().split("?")[0]
                if normalized_link == current_url:
                    return data.get("date", "").strip()
        else:
            # For non-PDF articles, extract date from page content
            extracted_date = response.xpath('substring-before(//div[@class="met_infos"]/span[@class="met_time"]/text(), " ")').get()
            if extracted_date:
                return extracted_date.strip()

        print(f"⚠️ No date found for URL: {response.url}")
        return ""



    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        if not response.headers.get('Content-Type', b'').startswith(b'text/html'):
            return [response.url] if response.url.lower().endswith('.pdf') else []

        pdf_links = response.xpath('//div[@class="container"]//ul/li/a[contains(@href, ".pdf")]/@href').getall()
        return pdf_links if pdf_links else []

        
    def get_page_flag(self) -> bool:
        return False
    
    

    def go_to_next_page(self, response, start_url=None, current_page=None):
        next_page = response.xpath('//li[@class="page-item"]/a[@class="page-link"]/@href').get()
        print("#####NEXT_PAGE (raw):", next_page)

        if next_page:
            next_page_url = response.urljoin(next_page)
            print("#####NEXT_PAGE (full):", next_page_url)

            yield scrapy.Request(
                url=next_page_url,
                callback=self.parse,
                meta={'start_url': start_url},
                dont_filter=True
            )

        
    def extract_articles_with_titles(self, response):
        print("🔍 Extracting articles...")

        # Try default layout first
        articles = response.xpath('//li[@class="clearfix"]')

        # Fallback for other layouts if no articles found
        if not articles:
            articles = response.xpath('//div[@class="col-page-news2"]//li | //ul[@class="clearfix"]/li')

        print(f"🧾 Found {len(articles)} article nodes")

        for idx, article in enumerate(articles, start=1):
            link = article.xpath('.//a/@href').get()
            if not link:
                print(f"⚠️ Article #{idx} missing link")
                continue
            link = link.strip().split(",")[0].split("?")[0]

            title = article.xpath('.//a/@title | .//a/text()').get()
            title = title.strip() if title else ""

            raw_date = article.xpath('.//i/text() | .//span[contains(@class,"date")]/text()').get()
            raw_date = raw_date.strip() if raw_date else ""

            print(f"✅ Article #{idx}:")
            print(f"   📎 Link: {link}")
            print(f"   🏷️ Title: {title}")
            print(f"   📅 Date: {raw_date}")

            self.article_map[link] = {"title": title, "date": raw_date}
            self.article_date_map[link] = raw_date

        print("✅ Completed article extraction.")
