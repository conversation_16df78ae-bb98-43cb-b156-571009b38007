from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ForestProtectionDepartmentVietnam(OCSpider):
    name = "ForestProtectionDepartmentVietnam"
    
    start_urls_names = {
        "http://www.kiemlam.org.vn/Desktop.aspx/List/Tin-hoat-dong/": "	Tin hoạt động",
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"
    
    @property
    def language(self): 
        return ""
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        relative_urls = response.xpath('//div[@class="cssExListSummary"]//h2[@class="cssExListHeadline"]/a/@href | //div[@class="cssExListSummary"]//span[@class="cssExListHeadline5"]/a/@href').getall()
        return [response.urljoin(url) for url in relative_urls]

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="cssDefaultTitle"]/text()' ).get()

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath('//div[@class="cssContent"]//text()').getall()
        )

    def get_images(self, response) -> list:
        relative_urls = response.xpath('//div[@class="cssContent"]//img/@src').getall()
        return [response.urljoin(url) for url in relative_urls]
        
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> Optional[str]: 
        return response.xpath("substring-before(//div[@class='cssDefaultDateTime']/b[last()]/text(), ' ')").get()

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        relative_urls = response.xpath('//td/div/a/@href').getall()
        if not relative_urls:
            return None
        next_url = relative_urls[-1]
        absolute_url = response.urljoin(next_url)
        return absolute_url
