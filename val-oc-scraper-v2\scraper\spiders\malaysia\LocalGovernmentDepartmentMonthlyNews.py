from typing import Optional
from scraper.OCSpider import OCSpider

class LocalGovernmentDepartmentMonthlyNews(OCSpider):
    name = "LocalGovernmentDepartmentMonthlyNews"

    start_urls_names = {
        f"https://jkt.kpkt.gov.my/arkib-berita-bulanan/?selectedYear={year}": "Monthly News"
        for year in range (2025, 2020, -1)
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div//section//div[@class='oxy-dynamic-list']//div[@class='ct-div-block']//a[not(contains(@href, 'category'))]//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div//h1//span//text()").get().strip()
    
    def get_body(self, response) -> str:
        return response.xpath("//div//div[@class='ct-inner-content']//text()").get() or ""
    
    def get_images(self, response) -> list:
        return response.xpath("//div//div[@class='ct-div-block']//a[contains(@href, 'jpeg')]//@href").getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        date = response.xpath("//div//div[@class='ct-text-block']//span[@class='ct-span']//text()").get()
        if date:
            month_map = {
                    'Januari': 'January', 
                    'Februari': 'February', 
                    'Mac': 'March', 
                    'April': 'April',
                    'Mei': 'May', 
                    'Jun': 'June', 
                    'Julai': 'July', 
                    'Ogos': 'August',
                    'September': 'September', 
                    'Oktober': 'October', 
                    'November': 'November', 
                    'Disember': 'December',
                    'Jan': 'January',
                    'Feb': 'February',
                    'Mac': 'March',
                    'Apr': 'April',
                    'Mei': 'May',
                    'Jun': 'June',
                    'Jul': 'July',
                    'Ogos': 'August',
                    'Sep': 'September',
                    'Okt': 'October',
                    'Nov': 'November',
                    'Dis': 'December'
                }
            for malay, eng in month_map.items():
                if malay in date:
                    date = date.replace(malay, eng)
                    break
            return date
        return date
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return response.xpath("//div//section//div[@class='oxy-repeater-pages-wrap']//div[@class='oxy-repeater-pages']//a[@class='next page-numbers']//@href").get()