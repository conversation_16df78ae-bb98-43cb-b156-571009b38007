from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin, unquote

class ChiefGovernmentSecurityOfficeAnnouncements(OCSpider):
    name = "ChiefGovernmentSecurityOfficeAnnouncements"

    start_urls_names = {
        "https://www.cgso.gov.my/en/media/pengumuman/" : "Announcement",
        }

    charset = "utf-8"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    malay_to_eng_month = {
        "Januari": "Jan",
        "Februari": "Feb",
        "Mac": "Mar",
        "April": "Apr",
        "Mei": "May",
        "Jun": "Jun",
        "Julai": "Jul",
        "Ogos": "Aug",
        "September": "Sep",
        "Oktober": "Oct",
        "November": "Nov",
        "Disember": "Dec"
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def get_articles(self, response) -> list: 
        articles = response.xpath('//p[@class="eael-entry-title"]//a/@href').getall()
        print(articles)
        # press_release_data = {}
        # base_url = "https://www.labuanfsa.gov.my/"
        # articles = [] 
        # for entry in response.xpath('//div[contains(@class, "col-a6105836-b393-4aaa-bb9f-a800b3d511e9")]') or response.xpath('//div[contains(@class, "col-3cabb002-0480-40f8-a8a1-f064f6e52d55")]'):
        #     url = entry.xpath('.//a/@href').get()
        #     date = entry.xpath('.//div[contains(@class,"aps-0036-so-wrapper")]//div[@class="a-inner-text"]/text()').get() or entry.xpath('./following-sibling::div[1]//div[@class="a-inner-text"]/text()').get()
        #     if url and date:
        #         full_url = urljoin(base_url, url)
        #         normalized_url = unquote(full_url).rstrip('/')
        #         press_release_data[normalized_url] = date
        #         articles.append(normalized_url)
        # self.article_to_pdf_mapping.update(press_release_data)
        # return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h3[@class="elementor-heading-title elementor-size-default"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('(//div[@class="elementor-widget-container"])[18]//text()').getall())

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response) -> str:
        start_url = response.meta.get('start_url')
        normalized_response_url = unquote(response.url).rstrip('/')
        if "/general-info/media" in start_url or "/general-info/investor-alerts" in start_url:
            date = self.article_to_pdf_mapping.get(normalized_response_url)
            if date:
                return date.strip()
        if  "berita/ucapan" in start_url or "arkib3/pengumuman" in start_url or "berita/siaran-media" in start_url:
            date_text = response.xpath('//time//text()').get().strip()
            if date_text:
                for malay_month, eng_month in self.malay_to_eng_month.items():
                    if malay_month in date_text:
                        date_text = date_text.replace(malay_month, eng_month)
                        break
                return date_text.strip()
        else:
            return response.xpath('//time//text()').get().strip()

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return response.xpath('(//div[@class="elementor-widget-container"])[18]//a/@href').getall()
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        start_url = response.meta.get('start_url')
        if "arkib3/pengumuman" in start_url:
            base_url = "https://www.mof.gov.my"
            url = response.xpath('//a[@aria-label="Go to seterusnya page"]/@href').get()
            full_url= base_url + url if url else None
            return full_url
        else:
            return None