from scraper.OCSpider import OCSpider  
import re 
from urllib.parse import unquote 
 
class EducationServiceCommissionCirculars(OCSpider): 
    name = "EducationServiceCommissionCirculars" 
 
    start_urls_names = { 
        'https://www.spp.gov.my/menupekeliling': 'ministry' 
    } 
 
    start_urls_with_no_pagination_set = {} 
 
    charset = "iso-8859-1" 
     
    country = "Malaysia" 

    # Add content length limit to prevent BigQuery query size issues
    MAX_CONTENT_LENGTH = 500000  # 500KB limit for content
 
    @property 
    def language(self):  
        return "Bahasa Malaysia" 
 
    @property 
    def source_type(self): 
        return 'ministry' 
 
    @property 
    def timezone(self): 
        return "Asia/Kuala_Lumpur" 
     
    article_url_title_date_mapping = {} 
 
    def get_articles(self, response) -> list: 
        articles_collected = [] 
        articles = response.xpath('//div[@class="teaser-item"]') 
        for article in articles: 
            url = article.xpath('.//div[@class="pos-button"]/a/@href').get() 
            full_url = response.urljoin(url) if url else None 
            title = article.xpath('.//h2[@class="pos-title"]/text()').get() 
            date_data = article.xpath('.//p[@class="pos-meta"]/text()').get() 
            pattern = r"\b\d{1,2}\s+\w+\s+\d{4}\b" 
            malay_date = re.search(pattern, date_data or "")  # avoid NoneType error 
            eng_date = None 
            if malay_date: 
                eng_date = self.change_date(malay_date.group()) 
            if eng_date and full_url and title: 
                articles_collected.append(full_url) 
                self.article_url_title_date_mapping[full_url] = [title.strip(), eng_date] 
        return articles_collected 
 
    def get_href(self, entry) -> str: 
        return entry 
 
    def get_title(self, response) -> str: 
        return self.article_url_title_date_mapping.get(unquote(response.url), [""])[0] 
 
    def get_body(self, response) -> str: 
        # Return empty string for PDF documents to avoid large content issues
        content_type = response.headers.get('Content-Type', b'').decode().lower()
        url = response.url
        
        # Check if this is a PDF or document download
        if (url.lower().endswith('.pdf') or 'application/pdf' in content_type 
            or 'format=raw' in url):
            return ""
            
        # For regular HTML pages, extract body text but limit length
        body_text = ""
        if response.encoding is not None:
            # Try to extract main content
            body_elements = response.xpath('//div[@class="item-page"]//text() | //div[@class="content"]//text() | //main//text()').getall()
            if body_elements:
                body_text = ' '.join([text.strip() for text in body_elements if text.strip()])
            
            # Fallback to general body text extraction
            if not body_text:
                body_elements = response.xpath('//body//text()').getall()
                body_text = ' '.join([text.strip() for text in body_elements if text.strip()])
        
        # Truncate content if it's too long to prevent BigQuery issues
        if len(body_text) > self.MAX_CONTENT_LENGTH:
            body_text = body_text[:self.MAX_CONTENT_LENGTH] + "... [Content truncated due to length]"
            
        return body_text
 
    def get_date(self, response) -> str: 
        return self.article_url_title_date_mapping.get(unquote(response.url), ["", ""])[1] 
 
    def date_format(self) -> str: 
        return "%d %B %Y" 
 
    def get_images(self, response) -> list: 
        return [] 
 
    def get_document_urls(self, response, entry=None) -> list: 
        content_type = response.headers.get('Content-Type', b'').decode().lower() 
        url = response.url 
        # If this is already a PDF/document URL, return it
        if (url.lower().endswith('.pdf') or 'application/pdf' in content_type 
            or 'format=raw' in url): 
            return [url] 
        # Look for PDF links on the page
        if response.encoding is not None: 
            pdf_links = response.xpath('//a[contains(translate(@href,"PDF","pdf"),".pdf")]/@href').getall()
            document_urls = []
            for pdf_url in pdf_links:
                full_pdf_url = response.urljoin(pdf_url)
                document_urls.append(full_pdf_url)
            # Also look for other document download links
            download_links = response.xpath('//a[contains(@href, "format=raw") or contains(@href, "download")]/@href').getall()
            for download_url in download_links:
                full_download_url = response.urljoin(download_url)
                if full_download_url not in document_urls:
                    document_urls.append(full_download_url)   
            return document_urls
        return [] 
 
    def get_authors(self, response): 
        return "" 
 
    def get_page_flag(self) -> bool: 
        return False 
     
    def get_next_page(self, response): 
        next_page_url = response.xpath('//a[@class="next"]/@href').get()
        if next_page_url:
            return response.urljoin(next_page_url)
        return None
     
    def change_date(self, malay_date): 
        malay_to_english_months = { 
            "Januari": "January", 
            "Februari": "February", 
            "Mac": "March", 
            "April": "April", 
            "Mei": "May", 
            "Jun": "June", 
            "Julai": "July", 
            "Ogos": "August", 
            "September": "September", 
            "Oktober": "October", 
            "November": "November", 
            "Disember": "December" 
        } 
        try: 
            day, malay_month, year = malay_date.strip().split() 
            english_month = malay_to_english_months.get(malay_month) 
            if not english_month: 
                raise ValueError(f"Unknown month: {malay_month}") 
            return f"{day} {english_month} {year}" 
        except Exception as e: 
            self.logger.error(f"Date parsing error for '{malay_date}': {e}")
            return None