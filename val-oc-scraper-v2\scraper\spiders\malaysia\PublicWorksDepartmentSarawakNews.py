from scraper.OCSpider import <PERSON>CSpider 
from scraper.utils.helper import body_normalization
import re
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class PublicWorksDepartmentSarawakNews(OCSpider):
    name = "PublicWorksDepartmentSarawakNews"
    
    start_urls_names = {
        'https://jkr.sarawak.gov.my/web/subpage/news_list/': 'news list'
    }

    start_urls_with_no_pagination_set = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        request.meta['current_page'] = response.request.meta.get('current_page', 2)
        yield request

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="table-body-cell table-action"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="list-title"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="list-content"]//text()').getall())

    def get_date(self, response) -> str:
        date_data =  response.xpath('//div[@class="list-sub"]//text()').get()
        if date_data:
            date = re.search(r'\d+ \w+ \d+', date_data)
            if date: 
                return date.group(0)

    def date_format(self) -> str:
        return "%d %b %Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="list-content"]//img//@src').getall()
    
    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        next_page = response.meta.get("current_page")
        next_page += 1
        next_url = f"https://jkr.sarawak.gov.my/web/subpage/news_list_ajax/?page={str(next_page)}&sort=&order_by=date&s=&m=&y=&category=&extra_cat_param="
        return next_url
    
    def go_to_next_page(self, response, start_url, current_page=None):
        hbp = HeadlessBrowserProxy()
        next_page_url = self.get_next_page(response)
        if next_page_url is not None:
            request = scrapy.Request(hbp.get_proxy(next_page_url, timeout=10000), callback=self.parse
            )
            request.meta['start_url'] = start_url
            request.meta['current_page'] = int(response.request.meta.get('current_page', )) + 1
            yield request
        else:
            yield None