from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization

class HaNoiMoiNewspaperofVietnam(OCSpider):
    name = "HaNoiMoiNewspaperofVietnam"

    start_urls_names = {
        'https://hanoimoi.vn/chinh-tri': 'news'
    }

    start_urls_with_no_pagination_set = {
        'https://hanoimoi.vn/chinh-tri' # Pagination is not supported
    }

    charset = "utf-8"

    country = "Vietnam" 

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return list(set(response.xpath('//div[@class="b-grid__img"]//a/@href').getall() ))
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="sc-longform-header-title block-sc-title"]//text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="c-news-detail"]//p//text()').getall() )
        
    def get_date(self, response) -> str:
        date_data = response.xpath('//span[@class="sc-longform-header-date block-sc-publish-time"]//text()').get()
        date = re.search(r"\d{2}/\d{2}/\d{4}", date_data or '')
        if date:
            return date.group(0)
        else:
            ValueError(f"Date not found for url = {response.url}")
       
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="entry entry-no-padding"]//img/@src').getall()

    def get_document_urls(self, response, entry=None)->list:
        return []
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None