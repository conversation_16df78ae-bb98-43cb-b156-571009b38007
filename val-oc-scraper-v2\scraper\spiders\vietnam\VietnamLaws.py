from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
import re
from datetime import datetime
class VietnamLaws(OCSpider):
    name = "VietnamLaws"

    start_urls_names = {
        "https://thuvienphapluat.vn/chinh-sach-phap-luat-moi/vn/thong-bao-van-ban-moi": "Notice" # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://thuvienphapluat.vn/chinh-sach-phap-luat-moi/vn/thong-bao-van-ban-moi"
    }
     
    charset = "utf-8" 
    
    country = "Vietnam"
    
    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        return response.xpath('//li[@class="liTitle"]//a//@href | li[@class="tt"]//a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="divModelDetail"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="divAutho"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath('//span[@class="spanDay visible-xs-block"]//text()').get()
        match = re.search(r"\b(\d{2}/\d{2}/\d{4})\b", date)
        date_str = match.group(1) 
        dt = datetime.strptime(date_str, "%d/%m/%Y")
        return dt.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None