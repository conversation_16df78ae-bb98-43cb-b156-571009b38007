
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from typing import Optional
from datetime import datetime
import re
import scrapy
import urllib.parse
#HaNoiPortalofVietnam

class HaiPhongPortal(OCSpider):
    name = "HaiPhongPortal"

    start_urls_names = {
        "https://haiphong.gov.vn/tin-tuc-su-kien": "News",
        }
    

    api_start_url = {
        "https://haiphong.gov.vn/tin-tuc-su-kien": {
            'url': "https://haiphong.gov.vn/DesktopModule/UIArticleInMenu/ArticleInMenuPagination.aspx/LoadArticle",
            "payload" : {
                "article_category_id" : "75892",
                "categoryIds":"75892, 75963, 76588",
                "site_id":"4",
                "page":"1",
                "page_size":"15",
                "keyword":"",
                "date_begin":"",
                "date_end": "",
                "show_no": "False",
                "show_post_date":"True",
                "num_of_text":"160",
                "show_view_count":"False",
                "filter_order_in_list":"False",
                "is_default":"False",
                "new":"False",
                "number_of_day":"3",
                "no":"-15",
                "articlelang":"vi-VN",
                "is_authenticated":"False"

            }
        }
    }

    

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["page"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
        
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//h2[@class="Title"]//a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="ArticleHeader"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="ArticleContent"]//p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//figure//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//div[@class="PostDate"]//text()').get()
        date_obj = dateparser.parse(date, languages=['vi'])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        
        return int(response.meta.get("current_page")) + 1

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_url.get(start_url)
        if not api_data:
           self.logger.error("API data not found for start_url")
           return

        api_url = api_data["url"]
        next_page = self.get_next_page(response, current_page)
        

        if next_page:
          payload = response.meta.get("payload", {}).copy()
          payload["page"] = str(next_page)

          yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            formdata=payload,
            callback=self.parse_intermediate,
            meta={"current_page": next_page, "start_url": start_url, "payload": payload},
            dont_filter=True,
           )
        else:
         return None
         
