from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization 
from urllib.parse import urlparse, parse_qs
from typing import Optional

class ArizonaAttorneyGeneral(OCSpider):
    name = "ArizonaAttorneyGeneral"
    
    country = "US"

    start_urls_names = {
        "https://www.azag.gov/press-releases": "Newsroom",
    }

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # for using geo targeted proxy, add this middleware
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }
    
    charset = "utf-8"
    
    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Phoenix"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="view-content"]//div[@class="views-field views-field-title"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="region region-header"]//h1[@class="page-header"]/span/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="field field--name-body field--type-text-with-summary field--label-hidden field--item"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="field field--name-field-date-of-release field--type-datetime field--label-hidden field--item"]/time/@datetime').get().split("T")[0]
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="pager__item pager__item--next"]/a/@href').get()
        last_page = response.xpath('//li[@class="pager__item pager__item--last"]/a/@href').get()
        current_page = int(parse_qs(urlparse(response.url).query).get('page', [1])[0])
        last_page_num = int(parse_qs(urlparse(last_page).query).get('page', [None])[0]) if last_page else None
        if last_page_num is None or current_page < last_page_num:
           return response.urljoin(next_page)
        else:
            return None