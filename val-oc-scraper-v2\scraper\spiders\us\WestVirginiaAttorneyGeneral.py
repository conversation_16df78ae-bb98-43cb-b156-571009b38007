from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class WestVirginiaAttorneyGeneral(OCSpider):
    name = "WestVirginiaAttorneyGeneral"

    country = "US"

    start_urls_names = {
        "https://ago.wv.gov/pressroom/Pages/default.aspx": "Press Room",
        }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="newsitem"]//b/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="allaroundpadding"]//h2/span/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="ms-rtestate-field"]//p/text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> str:
        date_raw = response.xpath('//div[@class="ms-rtestate-field"]/preceding::p[1]/text()').get()
        return date_raw.replace(u'\xa0', u' ').strip() 
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response) -> Optional[str]: 
        return None