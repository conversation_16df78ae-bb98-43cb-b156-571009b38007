from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import logging
from typing import Optional
import scrapy

class AlbamaOfficeOfAttorneyGeneral(OCSpider):
    name = 'AlbamaOfficeOfAttorneyGeneral'
    
    country = "US"

    start_urls_names = {
        'https://www.alabamaag.gov/news/': 'News'
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h3[@class='elementor-post__title']//a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath("//div[@class='elementor-element elementor-element-b6aa317 elementor-widget elementor-widget-theme-post-title elementor-page-title elementor-widget-heading']/div/h1/a/@href").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class= 'elementor-widget-container']//p//text()").getall())
    
    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class= 'elementor-widget-container']/p/text()[2]").re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.xpath("//div//div[@class= 'elementor-widget-container']/div/div/a/@href").get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        if response.status != 200:
            self.logger.error(f"Error {response.status}: Not incrementing page.")
            return None
        else:
            return str(int(current_page) + 1)
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page = self.get_next_page(response, current_page)
        url=f'https://www.alabamaag.gov/news/page/{next_page}/'
        if next_page:
            yield scrapy.Request(
                url=url,
                method='GET',
                callback=self.parse, 
                dont_filter=True,
                 meta={
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None