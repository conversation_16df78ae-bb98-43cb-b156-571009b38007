from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy, json
import datetime

class TheEducationandTimesNewspaper(OCSpider):
    name = "TheEducationandTimesNewspaper"

    start_urls_names = {
        "https://giaoducthoidai.vn/giao-duc/": "News" # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://giaoducthoidai.vn/giao-duc/"
    } 

    api_start_url = {
        'https://giaoducthoidai.vn/giao-duc/':
        'https://api.giaoducthoidai.vn/api/morenews-zone-17-3.html?phrase=&sz=17&st=zone',
        }
    
    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self,response):
        start_url = response.meta.get("start_url")
        api_url = self.api_start_url[start_url]
        print(api_url)
        yield scrapy.Request(
            url = api_url,
            method = "GET",
            callback = self.parse,
            meta = {
                "start_url" : start_url,
                "api_url" : api_url 
            }
        )

    articles_to_date = {}
    
    def get_articles(self, response) -> list:  
        articles = []
        map = {}
        try :
            data = json.loads(response.text)
            for item in data.get("data", {}).get("contents", []):
                title = item.get("title")
                url = item.get("url")
                ts = item.get("date")
                date = None
                if isinstance(ts, (int, float)):  
                    try:
                        date = datetime.datetime.utcfromtimestamp(ts).strftime("%d/%m/%Y")
                    except Exception as e:
                        print("Date conversion error:", ts, e)
                if url and date :
                    articles.append(url)
                    map[url] = {
                        'title' : title,
                        'date' : date
                    }
            self.articles_to_date.update(map)
            return articles
        except Exception as e:
            return 

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.articles_to_date.get(response.url,{}).get('title','')
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@itemprop="articleBody"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return self.articles_to_date.get(response.url,{}).get('date','')

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None