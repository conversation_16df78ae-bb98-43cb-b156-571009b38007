import re
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization


class NationalInternetFinanceAssociationOfChina(OCSpider):

    name = "NationalInternetFinanceAssociationOfChina"

    proxy_country = "cn"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # for using geo targeted proxy, add this middleware
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }

    start_urls_names = {
         'https://www.nifa.org.cn/nifa/2955707/2955765/index.html': '',
         'https://www.nifa.org.cn/nifa/2955689/2955725/index.html': '',
         'https://www.nifa.org.cn/nifa/2955675/2955761/index.html': '',
         'https://www.nifa.org.cn/nifa/2955675/2955763/index.html': ''
    }

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    def get_page_flag(self) -> bool:
        return False

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = response.xpath("//div[@class='portlet']//tbody//tbody//a/@href").extract()
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//td[@class='dabiaoti']/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='portlet']//p/text()").extract())

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        date_str = response.xpath("//div[@class='portlet']//tbody//tbody/tr/td/text()").extract()[1]

        match = re.search(r'(\d{4})年(\d{2})月(\d{2})日', date_str)

        if match:
            # Extract year, month, and day
            year = match.group(1)
            month = match.group(2)
            day = match.group(3)

            # Format the date as YYYY-MM-DD
            formatted_date = f'{year}-{month}-{day}'
        else:
            formatted_date = ''
        return formatted_date


    def get_images(self, response) -> list:
        images = []
        for imgTag in response.xpath("//div[@class='portlet']//img/@src").extract():
            images.append(response.urljoin(imgTag))
        return images

    def get_authors(self, response):
        return []

    def get_next_page(self, response) -> str:
        current_url = response.url

        if current_url.endswith('index.html'):
            current_page = 0
        else:
            current_page = int(current_url.split('_')[-1].split('.')[0])

        next_page_url = response.urljoin(f"index_{current_page + 1}.html")
        return next_page_url
