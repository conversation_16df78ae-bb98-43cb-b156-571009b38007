#Economics & Urban Newspaper

from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class EconomicsandUrbanNewspaper(OCSpider):
    name = "EconomicsandUrbanNewspaper"

    start_urls_names = {
        "https://kinhtedothi.vn/kinh-te?com-270=page-1": "public-security-News",
        }

    charset = "iso-8859-1"

    country = "Vietnam"

    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    HEADLESS_BROWSER_WAIT_TIME = 30000

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//div[@class="section-container"]//h3//a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//*[@id="print-area"]/div[5]/p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//*[@id="content"]/div/div[1]/div[1]/div/div[2]/div/div[5]/div//span/span/em/img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//*[@id="print-area"]/div[1]/div//text()').get()
        date_obj = dateparser.parse(date, languages=['vi'])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None
