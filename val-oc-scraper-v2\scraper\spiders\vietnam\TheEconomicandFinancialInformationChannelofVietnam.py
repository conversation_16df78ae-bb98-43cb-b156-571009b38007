from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re
import scrapy

class TheEconomicandFinancialInformationChannelofVietnam(OCSpider):
    name = "TheEconomicandFinancialInformationChannelofVietnam"

    start_urls_names = {
        'https://cafef.vn/xa-hoi.chn': 'Economic and Financial Information Channel of Vietnam',# Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://cafef.vn/xa-hoi.chn'
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return 'unknown'

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def parse(self, response):
        response.meta.setdefault("start_url", response.url)
        yield from super().parse(response)
        if self.get_page_flag():
            current_page = response.meta.get("current_page", 1)
            start_url = response.meta["start_url"]  
            yield from self.go_to_next_page(response, start_url=start_url, current_page=current_page)

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="hl-info"]//a/@href | //div[@class="big "]//h3//a/@href | //div[@class="tlitem box-category-item"]//h3//@href | //div[@class="tlitem"]//a/@href | //h3[@class="title"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="title"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="w640 fr clear"][1]//h2/text() | //div[@class="w640 fr clear"][1]//p/text()').getall())

    def get_date(self, response) -> str:
        date = response.xpath('//p[@class="dateandcat"]//span/text()').get()
        match = re.search(r"\b\d{2}-\d{2}-\d{4}\b", date)
        return match.group(0) if match else ""

    def date_format(self) -> str:
        return "%d-%m-%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//figure//img/@src').getall()   

    def get_document_urls(self, response, entry=None) -> list[str]:
        return []   

    def get_authors(self, response) -> str :
        return response.xpath('//p[@class="author"]//text()').get()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: int):
        articles = self.get_articles(response)
        if not articles:
            return None
        next_page = current_page + 1
        next_url = f"https://cafef.vn/timelinelist/188112/{next_page}.chn"
        return next_url, next_page
    
    def go_to_next_page(self, response, start_url=None, current_page: int = 1):
        result = self.get_next_page(response, current_page)
        if not result:
            self.logger.info("No more pages to fetch.")
            return
        next_url, next_page = result
        yield scrapy.Request(
            url=next_url,
            callback=self.parse,
            meta={"page": next_page, "start_url": start_url}
    )

