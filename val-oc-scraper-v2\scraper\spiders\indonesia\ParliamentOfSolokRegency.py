from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfSolokRegency(OCSpider):
    name = "ParliamentOfSolokRegency"

    start_urls_names = {
        "https://dprd.solokkab.go.id/berita": "ALLNEWS"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:  
        return response.xpath('//h4[@class="visible-xs"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="article-title"]/h1/b/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="column12"]//p//text() | //div[@class="column12"]//text()').getall())
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %b %Y, %H:%M:%S"
    
    def get_date(self, response) -> str:
        date = response.xpath('//div[@class="a-content"]/span[2]/text()').get().strip()
        return date.replace('WIB', '').strip()
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None