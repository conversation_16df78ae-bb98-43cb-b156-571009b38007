from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class CompaniesCommissionOfMalaysia(OCSpider):
    name = "CompaniesCommissionOfMalaysia"
    
    start_urls_names = {
        "https://www.mycc.gov.my/bm/pengumuman": "News",
        "https://www.mycc.gov.my/bm/kenyataan-media":"News",
        "https://www.mycc.gov.my/bm/kes": "Guidance",
        "https://www.mycc.gov.my/bm/aku-janji": "Cases",
        "https://www.mycc.gov.my/bm/garis-panduan-muktamad": "News"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "FS"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map ={}

    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//div[@class='region region-content']//div[@class='inner-layer'] | //div[@class='view-content']//div[@class='tab-content']//tbody//tr | //div[@class='view-content']//div[@class='table-responsive']//tbody//tr | //div[@class='view-content']//div[@class='field-content']"): 
            url = article.xpath(".//div[@class='announce-title']//a//@href | .//div[@class='mediaR-title']//a//@href | .//div[@class='pdf-file']//a//@href | .//div[@class='handbook-wrapper']//a//@href").get()
            title=article.xpath(".//div[@class='announce-title']//a//text() | .//div[@class='mediaR-title']//a//text() | .//td[@class='views-field views-field-title']//text() | .//div[@class='handbook-title']//text()").get()
            date =article.xpath(".//div[@class='announce-date']//time//text() | .//div[@class='mediaR-date']//time//text() | .//td[@class='views-field views-field-field-date']//text() | .//div[@class='handbook-date']//time//text()").get()
            if url and title and date:
                full_url = url
                title= title.strip()
                date= date.strip()
                self.article_data_map[full_url]={"title": title, "date": date}
                articles.append(full_url)
        return articles    
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        if 'pdf' not in response.url.lower():
            return body_normalization(response.xpath("//div[@class='content']//p//text()").getall()) 
        return ""
    
    def get_images(self, response) -> list:
        return []
  
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> Optional[str]:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(date, languages=['ms',"en"])
        return parsed_date.strftime("%Y-%m-%d")

    def get_document_urls(self, response, entry=None) -> list:
        if 'pdf' in response.url.lower():
            return [response.url]
        return response.xpath("//div[@class='NP-pdf']//a//@href").getall()
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath("//ul//li[@class='pager__item pager__item--next']/a/@href").get()
        if  next_page:
           return next_page
        return None           