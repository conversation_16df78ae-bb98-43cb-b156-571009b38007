from datetime import datetime
from typing import List
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class ParliamentOfSelayarIslandsRegency(OCSpider):
    name = "ParliamentOfSelayarIslandsRegency"

    start_urls_names = {
        "https://dprd.kepulauanselayarkab.go.id/berita/": "Berita",
        # "https://dprd.kepulauanselayarkab.go.id/download": "",   No dates in anywhere for the pdf
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"
    
    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "post-thumbnail-wrap"]//div[@class= "post-thumbnail"]/a/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h2//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="wpb_wrapper"]//div//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        MONTH_MAP = {
            'Januari': 'January',
            'Februari': 'February',
            'Maret': 'March',
            'April': 'April',
            'Mei': 'May',
            'Juni': 'June',
            'Juli': 'July',
            'Agustus': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Desember': 'December',
            'Des': 'Dec' 
        }
        raw_date = response.xpath('//em[@class="f12"]/text()').get()
        if not raw_date:
            return None
        raw_date = raw_date.strip()
        if ',' in raw_date:
            raw_date = raw_date.split(',', 1)[1].strip()
        for indo, eng in MONTH_MAP.items():
            if indo in raw_date:
                raw_date = raw_date.replace(indo, eng)
                break
        parts = raw_date.split()
        if len(parts) >= 3:
            date_part = " ".join(parts[:3])
        else:
            return None
        for fmt in ("%d %b %Y", "%d %B %Y"):
            try:
                date_obj = datetime.strptime(date_part, fmt)
                return date_obj.strftime("%Y-%m-%d")
            except ValueError:
                continue
        return None

    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class= "elementor-widget-container"]//figure//img/@src').getall() 
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page =  response.xpath("//div[@class='pagination']//a[@rel='next']/@href").get() 
        if next_page :
            return next_page
        return None