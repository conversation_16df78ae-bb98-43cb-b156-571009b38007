from typing import Optional
from scraper.OCSpider import OCSpider
import re
from urllib.parse import quote, urlparse

class MinistryOfRuralAndRegionalDevelopmentSpeeches(OCSpider):
    name = "MinistryOfRuralAndRegionalDevelopmentSpeeches"
    
    start_urls_names = {
        f"https://www.rurallink.gov.my/ucapan-menteri-{year}/": "Speeches"
        for year in range(2025, 2019, -1)
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}  # Mapping title to child articles from start URL
    
    def get_articles(self, response) -> list:
        articles = []
        for section in response.xpath("//div[@class='eael-accordion-list']//ul//li"):
            url = section.xpath(".//a//@href").get()
            title = section.xpath(".//a//text()").get()
            if url and title:
                # Normalize the URL first
                normalized_url = self._normalize_url(url.strip())
                if not normalized_url:
                    self.logger.warning(f"Skipping invalid URL: '{url}' (title: '{title}')")
                    continue
                # Join with base URL - this is crucial for relative URLs
                try:
                    full_url = response.urljoin(normalized_url)
                    # Final validation of the constructed URL
                    parsed = urlparse(full_url)
                    if not all([parsed.scheme, parsed.netloc]):
                        self.logger.warning(f"Skipping malformed final URL: '{full_url}' (original: '{url}')")
                        continue
                    # Check if final URL still has unencoded spaces
                    if ' ' in full_url:
                        self.logger.warning(f"Final URL still contains spaces, skipping: '{full_url}'")
                        continue 
                    self.article_data_map[full_url] = {"title": title.strip()}
                    articles.append(full_url)
                    self.logger.debug(f"Added valid URL: '{full_url}'")  
                except Exception as e:
                    self.logger.error(f"Error processing URL '{url}': {e}")
                    continue       
        total_links_xpath = '//div[@class="eael-accordion-list"]//ul//li'
        self.logger.info(f"Found {len(articles)} valid article URLs out of {len(response.xpath(total_links_xpath))} total links")
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m"
    
    def get_date(self, response, entry=None) -> int:
        match = re.match(r".*/(\d{4})/(\d{2})/.*", response.url)
        if match:
            year, month = match.groups()
            return f"{year}-{month}"
        else:
            return None
    
    def get_document_urls(self, response, entry=None) -> list:
        if '.pdf' in response.url:
            return response.url
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to crawl
        return None
    
    def _normalize_url(self, url):
        """
        Normalize and validate URL to handle spaces and special characters
        """
        if not url:
            return None    
        # Remove leading/trailing whitespace
        url = url.strip()
        # Skip if it doesn't look like a valid URL
        if not url.startswith(('http://', 'https://', '/')):
            self.logger.warning(f"Skipping invalid URL format: {url}")
            return None   
        try:
            # Parse the URL to handle encoding properly
            parsed = urlparse(url)
            # If it's a relative URL, we'll let response.urljoin handle it
            if not parsed.scheme:
                return url   
            # For absolute URLs, encode the path and query components
            if ' ' in url or any(ord(char) > 127 for char in url):
                # Split URL into base and path
                base_url = f"{parsed.scheme}://{parsed.netloc}"
                path_and_query = url[len(base_url):]
                # Encode the path and query part
                encoded_path = quote(path_and_query, safe='/?&=')
                normalized_url = base_url + encoded_path
                self.logger.info(f"Normalized URL: {url} -> {normalized_url}")
                return normalized_url  
            return url 
        except Exception as e:
            self.logger.error(f"Error normalizing URL '{url}': {e}")
            return None
    
    def handle_http_error(self, failure):
        """
        Override the base class error handler to properly handle ValueError exceptions
        """
        try:
            # Check if it's a ValueError (invalid URI)
            if isinstance(failure.value, ValueError):
                self.logger.error(f"Invalid URI error: {failure.value}")
                return None
            # For other errors, call the parent handler if it exists
            if hasattr(super(), 'handle_http_error'):
                return super().handle_http_error(failure)
            else:
                self.logger.error(f"HTTP error: {failure}")
                return None      
        except Exception as e:
            self.logger.error(f"Error in error handler: {e}")
            return None