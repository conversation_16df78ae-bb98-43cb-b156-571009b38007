from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re, scrapy , datetime

class JournalofAuditingStudiesofVietnam(OCSpider):
    name = "JournalofAuditingStudiesofVietnam"

    start_urls_names = {
        "http://www.khoahockiemtoan.vn/8-1/tin-tuc.sav": "News"
    }

    start_urls_with_no_pagination_set = {}

    api_start_url = {
        "http://www.khoahockiemtoan.vn/8-1/tin-tuc.sav":
        "http://www.khoahockiemtoan.vn/8-3-trang-{page}/tin-tuc.sav",
        }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self,response):
        start_url = response.meta.get("start_url")
        api_url = self.api_start_url[start_url]
        current_page = response.meta.get("current_page",1)
        url = api_url.format(page=current_page)
        yield scrapy.Request(
            url = url,
            method = "GET",
            callback = self.parse,
            dont_filter= True,
            meta = {
                "start_url" : start_url,
                "api_url" : api_url ,
                "current_page" : current_page
            }
        )
    
    def get_articles(self, response) -> list:  
        return response.xpath('//a[@class="CssHomeTitleSmallNotBold1"]//@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="CssNewsTitle"]//span//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//table[@class="table borderless"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"
            
    def get_date(self, response) -> str:
        formats = ["%d/%m/%Y", "%m/%d/%Y"]
        #some child url does not contain year part in article
        try:
            date_text = "".join(
                response.xpath('(//div[@class="CssNewsNote"]//span)[2]//text()').getall()
            ).strip()

            formats = ["%d/%m/%Y", "%m/%d/%Y"]
            full_match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', date_text)
            if full_match:
                date_str = full_match.group(1)
                for fmt in formats:
                    try:
                        dt = datetime.datetime.strptime(date_str, fmt)
                        return dt.strftime("%Y-%m-%d")
                    except ValueError:
                        continue

            dm_match = re.search(r'(\d{1,2}/\d{1,2})(?!/\d{4})', date_text)
            year_match = re.search(r'(\d{4})', date_text)

            if dm_match and year_match:
                date_str = f"{dm_match.group(1)}/{year_match.group(1)}"
                for fmt in formats:
                    try:
                        dt = datetime.datetime.strptime(date_str, fmt)
                        return dt.strftime("%Y-%m-%d")
                    except ValueError:
                        continue
            return None  
        except Exception as e:
            return None

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = int(response.meta.get("current_page")) + 1
        if response.status == 200:
            return str(next_page)
        return 
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        api_url = response.meta.get("api_url")
        next_page = self.get_next_page(response)
        if next_page :
            url = api_url.format(page=next_page)
            yield scrapy.Request(
            url = url,
            method = "GET",
            callback = self.parse_intermediate,
            meta = {
                "start_url" : start_url,
                "api_url" : api_url ,
                "current_page":next_page
            }
        )
