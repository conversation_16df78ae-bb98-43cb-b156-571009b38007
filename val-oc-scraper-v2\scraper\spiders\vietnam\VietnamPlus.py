from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization

class VietnamPlus(OCSpider):
    name = "VietnamPlus"

    start_urls_names = {
        'https://www.vietnamplus.vn/topic/tin-moi-nhan-111.vnp': 'news'
    }

    start_urls_with_no_pagination_set = {
        'https://www.vietnamplus.vn/topic/tin-moi-nhan-111.vnp' # Pagination is not suported
    }
    
    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return 'unknown'

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    @property
    def language(self):
        return "Vietnamese"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="timeline secondary content-list"]//a[@class=" cms-link"]/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="article__title cms-title "]/text()').get() or ''
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article__body zce-content-body cms-body"]//p//text()').getall())
        
    def get_date(self, response) -> str:
        date_data = response.xpath('//time[@class="time"]/text()').get()
        date = re.search(r"\d{2}/\d{2}/\d{4} \d{2}:\d{2}", date_data)
        if date:
            return date.group(0)
        else:
            ValueError(f"Date not found for url = {response.url}")
       
    def date_format(self) -> str:
        return "%d/%m/%Y %H:%M"

    def get_images(self, response) -> list[str]:
        return response.xpath('//img[@class="cms-photo"]/@src').getall()

    def get_document_urls(self, response, entry=None)->list:
        return []
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None
   