from scraper.OCSpider import OCSpider
import dateparser
import re
from datetime import datetime
from scraper.utils.helper import body_normalization


class MalaysiaQualificationsAgencyCirculars(OCSpider):
    name = "MalaysiaQualificationsAgencyCirculars"

    start_urls_names = {
        "https://www.mqa.gov.my/new/pub_circular_2025.cfm#gsc.tab=0": "Notification Letters"
    }

    start_urls_with_no_pagination_set = {
        "https://www.mqa.gov.my/new/pub_circular_2025.cfm#gsc.tab=0"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for row in response.xpath("//table[@class='table table-bordered table-striped']/tbody/tr"):
            url = row.xpath(".//td[3]//a/@href").get()
            title = body_normalization(row.xpath(".//td[2]//text()").getall())
            if not url or not title:
                continue
            date_candidates = [
                row.xpath(".//td[1]//text()").get() or "",
                row.xpath(".//td[4]//text()").get() or "",
                row.xpath(".//td[5]//text()").get() or "",
                title
            ]
            url = response.urljoin(url.strip())
            self.article_data_map[url] = {
                "title": title,
                "full_url": url,
                "date_candidates": [candidate.strip() for candidate in date_candidates if candidate.strip()]
            }
            articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date_pattern = r'(\d{1,2}\s+(?:JANUARI|FEBRUARI|MAC|APRIL|MEI|JUN|JULAI|OGOS|SEPTEMBER|OKTOBER|NOVEMBER|DISEMBER)\s+\d{4})'
        date_candidates = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date_candidates", [])
        for candidate in date_candidates:
            date_match = re.search(date_pattern, candidate, re.IGNORECASE)
            if date_match and (parsed_date := dateparser.parse(date_match.group(1), languages=["ms"], settings={"PREFER_DAY_OF_MONTH": "first"})):
                return parsed_date.strftime(self.date_format())
        return datetime.now().strftime(self.date_format())

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None