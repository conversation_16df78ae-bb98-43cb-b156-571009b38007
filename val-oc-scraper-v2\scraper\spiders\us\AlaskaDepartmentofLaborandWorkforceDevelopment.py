from scraper.utils.helper import body_normalization
from scraper.OCSpider import OCSpider
import scrapy

class AlaskaDepartmentOfLaborAndWorkforceDevelopment(OCSpider):
    name = 'AlaskaDepartmentOfLaborAndWorkforceDevelopment'

    country = "US"

    start_urls_names = {
        "https://gov.alaska.gov/newsroom/page/1/?el_dbe_page": "News"
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url", response.url)
        yield scrapy.Request(
            url=start_url,
                callback=self.parse,
                meta={                 
                    'start_url': start_url
                },
                dont_filter=True
            )

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "America/Anchorage"
    
    @property
    def language(self):
        return "English"
    
    def get_articles(self, response) -> list:
        return list(set(response.xpath("//div[@class='post-content']//a//@href").getall()))
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='et_pb_module et_pb_post_content et_pb_post_content_0_tb_body']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%b %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//p[@class='et_pb_title_meta_container']//text()").get()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        page_number = int(response.url.split('/')[-2])
        print("THE PAGE NO IS :", page_number)
        if response.status == 200:
            return f"https://gov.alaska.gov/newsroom/page/{page_number + 1}/?el_dbe_page"
        else:
            return None
    
    def go_to_next_page(self, response, start_url, current_page=None):
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=next_page,
                callback=self.parse_intermediate,
                dont_filter=True
            )