from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
from datetime import datetime
from scraper.utils.helper import body_normalization
import scrapy
import re
from typing import Optional

class DepartmentOfThePremierOfSarawakNews(OCSpider):
    name = "DepartmentOfThePremierOfSarawakNews"

    country = "Malaysia"

    start_urls_names = {
        "https://premierdept.sarawak.gov.my/web/subpage/news_list_ajax/?page=1&sort=&order_by=date&s=&m=&y=&category=&extra_cat_param=&": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request 

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='table-body-cell table-action']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='list-title']//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='list-content']//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='list-content']//img//@src").getall()

    def date_format(self) -> str:
        return "%d %b %Y"
 
    def get_date(self, response):
        raw_date = response.xpath('//div[@class="list-sub"]//h5[contains(text(), "Disiarkan pada")]/text()').get()
        if raw_date:
            raw_date = raw_date.replace("Disiarkan pada", "").strip()
            try:
                dt = datetime.strptime(raw_date, "%d %b %Y")  
                return dt.strftime(self.date_format())
            except ValueError:
                return ""
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return []

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        current_url = response.url        
        match = re.search(r".*%3Fpage%3D(\d+)", current_url)       
        if match:
            current_page_num = int(match.group(1))
            next_page_num = current_page_num + 1
            next_page_url = current_url.replace(
                f"%3Fpage%3D{current_page_num}",
                f"%3Fpage%3D{next_page_num}"
            )          
            return next_page_url
        else:
            return None