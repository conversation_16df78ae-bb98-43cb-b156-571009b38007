from typing import Optional
from scraper.OCSpider import OCSpider
from datetime import datetime
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse, unquote
import re

class OregonEmploymentDepartment(OCSpider):
    name = "OregonEmploymentDepartment"

    start_urls_names = {
        "https://www.oregon.gov/employ/newsandmedia/pages/archives.aspx": "News Release Archives",
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    charset = "iso-8859-1"

    article_date_map = {}

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/Los_Angeles"

    def normalize_url(self, url: str) -> str:
        return unquote(url.strip()).split('?')[0] if url else url

    def normalize_response_url(self, response) -> str:
        return self.normalize_url(response.url)

    def get_articles(self, response) -> list:
        rows = response.xpath('//tbody/tr')
        urls = []
        for row in rows:
            link = row.xpath('.//td[2]/a/@href').get()
            title = row.xpath('.//td[2]/a/text()').get()
            raw_date = row.xpath('.//td[3]//text()').get()
            if not link:
                continue
            abs_link = response.urljoin(link.strip())
            norm_url = self.normalize_url(abs_link)
            clean_title = title.strip() if title else norm_url.split('/')[-1]
            clean_date = None
            if raw_date:
                date_parts = raw_date.strip().split()
                if date_parts:
                    clean_date = date_parts[0]
            if not clean_date:
                match = re.search(r'(\d{4})-(\d{2})-(\d{2})', norm_url)
                if match:
                    year, month, day = match.groups()
                    clean_date = f"{month}/{day}/{year}"
            self.article_date_map[norm_url] = {
                "title": clean_title,
                "date": clean_date,
            }
            urls.append(abs_link)
        return urls

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        norm_url = self.normalize_response_url(response)
        return self.article_date_map.get(norm_url, {}).get("title", norm_url.split('/')[-1])

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> Optional[datetime]:
        norm_url = self.normalize_response_url(response)
        meta = self.article_date_map.get(norm_url, {})
        return meta.get("date")

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url] if response.url.lower().endswith(".pdf") else []

    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response) -> Optional[str]:
        current_url = response.url
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)
        current_page = int(query_params.get('wp7099', ['p:0'])[0].split(':')[-1])
        next_page_num = current_page + 1
        query_params['wp7099'] = f'p:{next_page_num}'
        new_query = urlencode(query_params, doseq=True)
        next_url = urlunparse(parsed_url._replace(query=new_query))
        return next_url if next_url != current_url else None
