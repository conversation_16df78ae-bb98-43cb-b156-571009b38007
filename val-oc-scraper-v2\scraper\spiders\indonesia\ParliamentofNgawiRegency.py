from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentofNgawiRegency(OCSpider):
    name = "ParliamentofNgawiRegency"
    
    country = "ID"

    start_urls_names = {
        "https://dprd.ngawikab.go.id/berita/" : "News",
        "https://jdih-dprd.ngawikab.go.id/" : "JDIH",# no artical
        "https://dprd.ngawikab.go.id/ppid/" : "PPID", #no artical
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h2[@class='entry-title ast-blog-single-element']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='entry-title']/text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='entry-content clear']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='entry-content clear']//img/@src").getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//div[contains(@class, 'jp-relatedposts-items')]//time[@class='jp-relatedposts-post-date']/text()").get().strip()
    
    def get_authors(self, response):
        return response.xpath("//div[@class='entry-meta']//span[@class='author-name']/text()").getall()
    
    def get_page_flag(self) -> bool:
        return True
     
    def get_next_page(self, response): 
        return response.xpath("//div[@class='ast-pagination']//a[contains(@class, 'next')]/@href").get()


