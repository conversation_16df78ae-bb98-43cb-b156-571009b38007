from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfSouthSulawesi(OCSpider):
    name = "ParliamentOfSouthSulawesi"

    start_urls_names = {
        "https://dprd.sulselprov.go.id/web/page/berita": "ALLNEWS",
        # "https://dprd.sulselprov.go.id/web/page/regulasi/4/peraturan-daerah" : "", # No date on child articles
        # "https://dprd.sulselprov.go.id/web/page/regulasi/8/peraturan-dprd" : "", # No date on child articles
        "https://dprd.sulselprov.go.id/web/page/pengumuman" : "Pengumuman"
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }

    article_to_pdf_urls_mapping = {}  # Mapping title, date and PDF with child articles from start URL
    
    def get_articles(self, response) -> list:  
        articles  =[]
        map ={}
        if response.url == "https://dprd.sulselprov.go.id/web/page/pengumuman":
            for item in response.xpath('//div[@class="card-body"]'):
                title = item.xpath('.//h3/text()').get()
                url = item.xpath('.//a/@href').get()
                articles.append(url)
                date = item.xpath('./text()[1]').get()
                map[url] ={
                    "title": title.strip() ,
                    "date": date.strip(),
                    "pdf" : url
                }
            self.article_to_pdf_urls_mapping.update(map)
            return articles
        else:
            return response.xpath('//h3[@class="recommended-one__title"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        if "text" in content_type or "html" in content_type:
            title = response.xpath('//div[@class="page-header__inner"]/h2/text()').get()
            if title:
                return title.strip()
        mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
        if mapping_entry:
            return mapping_entry.get("title", "")
        return ""
    
    def get_body(self, response) -> str:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        if "text" in content_type or "html" in content_type:
            body = body_normalization(response.xpath('//div[@class="container"]//p/text()').getall())
            if body:
                return body
        return ""
    
    def get_images(self, response) -> list:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        if "text" in content_type or "html" in content_type:
            images = response.xpath('//div[@class="container"]//img/@src').getall()
            if images:
                return images
        return []
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        date = ""
        if "text" in content_type or "html" in content_type:
            raw_date = response.xpath('//ul[@class="thm-breadcrumb list-unstyled"]//li[3]//text()').get().strip()
            for indo, eng in self.indonesian_to_english.items():
                raw_date = raw_date.replace(indo, eng)
            return raw_date
        mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
        if mapping_entry:
            for indo, eng in self.indonesian_to_english.items():
                mapping_entry["date"] = mapping_entry["date"].replace(indo, eng)
            return mapping_entry.get("date", "")
        return ""

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None):
        if not "text" in response.headers.get("Content-Type", b"").decode("utf-8").lower():
            mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
            if mapping_entry:
                pdf_url = mapping_entry.get("pdf", "")
                if pdf_url:
                    return pdf_url
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[@rel="next"]/@href').get()