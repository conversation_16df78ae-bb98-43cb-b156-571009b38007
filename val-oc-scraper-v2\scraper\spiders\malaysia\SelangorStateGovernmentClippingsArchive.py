
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
import scrapy

class SelangorStateGovernmentClippingsArchive(OCSpider):
    name = "SelangorStateGovernmentClippingsArchive"
    
    start_urls_names = {
        "https://www.selangor.gov.my/index.php/pages/view/6655":"2024 News-Paper",
        "https://www.selangor.gov.my/index.php/pages/view/5656": "2023 News-paper",
        "https://www.selangor.gov.my/index.php/pages/view/4839":"2022 News-paper",
        "https://www.selangor.gov.my/index.php/pages/view/4066":"2021 News-paper",
        "https://www.selangor.gov.my/index.php/pages/view/3732":"2020 News-Paper",
        "https://www.selangor.gov.my/index.php/pages/view/3210":"2019 News-Paper",
        "https://www.selangor.gov.my/index.php/pages/view/2448":"2018 News-Paper",
        "https://www.selangor.gov.my/index.php/pages/view/1551":"2017 News-Paper",
        "https://www.selangor.gov.my/index.php/pages/view/1180":"2016 News-Paper",
        "https://www.selangor.gov.my/index.php/pages/view/1057":"2015 News-Paper",
        "https://www.selangor.gov.my/index.php/pages/view/349":"2014 News-paper",
        "https://www.selangor.gov.my/index.php/pages/view/181":"2013 News-Paper"

        }#Please Check All Url One Time

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    
    def parse_intermediate(self, response):
        articles =response.xpath("//div[contains(@class, 'product')]/@title").getall()
        total_articles = len(articles)
        start_url = list(self.start_urls_names.keys())
        for i in start_url:
            for start_idx in range(0,total_articles, 100):  # Indexing for virtual pagination to extract more than 100 articles from single page
                yield scrapy.Request(
                    url=i,  
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': i
                    },  
                    dont_filter=True
                )
     
    def get_articles(self, response) -> list:  
       articles= []
       for article in response.xpath('//div[@class="product"]'):
           link = article.xpath("./@data-url").get()
           title = article.xpath("./@title").get()
           if link:
              
               articles.append(link)
               self.article_data_map[link]={
                    "title":title,"link":link
                }
       start_idx = response.meta.get('start_idx', 0) 
       end_idx = start_idx + 100       
       return articles[start_idx:end_idx]
       
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return response.xpath("//a//@href").getall()
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 