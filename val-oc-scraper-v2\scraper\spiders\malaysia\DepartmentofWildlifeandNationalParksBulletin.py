#RegistrationofCompanyAnnualReports
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class DepartmentofWildlifeAndNationalParksBulletin(OCSpider):
    name = "DepartmentofWildlifeAndNationalParksBulletin"

    start_urls_names = {
        "https://www.wildlife.gov.my/index.php/penerbitan/109-buletin": "News"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//*[@id="tm-content"]/article/table/tbody/tr/td//a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        #link= self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
        print("ResoponseURL is ==============================",response.url)
        link = response.url
        filename = link.split('/')[-1].replace('.pdf', '')
        print("FileName is ===================",filename)
        #name = filename
        #print("PDF NAME IS +++++++++++++++++++++++++++++++",name)
        return filename

    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str: 
        # link= self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
         link = response.url
         year_match = re.search(r"/(\d{4})/", link)
         if year_match:
            year = year_match.group(1)
         else:
            year = str(datetime.now().year) 
        
        #year= "2021"
         parsed_date = dateparser.parse(year, languages=['ms','en'])
         return parsed_date.strftime("%Y")

        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 