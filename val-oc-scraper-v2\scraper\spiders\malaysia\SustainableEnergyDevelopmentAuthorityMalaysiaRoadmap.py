from scraper.OCSpider import OCSpider
import dateparser
from datetime import datetime

class SustainableEnergyDevelopmentAuthorityMalaysiaRoadmap(OCSpider):
    name = "SustainableEnergyDevelopmentAuthorityMalaysiaRoadmap"

    start_urls_names = {
        "https://www.seda.gov.my/reportal/myrer/": "Report DownLoad"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:
       articles= []
       link = response.xpath('//*[@id="1631090396318-acc4fc28-6dce"]//div[6]/a//@href').get()
       name= "Malyasia Renewable Energy Roadmap"
       now = datetime.now()
       current_year = now.year
       new_year=str(current_year)
       if link:
               articles.append(link)
               self.article_data_map[link]={
                    "title":name,"link":link,"date":new_year
                }
               
       return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        date =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 