from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MinistryofNaturalResourcesandEnvironmentSustainabilityPressReleasesArchive(OCSpider):
    name = "MinistryofNaturalResourcesandEnvironmentSustainabilityPressReleasesArchive"
    
    start_urls_names = {
        "https://www.nres.gov.my/ms-my/pustakamedia/Pages/ArkibBerita.aspx": "News"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
   
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='RMAnnouncementsTitle']//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("(//th[@class='ms-formbody'])[1]//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='x11i5rnm xat24cr x1mh8g0r x1vvkbs xtlvy1s x126k92a']//text()").getall())
    
    def get_images(self, response) -> list:
        images = response.xpath("//p//img//@src").getall()
        return images or []
        
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response):
        return response.xpath("(//th[@class='ms-formbody'])[2]//text()").get()
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) -> Optional[str]:
        return None