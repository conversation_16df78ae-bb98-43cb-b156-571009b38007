from scraper.utils.helper import body_normalization
from scraper.OCSpider import OCSpider

class LouisianaAttorneyGeneral(OCSpider):
    name = 'LouisianaAttorneyGeneral'       
    
    start_urls_names = {
            'https://www.ag.state.la.us/News/Page/1':'News'
            }

    country = "US"

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 6,
    }

    article_data_map ={}  # Mapping date with articles from start URL

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Central"
    
    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        articles = response.xpath("//div[@class='card']//a//@href").getall()
        dates = response.xpath("//span[@class='article-listing-date']//text()").getall()
        for i in range(len(articles)):
            full_url = articles[i]
            date = dates[i]
            if full_url:
                    self.article_data_map[full_url] = {
                        "date": date if date else "",
                        "full_url": [full_url], 
                    }
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='page-title']//p//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='article-section']//p//text()").getall())

    def get_images(self, response) -> list:
        images = response.xpath("//ul[@class='list-group']//li//a[contains(@href,'.jpg')]//@href").getall()
        if images:
            return images
        return []
    
    def date_format(self) -> str:
        return '%m/%d/%Y'

    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date"," ").strip()
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        pdf = response.xpath("//ul[@class='list-group']//li//a[contains(@href,'.pdf')]//@href").getall()
        if pdf:
            return pdf
        else:
            return None
                              
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//li[@class='page-item']//a//@href").get()
        current_page = int(response.url.split('/')[-1]) if response.url.split('/')[-1].isdigit() else 1
        if current_page <=9:
            next_page = f"/News/Page/{current_page + 1}"
            return response.urljoin(next_page)
        else:
            return None