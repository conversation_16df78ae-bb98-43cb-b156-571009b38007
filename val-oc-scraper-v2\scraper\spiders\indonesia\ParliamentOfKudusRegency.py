from datetime import datetime
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfKudusRegency(OCSpider):
    name = "ParliamentOfKudusRegency"
    
    start_urls_names = {
        "https://dprd.kuduskab.go.id/berita/": "",
        #"https://jdih-dprd.kuduskab.go.id/":"" this URL doesn't contain any articles
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response):
        return response.xpath('//h2//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='fusion-text fusion-text-1']//p//text()").getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_images(self, response) -> list:
        return []
    
    def get_date(self, response) -> str:
        MONTH_MAP = {
            'Januari': 'January',
            'Februari': 'February',
            'Maret': 'March',
            'April': 'April',
            'Mei': 'May',
            'Juni': 'June',
            'Juli': 'July',
            'Agustus': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Desember': 'December'
        }
        raw_date = response.xpath("//div[@class='fusion-meta-info-wrapper']/span[contains(@class, 'updated')]/following-sibling::span[1]/text()").get()
        if not raw_date:
            return None 
        raw_date = raw_date.strip()
        raw_date = raw_date.replace("WIB", "").strip()
        for indo_month, eng_month in MONTH_MAP.items():
            if indo_month in raw_date:
                raw_date = raw_date.replace(indo_month, eng_month)
                break
        try:
            date_obj = datetime.strptime(raw_date, "%d %B %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError as e:
            raise e
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return None