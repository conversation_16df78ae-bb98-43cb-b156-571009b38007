from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MaineDepartmentOfEconomicAndCommunityDevelopment(OCSpider):
    name = 'MaineDepartmentOfEconomicAndCommunityDevelopment'

    country = "US"

    start_urls_names = {
        "https://www.maine.gov/portal/government/state-news.html": "News",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//dt//a//@href").getall() 
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h2//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='clearfix']//p//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='clearfix']//p[2]//text()").get()
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None):
        return response.xpath("//p//a[contains(@href,'.php')]//@href").get()

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        # No next page to scrape
        return None