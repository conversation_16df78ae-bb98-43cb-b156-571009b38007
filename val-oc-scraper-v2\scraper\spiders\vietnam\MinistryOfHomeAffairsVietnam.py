from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re
from urllib.parse import urljoin

class MinistryOfHomeAffairsVietnam(OCSpider):
    name = "MinistryOfHomeAffairsVietnam"

    start_urls_names = {    
        "https://moha.gov.vn/tintuc/Pages/listbnv.aspx" : "News events"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
	}

    HEADLESS_BROWSER_WAIT_TIME = 10000   

    def get_articles(self, response) -> list:
        base_url = "https://moha.gov.vn/"  
        relative_urls = response.xpath('//div[@class="item-new display-flex-center"]//a[@class="item-image"]/@href').getall()
        return [urljoin(base_url,url) for url in relative_urls]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h3[@class="title-post font-size-14"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='content-post--text']//p//text()").getall())

    def get_images(self, response) -> list:
        return [response.urljoin(url) for url in response.xpath('//figure[@class="image"]//img/@src').getall()]

    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> str:
        date_text = response.xpath('normalize-space(//div[contains(@class,"date-post")]//span[contains(@class,"color-date")])').get()     
        date_text = date_text.strip()        
        m = re.search(r"(\d{1,2}/\d{1,2}/\d{4})", date_text)
        date_part = m.group(1) if m else date_text.split(" | ")[0].strip()     
        for fmt in ["%d/%m/%Y", "%m/%d/%Y"]:
            try:
                parsed = datetime.strptime(date_part, fmt)
                return parsed.strftime(self.date_format())
            except ValueError:
                continue
        return date_part if re.fullmatch(r"\d{1,2}/\d{1,2}/\d{4}", date_part) else ""
    
    def get_authors(self, response):
        return  ""
    
    def get_document_urls(self, response, entry=None)->list:
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        next_url = response.xpath('//a[@class="item-pagination font-size-14 nexts"]/@href').get()
        return response.urljoin(next_url) if next_url else None
