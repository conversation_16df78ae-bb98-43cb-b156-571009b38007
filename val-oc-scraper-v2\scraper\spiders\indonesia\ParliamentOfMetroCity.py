from datetime import datetime
import re
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfMetroCity(OCSpider):
    name = "ParliamentOfMetroCity"
    
    start_urls_names = {
        "https://dprd.metrokota.go.id/q-dprd_kota_metro-produk_hukum_dihasilkan": "Articles",
        "https://dprd.metrokota.go.id/q-media-berita": "Articles",
    }

    charset = "utf-8"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response):
        return response.xpath("//div[@class='event-cont']//a//@href").getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h2//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="cont"]//p//span//text() | //div[@class="cont"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='thum']//img//@src").getall()
    
    def get_date(self, response) -> str:
        MONTH_MAP = {
            'Januari': 'January',
            'Februari': 'February',
            'Maret': 'March',
            'April': 'April',
            'Mei': 'May',
            'Juni': 'June',
            'Juli': 'July',
            'Agustus': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Desember': 'December'
        }
        raw_date = response.xpath('//div[@class="cont"]//ul//li/a/text()').get()
        if not raw_date:
            return None
        raw_date = raw_date.strip().replace("WIB", "").strip()
        match = re.search(r"\d{1,2} \w+ \d{4}", raw_date)
        if not match:
            print(f"Could not extract date from: {raw_date}")
            return None
        clean_date = match.group()  
        for indo_month, eng_month in MONTH_MAP.items():
            if indo_month in clean_date:
                clean_date = clean_date.replace(indo_month, eng_month)
                break
        try:
            date_obj = datetime.strptime(clean_date, "%d %B %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError as e:
            return 
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//ul[@class='pagination justify-content-center']//a[@aria-label='Next']/@href").get()  