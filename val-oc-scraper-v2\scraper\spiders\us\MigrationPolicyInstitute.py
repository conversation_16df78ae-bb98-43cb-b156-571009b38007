from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class MigrationPolicyInstitute(OCSpider):
    name = "MigrationPolicyInstitute"
    
    country = "US"

    start_urls_names = {
        "https://www.migrationpolicy.org/research" : "Research"
    }

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		}
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 100
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="field-item even"]//h4//a[contains(@href,"research")]//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="field-item even"]/h1/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="field-item even"]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('(//span[@datatype="xsd:dateTime"])[2]/text()').get().strip()
        
    def get_authors(self, response):
        return response.xpath('//span[@class="textformatter-list"]//text()').getall()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath('//li[@class="pager-next"]/a[@title="Go to next page"]/@href').get()