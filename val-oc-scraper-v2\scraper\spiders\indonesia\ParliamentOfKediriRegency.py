from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class ParliamentOfKediriRegency(OCSpider):
    name = "ParliamentOfKediriRegency"

    start_urls_names = {
       "https://dprdkedirikab.go.id/category/news/" : "News",
       "https://dprdkedirikab.go.id/produk-dprd/" : "Produk DPRD",  # No artilce to scrape
       "https://dprdkedirikab.go.id/category/publikasi/raperda/" : "Raperda" # No artilce to scrape
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Sepetember": "September",  # <- typo handled here
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }

    day_map = {
        'Senin': 'Monday', 'Selasa': 'Tuesday', 'Rabu': 'Wednesday',
        'Kamis': 'Thursday', 'Jumat': 'Friday', 'Sabtu': 'Saturday', 'Minggu': 'Sunday'
    }
    
    def get_articles(self, response) -> list:  
        return response.xpath('//h6[@class="entry-title"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//span[@class="text-title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="main-content"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        text = response.xpath('//div[@class="entry-content"]/p[1]//text()').getall()
        combined_text = ' '.join(text)
        match = re.search(rf'\b\d{{1,2}}\s+({self.months_pattern})\s+\d{{4}}\b', combined_text)
        if match:
            raw_date = match.group(0)
            date_part = raw_date.split(', ')[-1]
            for indo, eng in self.indonesian_to_english.items():
                date_part = date_part.replace(indo, eng)
            return date_part.strip()
        return None 

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
       return response.xpath('//a[@class="next page-numbers"]/@href').get() 
    months_pattern = r"Januari|Februari|Maret|April|Mei|Juni|Juli|Agustus|September|Oktober|November|Desember"