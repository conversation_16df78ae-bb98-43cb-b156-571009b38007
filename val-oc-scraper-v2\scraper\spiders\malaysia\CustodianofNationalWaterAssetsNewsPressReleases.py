from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization

class CustodianofNationalWaterAssetsNewsPressReleases(OCSpider):
    name = "CustodianofNationalWaterAssetsNewsPressReleases"
    
    start_urls_names = {
        "https://www.paab.my/whats-on/": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
   
    def get_articles(self, response) -> list:
        return response.xpath("//p[@class='pp-content-grid-post-title']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='fl-col-group fl-node-sr5hpjexgtka fl-col-group-equal-height fl-col-group-align-top']//span[@class='fl-heading-text']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("(//div[@class='fl-module fl-module-fl-post-content fl-node-yrq9o7mi5fxp']//div[@class='fl-module-content fl-node-content']/p)[1]").getall())

    def get_images(self, response) -> list:
        return response.xpath("//figure[@class='wp-block-image size-large']//img//@src").getall()
        
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response):
        return response.xpath("//span[@class='fl-post-info-date']//text()").get()
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response):
        next=response.xpath("//a[@class='next page-numbers']//@href").get() 
        if next:
            return next
        return None