from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import urljoin, unquote

class KedahSyariahJuciaryDepartmentNewsClippings(OCSpider):
    name = "KedahSyariahJuciaryDepartmentNewsClippings"

    start_urls_names = {
        'https://syariah.kedah.gov.my/ms/Page?type=S2IrOmUxxXs=&pid=1elpIUgkkCU=#ye=MjAyMw==' : 'Press Clippings'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://syariah.kedah.gov.my/ms/Page?type=S2IrOmUxxXs=&pid=1elpIUgkkCU=#ye=MjAyMw==' 
    }   

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(response.url, timeout=20000), 
            callback=self.parse, 
            dont_filter=True, 
            meta ={
                "start_url" : response.url
            }
        )

    malay_days = {
        "Isnin": "Monday",
        "Selasa": "Tuesday",
        "Rabu": "Wednesday",
        "Khamis": "Thursday",
        "Jumaat": "Friday",
        "Sabtu": "Saturday",
        "Ahad": "Sunday"
    }

    malay_months = {
        "Januari": "January",
        "Februari": "February",
        "Mac": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }

    def get_articles(self, response):
        base_url = "https://syariah.kedah.gov.my/"
        articles = []
        mapping = {}
        rows = response.xpath('//tr[@role="row"]') 
        for row in rows:
            url = urljoin(base_url,row.xpath('.//div[@class="ListTitle"]/a/@href').get())
            title = row.xpath('.//div[@class="ListTitle"]/a/text()').get()
            date = row.xpath('./td[4]/text()').get()   
            print(date,title)
            if url and title and date:
                articles.append(url)
                mapping[url] = {"title": title, "date": date ,"img" :url}
        self.article_to_date_map.update(mapping)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('title')

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('date')

    def date_format(self) -> str:
        return "%d.%m.%Y"

    def get_images(self, response) -> list[str]:
        normalized_url = unquote(response.url)
        return [(self.article_to_date_map.get(normalized_url).get('img'))]

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None