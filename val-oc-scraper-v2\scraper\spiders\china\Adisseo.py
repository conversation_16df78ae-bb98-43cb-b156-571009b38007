from typing import List, Optional
from scraper.OCSpider import <PERSON><PERSON><PERSON>er
from scraper.utils.helper import body_normalization
import scrapy
import re

class Adisseo(OCSpider):
    name = "Adisseo"

    start_urls_names = {
        "https://www.adisseo.com.cn/investor-announcements": "老板电器",
        "https://www.adisseo.com.cn/news?page=1": "老板电器",
    }

    article_data_map ={}  # Mapping title, date and PDF with child articles from start URL

    charset = "iso-8859-1"

    def parse_intermediate(self, response):
        news_articles = response.xpath("//div[@class='news-content']//h4//a//@href").getall()
        investment_articles = response.xpath("//div[@class='col-sm-12']//a[@class='presse']//@href").getall()
        articles = news_articles + investment_articles
        total_articles = len(articles)
        start_url = list(self.start_urls_names.keys())
        for i in start_url:
            for start_idx in range(0,total_articles, 100):  # Indexing for virtual pagination to extract more than 100 articles from single page
                yield scrapy.Request(
                    url=i,  
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': i
                    },  
                    dont_filter=True
                )
                
    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            try:
                articles = []
                article_blocks = (
                response.xpath("//div[@class='news-content']") or
                response.xpath("//div[@class='col-sm-12']")
            )
                for article in article_blocks:
                    url = article.xpath(".//a[@class='presse']//@href | .//h4//a//@href ").get()
                    title = article.xpath(".//h3//text() | .//h4//a//text()").get()
                    date = article.xpath(".//span[@class='date-actualite']//text() | //div[@class='my-sous_titre_presse sous_titre_presse']//p//text()").get()
                    if url and title and date:
                        full_url = url
                        title = title.strip()
                        if '.pdf' in url.lower():
                            pdf = full_url
                        else:
                            pdf = "None"
                        clean_date = date.strip()
                        self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                        articles.append(full_url) 
                start_idx = response.meta.get('start_idx', 0) 
                end_idx = start_idx + 100       
                return articles[start_idx:end_idx]
            except Exception as e:
                return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath("//div[@class='contenu editer-content']//p//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", None)
        
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        if response.url.lower().endswith(".pdf") or response.url.lower().endswith(".doc"):
            return [response.url]
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")
        if pdf_url != 'None':
            return [pdf_url]

    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath("//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if response.status == 404:
            return None
        current_url = response.url
        match = re.search(r"page=(\d+)", current_url)
        if match:
            current_page_num = int(match.group(1))
            next_page_num = current_page_num + 1
            next_page_url = current_url.replace(
                f"page={current_page_num}",
                f"page={next_page_num}"
            )
            return next_page_url
        else:
            return None