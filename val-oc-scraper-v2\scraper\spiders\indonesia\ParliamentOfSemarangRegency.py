from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ParliamentOfSemarangRegency(OCSpider):
    name = "ParliamentOfSemarangRegency"

    start_urls_names = {
        "https://dprd.semarangkab.go.id/berita": "News",
        "https://jdih.dprd.semarangkab.go.id/": "JDIH" 
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self):
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Jakarta"

    def get_articles(self, response) -> list:
        return response.xpath("//h2[@class='mb-0 roboto-700 text-blue f-20']//a//@href | //div[@class='grid gap-2']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text() | //div[@class='text-2xl font-medium']//text()").get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='roboto-400 f-20 mt-0']//p//text() | //div[contains(@class, 'sm:grid-cols-3')]/div[1]/text() | //div[contains(@class, 'sm:grid-cols-3')]/div[2]/text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='article-detail mb-0']//img//@src | //div[@class='flex items-center justify-between mx-auto max-w-7xl']//img//@src").getall()

    def get_document_urls(self, response, entry=None) -> list:
        return [response.urljoin(url) for url in response.xpath('//div[@id="modal-dokumen"]//iframe/@src').getall()]
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:    
        raw_date = response.xpath("//span[@class='font-italic']/text()[1] ").get()
        if not raw_date:
            raw_date = response.xpath("//div[contains(@class, 'sm:grid-cols-3')][div[1][contains(text(), 'Tanggal Penetapan')]]/div[2]/text()").get()        
        if raw_date:
            raw_date = raw_date.strip().split(",", 1)[-1].strip()
            indo_to_eng = {
                "januari": "January", "februari": "February", "maret": "March",
                "april": "April", "mei": "May", "juni": "June", "juli": "July",
                "agustus": "August", "september": "September", "oktober": "October",
                "november": "November", "desember": "December"
            }
            for indo, eng in indo_to_eng.items():
                if indo in raw_date.lower():
                    raw_date = raw_date.lower().replace(indo, eng)
                    break
            return raw_date 
        return datetime.now().strftime(self.date_format())
       
    def get_authors(self, response):
        return response.xpath("//span[@class='font-italic']/a/text() ").getall()

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        # No next page to scrape
        return None 