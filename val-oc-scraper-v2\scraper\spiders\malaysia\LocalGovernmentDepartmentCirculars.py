from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class LocalGovernmentDepartmentCirculars(OCSpider):
    name = "LocalGovernmentDepartmentCirculars"

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        self.start_urls_names = {
            f"https://jkt.kpkt.gov.my/en/list-of-circular/?selectedYear=2010": "News/Circular"
        }

        super().__init__(*args, **kwargs)
        self.country = "Malaysia"
        self.article_data_map = {}
        self.current_year = 2010

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
    }

    @property
    def source_type(self) -> str:
        return "official_line"

    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        articles = []
        for block in response.xpath('//div[contains(@class, "drp-import-data")]'):
            main_link_element = block.xpath('.//p/a')
            if main_link_element:
                url = main_link_element.xpath('./@href').get()
                title = main_link_element.xpath('normalize-space(./text())').get()

                if url and title and ".pdf" in url:
                    date = None
                    try:
                        year_match = url.split('/')[-1]
                        year_match = year_match.split('_')[-1]
                        year_match = year_match.split('.')[0]
                        if year_match.endswith('_'):
                            year_match = year_match[:-1]

                        if year_match.isdigit() and len(year_match) == 4:
                            date = f"01/01/{year_match}"
                        else:
                            current_year_from_response = response.url.split('selectedYear=')[-1]
                            if current_year_from_response.isdigit() and len(current_year_from_response) == 4:
                                date = f"01/01/{current_year_from_response}"

                    except IndexError:
                        current_year_from_response = response.url.split('selectedYear=')[-1]
                        if current_year_from_response.isdigit() and len(current_year_from_response) == 4:
                            date = f"01/01/{current_year_from_response}"
                        pass

                    title = body_normalization(title)
                    title = re.sub(r'(?<=[a-zA-Z])\s(?=[a-zA-Z])', '', title)
                    title = re.sub(r'\s+', ' ', title).strip()
                    if url and title and date:
                        articles.append(url)
                        self.article_data_map[url] = {
                            "title": title,
                            "date": date
                        }

            for ul_link in block.xpath('.//ul/li/a'):
                url = ul_link.xpath('./@href').get()
                title = ul_link.xpath('normalize-space(./text())').get()
                if url and title and ".pdf" in url:
                    date = None
                    try:
                        year_match = url.split('/')[-1]
                        year_match = year_match.split('_')[-1]
                        year_match = year_match.split('.')[0]
                        if year_match.endswith('_'):
                            year_match = year_match[:-1]

                        if year_match.isdigit() and len(year_match) == 4:
                            date = f"01/01/{year_match}"
                        else:
                            current_year_from_response = response.url.split('selectedYear=')[-1]
                            if current_year_from_response.isdigit() and len(current_year_from_response) == 4:
                                date = f"01/01/{current_year_from_response}"
                    except IndexError:
                        current_year_from_response = response.url.split('selectedYear=')[-1]
                        if current_year_from_response.isdigit() and len(current_year_from_response) == 4:
                            date = f"01/01/{current_year_from_response}"
                        pass

                title = body_normalization(title)
                title = re.sub(r'(?<=[a-zA-Z])\s(?=[a-zA-Z])', '', title)
                title = re.sub(r'\s+', ' ', title).strip()

                if url and title and date:
                    articles.append(url)
                    self.article_data_map[url] = {
                        "title": title,
                        "date": date
                    }
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get("entry"), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def get_document_urls(self, response, entry=None):
        return [entry] if entry and entry.endswith(".pdf") else []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        raw_date = self.article_data_map.get(response.request.meta.get("entry"), {}).get("date", "")
        parsed_date = dateparser.parse(raw_date, languages=["ms"])
        return parsed_date.strftime(self.date_format()) if parsed_date else ""

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        if self.get_page_flag():
            try:
                current_year_on_page = int(response.url.split('selectedYear=')[-1])
            except (ValueError, IndexError):
                current_year_on_page = self.current_year
            if current_year_on_page < datetime.now().year:
                self.current_year = current_year_on_page + 1
                next_year_url = f"https://jkt.kpkt.gov.my/en/list-of-circular/?selectedYear={self.current_year}"
                return next_year_url
        return None