from datetime import datetime
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy

class ParliamentofPurwakartaRegency(OCSpider):
    name = "ParliamentofPurwakartaRegency"

    start_urls_names = {
        "https://dprd.purwakartakab.go.id/berita ": "News",
        "https://dprd.purwakartakab.go.id/pengumuman": "Pengumuman",
        "https://jdih.dprd.purwakartakab.go.id/ ": "JDIH"
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        articles = response.xpath("//h3[@class='font-light darkcolor']//a//@href | //h4[@class='text-capitalize font-normal darkcolor']//a//@href | //h4//a//@href ").getall()
        total_articles = len(articles)
        start_url =  list(self.start_urls_names.keys())[0] 
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': start_url
                    },
                    dont_filter=True
            )
    

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Jakarta"

    def get_articles(self, response) -> list:
        articles = response.xpath("//h3[@class='font-light darkcolor']//a//@href | //h4[@class='text-capitalize font-normal darkcolor']//a//@href | //h4//a//@href ").getall()
        all_articles = list(set(articles))
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='card-body']//h3//text() | //h3//text()").get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='blog-entry']//p//text() | //div[@class='card-body']//p//text() | //div[@class='entry-content']//p//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='card-body']//img//@src").getall()

    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        MONTHS_ID = {
            'Januari': '01',
            'Februari': '02',
            'Maret': '03',
            'April': '04',
            'Mei': '05',
            'Juni': '06',
            'Juli': '07',
            'Agustus': '08',
            'September': '09',
            'Oktober': '10',
            'November': '11',
            'Desember': '12',
        }
        raw_date = response.xpath("//div[@class='blog-content mt-30']//ul//li//text() | //div[@class='container']//div[@class='card-body']//h3/following-sibling::text()[1] | //div[@class='entry-date']//span//text() ").get()
        if raw_date:
            try:
                if ":" not in raw_date :
                    cleaned_date = raw_date.split(",")[1].strip()
                    parts = cleaned_date.split(' ')
                    if len(parts) == 3:
                        day, month_id, year = parts
                        month_number = MONTHS_ID.get(month_id)
                        if month_number:
                            iso_date = f"{day}-{month_number}-{year}"
                            dt = datetime.strptime(iso_date, "%d-%m-%Y")
                            return dt.strftime("%d %B %Y")
                else:
                    cleaned_date = raw_date.split(',')[1].strip().rsplit(' ', 1)[0]
                    parts = cleaned_date.split(' ')
                    if len(parts) == 3:
                        day, month_id, year = parts
                        month_number = MONTHS_ID.get(month_id)
                        if month_number:
                            iso_date = f"{day.zfill(2)}-{month_number}-{year}"
                            dt = datetime.strptime(iso_date, "%d-%m-%Y")
                            return dt.strftime("%d %B %Y")                    
            except Exception:
                return ""
        return ""

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response): 
        # No next page to scrape
        return None