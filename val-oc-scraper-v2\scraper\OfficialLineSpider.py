import re
from typing import Dict, List
from abc import abstractmethod, ABCMeta
from datetime import datetime, timedelta
import json
from .wrapper import *
import logging
import requests
import pytz
import scrapy
from bs4 import BeautifulSoup
from scrapy.http import Request
# from common.gcp.datasource.Utils import utils
from scraper.middlewares import GeoProxyMiddleware
from scraper.utils.helper import get_source_tier
from .utils.Website import Website
from .items import OfficialLineItem
from scrapy.utils.project import get_project_settings
from scrapy.settings import Settings
from requests.exceptions import ConnectTimeout, ReadTimeout

def configure_ol_spider():
    pass


class OfficialLineSpider(scrapy.Spider):
    __metaclass__ = ABCMeta

    custom_settings = {
        'ITEM_PIPELINES': {
            #'scraper.pipelines.checkpoint.datastore.OfficialLinePipeline': 3,
            'scraper.pipelines.storage.bq_raw.Pipeline': 5,
        }
    }

    def __init__(self, name=None, **kwargs):
        super().__init__(name, **kwargs)
        self.backfill = bool(kwargs.get("backfill", False))
        #self.error_pipeline: ErrorPipeline = ErrorPipeline()
        self.start_urls = []
        self.tier = get_source_tier(self.name)
        self.threshold = 0.3
        self.CONNECTION_TIMEOUT = 30 # seconds
        # we need to set the date as the spider's timezone
        spider_timezone = pytz.timezone(self.timezone)
        if "date" in kwargs:
            self.date = datetime.strptime(kwargs["date"], "%Y-%m-%d %H:%M:%S")

            # get utc timezone
            utc_date = pytz.utc.localize(self.date)
            self.date = utc_date.astimezone(spider_timezone)
        else:
            self.date = datetime.now(spider_timezone).strftime('%Y-%m-%d')
            if self.name == "cctv":
                self.date = datetime.strptime(
                    self.date, "%Y-%m-%d") - timedelta(days=1)
            else:
                self.date = datetime.strptime(self.date, "%Y-%m-%d")
            self.date = spider_timezone.localize(self.date)

        self.source_type = getattr(self, "source_type", "official_line") 
        self.country = getattr(self, "country", "China")
        
        self.date_flag = False
        if "bypass_existing_urls" in kwargs:
            self.bypass_existing_urls = kwargs["bypass_existing_urls"]
        else:
            self.bypass_existing_urls = ''


        print(f"Starting {self.name} for {self.date}")

        self.headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.135 Safari/537.36"
        }


        if not self.start_urls_names:
            raise AttributeError(
                "OC Spider could not start: 'start_urls_names' not found. Please see readme.md")
        for start_url in self.start_urls_names:
            try:
                if self.backfill:
                    url = self.get_start_url(start_url, self.date)
                    logging.info(f"Backfill starting for scraper {self.name} for date {self.date} - url {url}")
                    self.start_urls.append(url)
                else:
                    # check the previous 4 days for the start url
                    for i in range(5):
                        date = self.date - timedelta(days=i) # initially i = 0, so it will check the start url for the current date
                        
                        url = self.get_start_url(start_url, date)

                        try:
                            # check if there is a GeoProxyMiddleware in the middlewares
                            if hasattr(self, "proxy_country"):
                                # if there is a GeoProxyMiddleware, then use it to make the request
                                # this will help us to not get blocked by the website
                                logging.info("Testing if the site is accessible using GeoProxyMiddleware")
                                # doing this will raise an exception if there is a timeout error
                                GeoProxyMiddleware().process_request(Request(url), self, timeout=self.CONNECTION_TIMEOUT)
                            else:
                                # doing just this will raise an exception if there is a timeout
                                requests.get(url, headers=self.headers, timeout=(self.CONNECTION_TIMEOUT, None))
                            
                            logging.info("The site is accessible and it did not raise timeout errors")
                            self.start_urls.append(url)
                        except ConnectTimeout as e:
                            logging.error(f"Connection time out while testing start_url for date {date.strftime('%Y-%m-%d')} and start_url {url}")
                            logging.exception(e)
                        except ReadTimeout as e:
                            logging.error(f"Read time out while testing start_url {date.strftime('%Y-%m-%d')}  {url}")
                            logging.exception(e)
            except Exception as e:
                logging.exception(e)
                logging.error(f"Error in getting start url for {self.name} - {self.date}")

    exclude_rules: List[str] = []
    include_rules: List[str] = []

    websites: Dict[str, Website] = {}

    regex_multiline = r"\n{1,}"
    regex_space_eachline = r"\s+\n"

    # You can override this var to the desired charset
    charset = 'utf-8'

    custom_settings = {}

    @classmethod
    def from_crawler(cls, crawler, *args, **kwargs):
        spider = super(OfficialLineSpider, cls).from_crawler(
            crawler, *args, **kwargs)
        crawler.signals.connect(spider.spider_closed,
                                signal=scrapy.signals.spider_closed)
        return spider

    def spider_closed(self, spider):
        spider.logger.info('Spider closed: %s', spider.name)
        #spider.driver.quit()

    @property
    @abstractmethod
    def timezone(self) -> str:
        raise NotImplementedError

    @property
    @abstractmethod
    def name(self) -> str:
        raise NotImplementedError

    '''
    @property
    def custom_settings(self) -> Dict:
        return {
            'ITEM_PIPELINES': {
                #'scraper.pipelines.checkpoint.datastore.OfficialLinePipeline': 3,
                'scraper.pipelines.storage.bq_raw.Pipeline': 5,
            }
        }
    '''
    @abstractmethod
    def get_articles(self, response) -> list:
        pass

    @abstractmethod
    def get_href(self, entry) -> str:
        pass

    @abstractmethod
    def get_title(self, response) -> str:
        pass

    @abstractmethod
    def get_body(self, response) -> str:
        pass

    @abstractmethod
    def date_format(self) -> str:
        pass

    @abstractmethod
    def get_date(self, response) -> int:
        pass

    @abstractmethod
    def get_authors(self, response) -> str:
        pass

    @abstractmethod
    def get_images(self, response) -> List[str]:
        pass

    @abstractmethod
    def get_next_page(self, response) -> List[str]:
        pass

    
    @abstractmethod
    def get_document_urls(self, response, entry=None) -> list:
        """
        This function should return a list of pdf urls
        [
            pdf_url_1,
            pdf_url_2,
            pdf_url_3
        ]
        """
        pass

    @abstractmethod
    def get_subhead(self, response) -> str:
        pass

    def start_requests(self):
        self.crawler.stats.set_value("articles_crawled", 0)
        self.crawler.stats.set_value("articles_successfully_scraped", 0)
        self.crawler.stats.set_value("articles_exist_in_db", 0)
        self.crawler.stats.set_value("articles_failed_to_scrape", 0)
        self.crawler.stats.set_value("articles_filtered_due_to_include_exclude_rule", 0)
        self.crawler.stats.set_value("articles_identified_with_incorrect_date", 0)
        self.crawler.stats.set_value("tier", self.tier)

        cls = self.__class__
        if not self.start_urls and hasattr(self, 'start_url'):
            raise AttributeError(
                "Crawling could not start: 'start_urls' not found "
                "or empty (but found 'start_url' attribute instead, "
                "did you miss an 's'?)")
        #Get All urls already pushed to BQ for the date
        self.get_articles_for_date()

        for url in self.start_urls:
            if hasattr(self, "parse_intermediate"):
                callback = self.parse_intermediate
            else:
                callback = self.parse
            
            request = Request(url, dont_filter=True, headers=self.headers, callback=callback)
            request.meta['start_url'] = url
            yield request

    def get_articles_for_date(self):
        scraped_url_list = []
        for scraped_url in scraped_url_list:
            self.websites[scraped_url] = "1"


    def is_already_crawled(self, start_url, _id):
        check_crawled = self.websites.get(_id)
        if check_crawled is None and _id.startswith('https://'):
            check_crawled = self.websites.get(_id.replace('https://', 'http://'))
        if check_crawled is None and _id.startswith('http://'):
            check_crawled = self.websites.get(_id.replace('http://', 'https://'))
        return check_crawled

    first_articles_id: Dict[str, str] = {}

    should_go_to_next_page: bool = True

    def parse(self, response):
        
        start_url = response.request.meta['start_url']
        
        current_article_index = response.request.meta.get('current_article_index', 0)

        try:
            articles = self.get_articles(response)
            # Index is being initialised to 0 because sometimes middle pages of a newspaper have
            # no article links, they are buggy essentially which causes the code to error out at
            # line yield from self.go_to_next_page(response, start_url, current_article_index+index+1,)
            index = 0

            for index, entry in enumerate(articles):

                href = self.get_href(entry)

                full_url = response.urljoin(href)

                if full_url.endswith('.pdf') and not hasattr(self, "get_meta"):
                    continue

                # If full url is already in start_url, then skip 
                # Reason: because some websites provide start_url as list of articles to scrape but we do not need to scrape them
                if full_url in self.start_urls: continue
                
                # Start incrementing if the article url is not in the start_urls
                self.crawler.stats.inc_value("articles_crawled")
                
                if len(self.include_rules) == 0:
                    should_scrap: bool = True
                    for exclude_rule in self.exclude_rules:
                        if re.compile(exclude_rule).findall(full_url):
                            should_scrap = False
                            break
                else:
                    should_scrap: bool = False
                    for include_rule in self.include_rules:
                        if re.compile(include_rule).findall(full_url):
                            should_scrap = True
                            break

                if should_scrap:
                    if self.first_articles_id.get(start_url) is None:
                        self.first_articles_id[start_url] = full_url

                    if self.is_already_crawled(start_url, full_url):
                        self.crawler.stats.inc_value("articles_exist_in_db")
                        continue

                    if self.is_url_already_scraped(full_url) is None:
                        meta = {'retry_times': 0, 'index' : current_article_index+index+1, '_id': full_url, 'start_url': start_url}

                        if getattr(self, "date_in_meta", None):
                            meta["date"] = self.get_date(entry)

                        if response.request.meta.get("reset_session", False):
                            request = Request(
                                url=full_url,
                                method='GET',
                                callback=self.parse_article,
                                meta=meta
                            )
                        else:
                            request = Request(
                                url=full_url,
                                method='GET',
                                encoding=response.request.encoding,
                                cookies=response.request.cookies,
                                headers=response.request.headers,
                                callback=self.parse_article,
                                meta=meta
                            )
                        yield request
                    else:
                        self.crawler.stats.inc_value("articles_exist_in_db")
                        yield None
                else:
                    self.crawler.stats.inc_value("articles_filtered_due_to_include_exclude_rule")
                    yield None


            if self.should_go_to_next_page and self.name != "xinhuanet_1" and len(articles) > 0:
                yield from self.go_to_next_page(response, start_url, current_article_index+index+1,)

        except Exception as error:
            
            logging.error("An Exception occurred in parse method, pleas take a look!!")
            logging.exception(error)

            article = OfficialLineItem()

            article['start_url'] = start_url

            article['source'] = self.source

            article['source_name'] = self.name

            article['source_type'] = self.source_type

            article['id'] = response.request.url

            article['original_link'] = response.request.url

            article['url'] = response.request.url

            logging.error(traceback.format_exc())

            article['error'] = traceback.format_exc().strip()

            yield article

    def go_to_next_page(self, response, start_url, current_article_index):
        next_page = self.get_next_page(response)
        if next_page is not None:
            print(f"Going to next page {next_page}")
            next_page = response.urljoin(next_page)
            request = response.request.replace(url=next_page, callback=self.parse)
            request.meta['start_url'] = start_url
            request.meta['current_article_index'] = current_article_index
            yield request
        else:
            yield None

    def parse_article(self, response) -> OfficialLineItem:

        _id = response.meta["_id"]
        start_url = response.meta["start_url"]

        article_index = response.meta["index"]

        article = build_official_line_article(start_url, _id, self, response, article_index)

        scrap_article = self.scrap_article(article, response)

        if scrap_article.get("success") is True:
            self.crawler.stats.inc_value("articles_successfully_scraped")
            return scrap_article.get('value')
        else:
            article['error'] = str(scrap_article.get('value'))
            logging.error(f"Error Scraping Article: {article['error']}")
            self.crawler.stats.inc_value("articles_failed_to_scrape")
            return

    @error_safe
    def scrap_article(self, article: OfficialLineItem, response) -> OfficialLineItem:
        article['title'] = self.get_title(response).strip()

        article['subhead'] = self.get_subhead(response)

        body = BeautifulSoup(self.get_body(response), features="lxml").get_text(separator="\n")

        # Clean body
        article['body'] = body
        article['start_url'] = response.request.meta['start_url']

        subhead = self.get_subhead(response)

        article['meta'] = json.dumps({"subhead" : subhead})

        article['body_html'] = response.text

        article['source'] = self.source

        article['news_line'] = self.source_type
        article['language'] = getattr(self, "language", "Chinese")

        if not getattr(self, "date_in_meta", None):
            date = self.get_date(response)
        else:
            date = response.request.meta["date"]
            

        date_format = self.date_format()
        
        # If the date of the article is > the current date, then we will be setting it to
        # the current date
        tz = pytz.timezone(self.timezone)
        
        date_obj = datetime.strptime(date, date_format)
        date_of_article = tz.localize(datetime(date_obj.year, date_obj.month, date_obj.day, date_obj.hour, date_obj.minute, date_obj.second), is_dst=None)
        
        if date_of_article.date() > self.date.date() and not self.backfill:
            self.crawler.stats.inc_value("articles_identified_with_incorrect_date")
            tz = pytz.timezone(self.timezone)
            article['date'] = self.date.timestamp()
            logging.debug(f"The parsed date ({date}) is greater than the current date - ({self.date})")
        # the date is correct, we can continue to parsing the date
        # as normal
        else:
            # sometimes, the published date of an article has a timestamp, and sometimes it doesn't
            # date_flag is set to True, if the published date of the article does not have a timestamp eg: 2024-07-12
            if self.date_flag == True:
                date_obj = datetime.strptime(date, date_format)
                article['date'] = date_of_article.timestamp()
            else:
                # Converting datetime to timestamp
                article['date'] = self.date.timestamp()

        article['country'] = self.country

        author_name = self.get_authors(response)

        try:
            if not isinstance(author_name, list):
                author_name = author_name.decode()
        except:
            pass

        try:
            if not isinstance(author_name, list):
                article['authors'] = [{"name" : author_name}]
            else:
                article['authors'] = [{"name" : author} for author in author_name]
        except:
            article['authors'] = []

        article["images"] = [{"url": img_obj, "caption" : ""} for img_obj in self.get_images(response)]

        if hasattr(self, "get_meta"):
            article["meta"] = self.get_meta(response)

        return article

    def is_url_already_scraped(self, url):
        # index = Config.config.elasticsearch.ministries_index
        # return self.es_utils.find_by_id(index, url)
        return None


    def close(self):

        # If any of the stats for some reason are missing, log and set the defaults to 0
        # since the stats are being set in one place, if one does not exist, the others will also not exist
        # therefore we can check if one exists and set all to 0
        if self.crawler.stats.get_value("articles_crawled") == None:
            self.crawler.stats.set_value("articles_crawled", 0)
            self.crawler.stats.set_value("articles_identified_with_incorrect_date", 0)
            self.crawler.stats.set_value("articles_successfully_scraped", 0)
            self.crawler.stats.set_value("articles_exist_in_db", 0)
            self.crawler.stats.set_value("articles_failed_to_scrape", 0)
            self.crawler.stats.set_value("articles_filtered_due_to_include_exclude_rule", 0)
            self.crawler.stats.set_value("tier", self.tier)
            raise Exception(f"Stats not found for {self.name} - {self.date}")
        

        articles_to_scrape = (self.crawler.stats.get_value("articles_crawled") - self.crawler.stats.get_value("articles_exist_in_db") - \
            self.crawler.stats.get_value("articles_filtered_due_to_include_exclude_rule"))
        
        # If the number of articles crawled is 0, then raise an exception
        if self.crawler.stats.get_value("articles_crawled") == 0:
            logging.error(f"Zero articles were crawled for {self.name} - {self.run_date}")
            raise Exception(f"Zero articles were crawled for {self.name} - {self.run_date}")
        elif articles_to_scrape > 0 and \
            (self.crawler.stats.get_value("articles_failed_to_scrape")/ articles_to_scrape) > self.threshold:
            logging.error(f"Articles failed to scrape for {self.name} - {self.run_date}")
            raise Exception(f"Articles failed to scrape for {self.name} - {self.run_date}")
        else:
            logging.info(f"Articles scraped successfully for {self.name} - {self.run_date} with {self.crawler.stats.get_value('articles_crawled')} articles crawled and {self.crawler.stats.get_value('articles_successfully_scraped')} articles successfully scraped")