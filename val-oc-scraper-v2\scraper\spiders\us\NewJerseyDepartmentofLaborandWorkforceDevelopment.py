from scraper.OCSpider import OCSpider
import scrapy
import re
from scraper.utils.helper import body_normalization
from datetime import datetime

class NewJerseyDepartmentOfLaborAndWorkforceDevelopment(OCSpider):
    name = 'NewJerseyDepartmentOfLaborAndWorkforceDevelopment'
    
    country = "US"

    start_urls_names = {
        'https://www.nj.gov/labor/lwdhome/press/': "Press"
    }

    def parse_intermediate(self, response):
        all_articles = list(set(response.xpath("//div[@class='card-body']/p/a/@href").getall()))
        total_articles = len(all_articles)
        articles=[]
        for article in all_articles:
            if ".pdf" not in article:
                articles.append(article)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        if total_articles > 0:
            for start_idx in range(0, total_articles, articles_per_page):
                yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'articles': articles, 
                        'start_url': start_url,
                        'total_articles': total_articles
                    },
                    dont_filter=True
                )
    
    charset = "utf-8"
            
    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = min(start_idx + 100, len(all_articles))
        return all_articles[start_idx:end_idx]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h3//text() | //h2[contains(@class, "pageTitle")]/text() | //span[@class="mainText"]//p//strong//text()').get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[contains(@class, "content")]//p//text()').getall())

    def get_images(self, response, entry=None) :
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        url_match = re.search(r'/(\d{4})/(\d{4})(\d{2})(\d{2})_', response.url)
        if url_match:
            year = url_match.group(2)
            month = url_match.group(3)
            day = url_match.group(4)
            try:
                date_obj = datetime.strptime(f"{year}-{month}-{day}", "%Y-%m-%d")
                return date_obj.strftime(self.date_format())
            except ValueError:
                return ""
        else:
            return response.xpath("//section[@class='mb-5']//p//text()").get()
        
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        # No next page to scrape
        return None