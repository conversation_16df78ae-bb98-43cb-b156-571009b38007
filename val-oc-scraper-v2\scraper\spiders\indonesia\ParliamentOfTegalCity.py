from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ParliamentOfTegalCity(OCSpider):
    name = "ParliamentOfTegalCity"

    proxy_country = "in"

    start_urls_names = {
        "https://dprd.tegalkota.go.id/contents/berita" : "Berita",
        # "https://jdih-dprd.tegalkota.go.id/" : "Berita",
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    # custom_settings = {
    #     "DOWNLOADER_MIDDLEWARES": {
    #         "scraper.middlewares.OxylabsProxyMiddleware": 350,
    #     }
    # }
    # # attributes required for oxylabs middleware
    # proxy_country = "Indonesia"
    # require_geo_proxy = True
    
    HEADLESS_BROWSER_WAIT_TIME = 1000

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            # 'scraper.middlewares.HeadlessBrowserProxy': 350,
           'scraper.middlewares.GeoProxyMiddleware': 350,
        },
        "DOWNLOAD_DELAY": 1,
    }
    
    day_map = {
        'Senin': 'Monday', 'Selasa': 'Tuesday', 'Rabu': 'Wednesday',
        'Kamis': 'Thursday', 'Jumat': 'Friday', 'Sabtu': 'Saturday', 'Minggu': 'Sunday'
    }
    
    def get_articles(self, response) -> list:  
        articles = (response.xpath('//div[@class="uc_post_title"]//a/@href').getall()
                    or response.xpath('//div[@class="entry-title"]//a/@href').getall() or response.xpath('//h1[@class="elementor-heading-title elementor-size-default"]//a/@href').getall())
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="container"]//h2/text()').get() or response.xpath('//h1[@class="elementor-heading-title elementor-size-default"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//main[@id="main"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="slider-wrap"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> str:
        raw_date = response.xpath('//div[@class="container"]//ul[1]/li[3]/a/text()').get()
        if raw_date:
            raw_date = raw_date.strip()
            for indo_day, eng_day in self.day_map.items():
                if indo_day in raw_date:
                    raw_date = raw_date.replace(indo_day, "")
                    break 
            date_str = raw_date.replace(",", "").strip()
            try:
                parsed_date = datetime.strptime(date_str, "%d-%m-%Y")
                return parsed_date.strftime("%d/%m/%Y")
            except ValueError as e:
                self.logger.warning(f"Failed to parse date: '{date_str}'. Error: {e}")
                return None
        else:
            raw_date = response.xpath('//time//text()').get()
            if raw_date:
                raw_date = raw_date.strip()
                return raw_date
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
       return None