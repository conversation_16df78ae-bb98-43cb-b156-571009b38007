from scraper.OCSpider import OCSpider
import dateparser
from typing import Optional

class AccountantGeneralsDepartmentAnnouncements(OCSpider):
    name = "AccountantGeneralsDepartmentAnnouncements"
    
    start_urls_names = {
        "https://www.anm.gov.my/pengumuman/2025": "Announcements"
    }

    start_urls_with_no_pagination_set = {}
    
    charset = "iso-8859-1"

    country = "Malaysia"
        
    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='latestnews-items']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='page-header']//h2//text()").get(default="").strip()

    def get_body(self, response) -> str:
        return " ".join(response.xpath('//div[@class="com-content-article__body"]//p//text()').getall()).strip()

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d %b %Y"
        
    def get_date(self, response) -> Optional[str]:
        date_str = response.xpath("//dl[@class='article-info text-muted']/dd[@class='modified']/time/text()[2]").get()
        if date_str:
            parsed_date = dateparser.parse(date_str, languages=['ms', 'en'])
            return parsed_date.strftime("%d %b %Y")
        return None

    def get_document_urls(self, response, entry=None) -> list:
        relative_urls = response.xpath("//div[@class='com-content-article__body']//p//a//@href").getall()
        return [response.urljoin(url) for url in relative_urls]

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath("//li[not(contains(@class, 'disabled'))]/a[span[contains(@class, 'icon-angle-right')]]/@href").get()