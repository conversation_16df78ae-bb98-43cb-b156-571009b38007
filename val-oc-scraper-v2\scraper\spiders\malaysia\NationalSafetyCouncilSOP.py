from scraper.OCSpider import OCSpider
from typing import Optional
from scraper.utils.helper import body_normalization
from datetime import datetime

class NationalSafetyCouncilSOP(OCSpider):
    name = "NationalSafetyCouncilSOP"
    
    start_urls_names = {
        "https://www.mkn.gov.my/web/ms/sop-perintah-kawalan-pergerakan/": "Latest Articles/News"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.mkn.gov.my/web/ms/sop-perintah-kawalan-pergerakan/"
    }

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:  
        return response.xpath('//div[contains(@class, "pt-cv-content-item")]//h4[@class="pt-cv-title"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[contains(@class, "entry-title")]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[contains(@class, "penci-entry-content")]/p//text()').getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d-%m-%Y"

    def get_date(self, response) -> str:
        raw_date = response.xpath('//time[contains(@class, "entry-date")]/text()').get()
        if not raw_date:
            return ""
        malay_to_english_months = {
            'Januari': 'January',
            'Februari': 'February',
            'Mac': 'March',
            'April': 'April',
            'Mei': 'May',
            'Jun': 'June',
            'Julai': 'July',
            'Ogos': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Disember': 'December'
        }
        for malay, english in malay_to_english_months.items():
            if malay in raw_date:
                raw_date = raw_date.replace(malay, english)
                break
        try:
            date_obj = datetime.strptime(raw_date.strip(), "%d %B %Y")
            return date_obj.strftime("%d-%m-%Y")
        except ValueError:
            return ""

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None