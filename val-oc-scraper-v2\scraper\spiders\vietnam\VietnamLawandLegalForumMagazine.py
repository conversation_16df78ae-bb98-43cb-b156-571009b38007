from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class VietnamLawandLegalForumMagazine(OCSpider):
    name = "VietnamLawandLegalForumMagazine"

    start_urls_names = {
       "https://vietnamlawmagazine.vn/news/1.html?p=1":"Magazines"
        }
    
    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       links= response.xpath('//*[@id="listPageData"]/article/h5/a//@href').getall()
       return links
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="article__title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article__body"]//p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//td[@class="pic"]//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//time[@class="time"]//text()').get()
        clean_date = date.strip()
        date_obj = dateparser.parse(clean_date, languages=['en'])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return response.xpath('//a[normalize-space(.)="next"]/@href').get()