from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
class AVICIndustryAndFinanceNews(OCSpider):
    name = "AVICIndustryAndFinanceNews"

    start_urls_names = {
        "https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/": "中航产融",
    }

    charset = "utf-8"

    current_page = 2 

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//li[@class="listTableLi"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//title/text()').get().strip()
    
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="txt"]//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        date = response.xpath("//span[@class='date']//text()").get()
        return date.replace("发布时间：", "")
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//img/@src').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        if self.current_page <= 5:
            next_page = f"https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/index_{self.current_page}.shtml?PC=PC"
            self.current_page += 1
            return next_page
        elif self.current_page <= 46:
            next_page = f"https://www.avicindustry-finance.com/cms/ui/catalog/19354/pc/index_{self.current_page}.shtml?PC=PC"
            current_page += 1
            return next_page
        return None    