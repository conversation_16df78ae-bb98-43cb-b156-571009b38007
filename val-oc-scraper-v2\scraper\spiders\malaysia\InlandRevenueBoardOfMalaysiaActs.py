from typing import Optional
from scraper.OCSpider import OCSpider
from datetime import datetime

class InlandRevenueBoardOfMalaysiaActs(OCSpider):
    name = "InlandRevenueBoardOfMalaysiaActs"
    
    start_urls_names = {
        "https://www.hasil.gov.my/perundangan/akta/": "News"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.hasil.gov.my/perundangan/akta/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='text']//li"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//a//text()").get()
            date_text = article.xpath(".//em//text()").get()
            date = date_text.split("Sebagaimana pada ")[1].strip()
            if url and title and date:
                self.article_data_map[url]={"url":url, "title": title, "date": date}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response):
        month_map = {
            'JANUARI': 'January',
            'FEBRUARI': 'February',
            'MAC': 'March',  
            'APRIL': 'April',
            'MEI': 'May',
            'JUN': 'June',
            'JULAI': 'July',
            'OGOS': 'August',
            'SEPTEMBER': 'September',
            'OKTOBER': 'October',
            'NOVEMBER': 'November',
            'DISEMBER': 'December'
        }
        raw_date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "").strip()
        date_upper = raw_date.upper()
        for malay_month, eng_month in month_map.items():
            if malay_month in date_upper:
                formatted_date = raw_date.replace(
                    malay_month.title(),  
                    eng_month            
                )
                try:
                    return datetime.strptime(formatted_date, "%d %B %Y").strftime("%d %B %Y")
                except ValueError:
                    return formatted_date  
        return raw_date  
 
    def get_document_urls(self, response, entry=None) -> list:
        if '.pdf' in response.url:
            return [response.url]
        return []
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) -> Optional[str]:
        return None