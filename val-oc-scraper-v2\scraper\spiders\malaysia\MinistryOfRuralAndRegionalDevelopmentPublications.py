from scraper.OCSpider import OCSpider
import re

class MinistryOfRuralAndRegionalDevelopmentPublications(OCSpider):
    name = "MinistryOfRuralAndRegionalDevelopmentPublications"
    
    start_urls_names = {
        "https://www.rurallink.gov.my/penerbitan/" : "publications"  # Pagination is not supported
    }
    start_urls_with_no_pagination_set = {
        "https://www.rurallink.gov.my/penerbitan/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response):
        articles = []       
        for entry in response.xpath('//div[@class="eael-accordion-list"]'):
            url = entry.xpath('.//div[@class="eael-accordion-content clearfix"]//a/@href | .//div[@class="eael-accordion-content clearfix"]//iframe//@src').get()
            title = entry.xpath('.//span[@class="eael-accordion-tab-title"]//text()').get()   
            match = re.match(r".*/uploads/(\d{4})/(\d{2})/", url)
            if match:
                year, month = match.groups()
                date = f"{year}/{month}"           
            if url and title and date:
                self.article_data_map[url]={"title":title.strip(),"date":date.strip()}
                articles.append(url)        
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y/%m"
    
    def get_date(self, response): 
       return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None