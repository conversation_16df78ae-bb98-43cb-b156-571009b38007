from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import logging

class ChinaWesternDevelopmentPromotionAssociation(OCSpider):
    name = "ChinaWesternDevelopmentPromotionAssociation"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 10000
 
    start_urls_names = {
        "http://www.cwdpa.org.cn/nr.jsp?_jcp=4_1": "工作动态",
        "http://www.cwdpa.org.cn/nr.jsp?_np=0_774_2": "新闻资讯",
        "http://www.cwdpa.org.cn/nr.jsp?_np=0_774_7": "党建园地",
        "http://www.cwdpa.org.cn/nr.jsp?_jcp=4_7&pcp=7": "党建园地"
    }

    charset = "utf-8"

    article_date_map = {}

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath("//td[@class='newsTitle']//a/@href").getall()]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='title']/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='jz_fix_ue_img']//span/text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='jz_fix_ue_img']//img/@src").getall()
    
    def get_authors(self, response):
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        if not self.article_date_map:
            self.extract_articles_with_dates(response)
        date = self.article_date_map.get(response.url)
        if not date:
            return
        else:
            try:
                return datetime.strptime(date, "%Y-%m-%d").strftime("%Y-%m-%d")
            except ValueError:
                logging.error(f"Invalid date format for article {response.url}: {date}")
                return

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@id='pagenation31']//span[@class='pageNext']//a/@href").get()
        if next_page:
            next_page_url = response.urljoin(next_page)
            return next_page_url if next_page_url != response.url else None
        else:
            return None
    
    def extract_articles_with_dates(self, response):
        for article in response.xpath("//td[@class='newsCalendar']"):
            url = article.xpath("./a/@href").get()
            date = article.xpath("./a/text()").get()
            if url and date:
                self.article_date_map[response.urljoin(url)] = date.strip()