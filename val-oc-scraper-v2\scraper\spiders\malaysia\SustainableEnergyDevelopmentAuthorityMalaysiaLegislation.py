from scraper.OCSpider import <PERSON>CSpider
from typing import Optional

class SustainableEnergyDevelopmentAuthorityMalaysiaLegislation(OCSpider):
    name = "SustainableEnergyDevelopmentAuthorityMalaysiaLegislation"
    
    start_urls_names = {
        "https://www.seda.gov.my/policies/renewable-energy-act-2011/": "Renewable Energy Act 2011",
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "SOE"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_date_map = {}

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return list(self.article_date_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        entry = response.request.meta.get("entry")
        return self.article_date_map.get(entry, {}).get("title", "").strip()

    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        entry = response.request.meta.get("entry")
        return self.article_date_map.get(entry, {}).get("date", "").strip()    

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        return None

    def extract_articles_with_dates(self, response):
        rows = response.xpath('//table//tr')
        for row in rows:
            title = row.xpath('./td[1]//text()[normalize-space()]').get()
            if title:
                title = title.strip()
            else:
                continue
            link = row.xpath('./td[2]//a/@href').get()
            if not link:
                continue
            date = row.xpath('./td[3]//text()[normalize-space()]').get()
            if date:
                date = date.strip()
            else:
                continue
            full_url = response.urljoin(link)
            self.article_date_map[full_url] = {
                "title": title,
                "date": date
            }
        return self.article_date_map
