#VietNamNationalAuthorityofTourism
#ForeignAffairsCommitteeofVietnam
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class VietNamSocialSecurity(OCSpider):
    name = "VietNamSocialSecurity"

    start_urls_names = {
        "https://baohiemxahoi.gov.vn/tintuc/Pages/linh-vuc-bao-hiem-y-te.aspx?CateID=169":"Parlaments",
        "https://baohiemxahoi.gov.vn/tintuc/Pages/linh-vuc-bao-hiem-xa-hoi.aspx?CateID=168":"News",
        "https://baohiemxahoi.gov.vn/tintuc/Pages/hoat-dong-he-thong-bao-hiem-xa-hoi.aspx?CateID=52":"News",
        "https://baohiemxahoi.gov.vn/tintuc/Pages/an-sinh-xa-hoi.aspx?CateID=170":"News",
        "https://baohiemxahoi.gov.vn/tintuc/pages/kinh-te-xa-hoi.aspx?CateID=170":"news"

        }
    
   

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//div[@class="col-xs-12 none-padding none-mobile"]//div/div/div//div/a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//p[@class="tieu-de"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="contenttin"]//p//text() | //div[@id="contenttin"]//span//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//div[@id="contenttin"]//p//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//div[@class="col-xs-6 none-padding sukien"]//p//text()').get()
        #print(date,"Date is ================================")
        clean_date = date.strip()   # "26/02/2024"
        date_obj = dateparser.parse(clean_date, languages=["vi", "en"])
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return response.xpath('//a[normalize-space(.)="Sau"]/@href').get()