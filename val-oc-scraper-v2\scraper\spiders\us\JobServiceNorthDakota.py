from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import Union

class JobServiceNorthDakota(OCSpider):
    name='JobServiceNorthDakota'

    country="US"

    start_urls_names= {
        'https://www.jobsnd.com/news':'News'
    }

    charset="utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='media-heading']/h2/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='page-header']/h1/span/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('string(//div[@class="content-body"]//div[@class="clearfix text-formatted field field--name-body field--type-text-with-summary field--label-hidden field__item"])').getall()) 
    
    def get_images(self, response) -> list[str]:
        return []
        
    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        return response.xpath("//span[@class='news-date']/text()").re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
 
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Union[None, str]:
        next_page = response.xpath("//li[contains(@class, 'pager__item--next')]/a[@title='Go to next page']/@href").get()
        return response.urljoin(next_page) if next_page else None