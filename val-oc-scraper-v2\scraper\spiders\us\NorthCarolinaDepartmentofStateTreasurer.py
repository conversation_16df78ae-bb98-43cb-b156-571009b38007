from datetime import datetime
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class NorthCarolinaDepartmentOfStateTreasurer(OCSpider):

    name = "NorthCarolinaDepartmentOfStateTreasurer"

    country="US"

    start_urls_names = {
        "https://www.nctreasurer.com/news/press-releases" : "Press Releases",
    }

    charset = "utf-8"

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
       return response.xpath('//h2[@class="field-content"]//a/@href').getall()
       
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//span[@class="field field--name-title field--type-string field--label-hidden"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//span[@class="field field--name-title field--type-string field--label-hidden"]//text()').getall())

    def get_images(self, response, entry=None) :
        return []
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%A,%Y-%m-%d"
    
    def get_date(self, response) -> str:
        #There are different xpaths for the base_url articles
        date = response.xpath('//div[@class="press_release__title col-12 col-lg-12 noimage"]/date//text()').get()
        if date is None:
            date=response.xpath('//div[@class="press_release__title col-12 col-lg-7"]/date/text()').get()
        if date:
            date_obj = datetime.strptime(date, "%A, %B %d, %Y")
            formatted_date = date_obj.strftime("%A,%Y-%m-%d")
            return formatted_date
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return response.xpath('//a[@title="Go to next page"]/@href').get()

