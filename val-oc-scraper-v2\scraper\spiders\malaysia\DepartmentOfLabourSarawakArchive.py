from scraper.OCSpider import OCSpider
import re
from datetime import datetime
from scraper.utils.helper import body_normalization

class DepartmentOfLabourSarawakArchive(OCSpider):
    name = "DepartmentOfLabourSarawakArchive"

    start_urls_names = {
        "https://www.jtkswk.gov.my/v2/?page_id=2345&playlist=6d719ef&video=d9b21f2": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"
    
    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h4[@class='bdt-post-list-title']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='breadcrumbs']//li//strong//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='post-content-right']//p//text() | //div[@class='post-content description ']/p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@data-elementor-type='wp-post']//div[@class='elementor-widget-container']//p//img//@src | //div[@class='post-content-right']//img//@src").getall()

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        title = response.xpath("//div[@class='breadcrumbs']//li//strong//text()").get().strip()
        match = re.search(r'\b(\d{1,2}) (\w+) (\d{4})\b', title)
        if match:
            try:
                date_obj = datetime.strptime(match.group(0), "%d %B %Y")
                return date_obj.strftime("%Y-%m-%d")
            except:
                pass
        match = re.search(r'\b(\d{2})/(\d{2})/(\d{4})\b', title)
        if match:
            try:
                date_obj = datetime.strptime(match.group(0), "%d/%m/%Y")
                return date_obj.strftime("%Y-%m-%d")
            except:
                pass
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response):
        return response.xpath("//ul[@class='bdt-pagination bdt-flex-center']//li[@class='bdt-active']/following-sibling::li[1]//a/@href").get()