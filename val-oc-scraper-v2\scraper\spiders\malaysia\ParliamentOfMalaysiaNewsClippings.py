from scraper.OCSpider import OCSpider
import re
from scraper.middlewares import HeadlessBrowserProxy

class ParliamentOfMalaysiaNewsClippings(OCSpider):
    name = "ParliamentOfMalaysiaNewsClippings"
    
    start_urls_names = {
        "https://www.parlimen.gov.my/keratan-akhbar.html?uweb=&": "Portal Rasmi Parliamen Malaysia"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    month_translation = {
    "Januari": "January",
    "Februari": "February",
    "Mac": "March",
    "April": "April",
    "Mei": "May",
    "Jun": "June",
    "Julai": "July",
    "Ogos": "August",
    "September": "September", 
    "Oktober": "October", 
    "November": "November", 
    "Disember": "December"
    }
    
    def get_articles(self, response) -> list:
        hbp = HeadlessBrowserProxy()
        articles = []
        for article in response.xpath('//div[@class="irow editable"]//ul/li'):
            url = article.xpath("./a/@href").get()
            title = article.xpath("./p//text()").get()
            date = article.xpath("./p//text()").get()

            if url and title and date:
                full_url = url.strip()
                title = title.strip()
                clean_date = date.strip()
                normalized_date = self.normalize_date_string(clean_date);
                match = re.search(r'\b\d{1,2} [A-Z][a-z]+ \d{4}\b', normalized_date)
                if match:
                    date_str = match.group(0)
                else:
                    date_str = "Unknown"  # or skip, or log warning 
                self.article_data_map[full_url] = {
                    "title": title,
                    "date": date_str,
                    "pdf": full_url
                }
                articles.append(full_url)
        return articles

    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d %B %Y"

   

    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        # normalized_date = self.normalize_date_string(raw_date)
        # try:
        #     date_obj = datetime.strptime(normalized_date, self.date_format())
        #     return date_obj.strftime("%Y-%m-%d")  # or any other desired format
        # except ValueError:
        #     return "Invalid date"

        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return None
    
    def normalize_date_string(self, date_str):
        for local, english in self.month_translation.items():
            if local in date_str:
                return date_str.replace(local, english)
        return date_str