from scraper.OCSpider import OCSpider

class DepartmentOfVeterinaryServicesPressReleases(OCSpider):
    name = "DepartmentOfVeterinaryServicesPressReleases"
    
    start_urls_names = {
        "https://www.dvs.gov.my/index.php/pages/view/5054": "Press Releases",  # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/4669": "Press Releases",  # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/4666": "Press Releases"  # Pagination is not supported
    }
    start_urls_with_no_pagination_set = {
        "https://www.dvs.gov.my/index.php/pages/view/5054",
        "https://www.dvs.gov.my/index.php/pages/view/4669",
        "https://www.dvs.gov.my/index.php/pages/view/4666"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//div[@class='editable']//ol//li | //div//div//div[@class='page-content']//figure[@class='table']"): 
                url = article.xpath(".//a//@href").get()
                title=article.xpath(".//a//text()").get()
                date = article.xpath("//div[@class='editable']//ol//li//span[2]").re_first(r"-?\s*(\d{1,2}\s+\w+\s+\d{4})") or article.xpath("//div[@class='editable']//ol//li//span[3]").re_first(r"-?\s*(\d{1,2}\s+\w+\s+\d{4})") or article.xpath("//div//div//div[@class='page-content']//figure[@class='table']//td//span[contains(text(), '[')]").re_first(r"\[\s*(\d{1,2}\s+\w+)\s*\]")
                if url and title and date:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    self.article_data_map[full_url]={"title": title, "date": date}
                    articles.append(full_url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry 

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date_str = date.split(" ")
        if len(date_str) != 3:
             date += ' 2023'
        month_map = {
                    'Januari': 'January',
                    'Februari': 'February',
                    'Mac': 'March',
                    'April': 'April',
                    'Mei': 'May',
                    'Jun': 'June',
                    'Julai': 'July',
                    'Ogos': 'August',
                    'September': 'September',
                    'Oktober': 'October',
                    'November': 'November',
                    'Disember': 'December',
                    'Jan': 'January',
                    'Feb': 'February',
                    'Mac': 'March',
                    'Apr': 'April',
                    'Mei': 'May',
                    'Jun': 'June',
                    'Jul': 'July',
                    'Ogos': 'August',
                    'Sep': 'September',
                    'Okt': 'October',
                    'Nov': 'November',
                    'Dis': 'December'
                }
        for malay, eng in month_map.items():
            if malay in date:
                date = date.replace(malay, eng)
                break
        return date

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None