from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy

class BankingReviewofVietnam(OCSpider):
    name = "BankingReviewofVietnam"

    start_urls_names = {
        "https://tapchinganhang.gov.vn/kinh-te-xa-hoi": "News"
    }

    start_urls_with_no_pagination_set = {}

    api_start_urls = {
        "https://tapchinganhang.gov.vn/kinh-te-xa-hoi" :
        "https://tapchinganhang.gov.vn/apicenter@/article_lm&sid=46&cond=IEFORCBhLmlkIE5PVCBJTiAoMTY0MjMsMTY0MTksMTYzOTcp&cond_2=IEpPSU4gdGJsX2FydGljbGVfY2F0ZWdvcmllcyBhYyBPTiAoYS5pZD1hYy5hcnRpY2xlX2lkKSA=&cond_3=IEFORCBtZW51X2lkID0gNCA=&cond_4=IE9SREVSIEJZIGEuYXJ0aWNsZV9wdWJsaXNoX2RhdGUgREVTQw==&BRSR={page}&lim=15&tf=ZGVza3RvcC9jYXRlZ29yeS5odG1s"
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self,response):
        start_url = response.meta.get("start_url")
        api_url = self.api_start_urls.get(start_url)
        current_page = response.meta.get("current_page",0)
        url = api_url.format(page=current_page)
        if url :
            yield scrapy.Request(
                url = url,
                method = "GET",
                callback = self.parse,
                dont_filter= True,
                meta = {
                    "start_url" : start_url,
                    "current_page" : current_page
                }
            )
    
    def get_articles(self, response) -> list:  
        articles = list(set(response.xpath('//div[@class="article"]//a[1]/@href').getall()))
        return [url for url in articles if url.endswith(".html")]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = (response.xpath('//h1[@class="article-detail-title"]//text()').get() or 
                 response.xpath('//div[@class="bread-title"]//a/text()').get())
        if title :
            return title.strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article-detail-content"]//p//text()').getall() or response.xpath('//div[@class="bx-detail-article"]//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        date = (response.xpath('//span[@class="format_date"]//text()').re_first("\d{1,2}/\d{1,2}/\d{4}") or 
                response.xpath('//h1[@class="title-vb"]//text()').re_first("\d{1,2}/\d{1,2}/\d{4}"))
        if date:
            return date

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
       next_page = int(response.meta.get("current_page")) + 15
       if response.status == 200:
           return str(next_page)
       return 
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        api_url = self.api_start_urls.get(start_url)
        try :
            next_page = self.get_next_page(response)
            if next_page:
                url = api_url.format(page=next_page)
                yield scrapy.Request(
                    url = url,
                    method = "GET",
                    callback = self.parse_intermediate,
                    meta = {
                        "start_url" : start_url,
                        "current_page" : next_page
                    }
                )
        except Exception as e:
            return 
