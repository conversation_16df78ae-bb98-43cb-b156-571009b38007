from scraper.OCSpider import OCSpider
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
from urllib.parse import unquote,urljoin
import re

class LegalAidDepartmentNewsClippings(OCSpider):
    name = "LegalAidDepartmentNewsClippings"

    start_urls_names = {
        'https://www.jbg.gov.my/index.php/ms-my/icons/keratan-akhbar' : 'News'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set= {
        'https://www.jbg.gov.my/index.php/ms-my/icons/keratan-akhbar'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'justice'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(response.url,timeout=20000),
            callback=self.parse, 
            dont_filter=True, 
            meta ={
                "start_url" : response.url
                }
            )

    def get_articles(self, response):
        base_url = "https://www.jbg.gov.my/"
        articles = []
        mapping ={}
        for element in response.xpath('//div[@data-rlta-element="panel-content"]'):
            url = element.xpath('.//a/@href').get()
            title = element.xpath('.//a/text()').get()
            match = re.search(r'/(\d{4})/', url)
            if match:
                date = match.group(1)
            if title and url and date :
                full_url = urljoin(base_url,url)
                normalized_url = unquote(full_url)
                articles.append(normalized_url)
                mapping[normalized_url] = {
                    'title' : title,
                    'date' : date,
                    'pdf' : url
                }
        self.article_to_date_map.update(mapping)
        return articles 
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('title')

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('date')

    def date_format(self) -> str:
        return "%Y"
    
    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('pdf')

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None