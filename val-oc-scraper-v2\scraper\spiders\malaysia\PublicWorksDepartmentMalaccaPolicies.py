from scraper.OCSpider import OCSpider 
from urllib.parse import urljoin ,  unquote
import re

class PublicWorksDepartmentMalaccaPolicies(OCSpider):
    name = "PublicWorksDepartmentMalaccaPolicies"

    start_urls_names = {
        'https://www.jkrmlk.gov.my/1/page.php?id=1848': 'bulletin'# pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.jkrmlk.gov.my/1/page.php?id=1848"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list = []
        malay_to_english = {
            "Jan" : "January",
            "Feb" : "February",
            "Mac" : "March",
            "Apr": "April",
            "Mei" : "May",
            "Jun" : "June",
            "Jul" : "July",
            "Ogos" : "August",
            "Sep" : "September",
            "Okt" : "October",
            "Nov" : "November",
            "Dis" : "December"
        }
        articles = response.xpath('//table[@class="senarai content_text"]//tbody//tr')
        for article in articles:
            title = article.xpath('.//div[@align="left"]//text()').get()
            url = article.xpath('.//div//a[text()="Muat-turun"]//@href').get()
            date = article.xpath('.//td[3]//text()').get()

            if date and title and url:
                title = title.strip()          
                full_url = urljoin("https://www.jkrmlk.gov.my", url)
                date = date.strip()
                date_parts = re.search(r'(\d+) (\w+) (\d+)' , date)
                eng_month = malay_to_english.get(date_parts.group(2))
                date = f"{date_parts.group(1)} {eng_month} {date_parts.group(3)}"

                if date and full_url:
                    child_article_list.append(unquote(full_url))
                    self.article_url_title_date_mapping[unquote(full_url)]= [title , date]

        return child_article_list
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[0]

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[1]

    def date_format(self) -> str:
        return "%d %B %Y"

    def get_images(self, response) -> list[str]:
       return []
    
    def get_document_urls(self, response, entry=None):
        return unquote(response.url)
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None