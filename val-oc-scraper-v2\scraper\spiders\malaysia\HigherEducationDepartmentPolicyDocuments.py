from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class HigherEducationDepartmentPolicyDocuments(OCSpider):
    name = "HigherEducationDepartmentPolicyDocuments"

    start_urls_names = {
        "https://jpt.mohe.gov.my/portal/index.php/ms/korporat/dokumen-dasar": "News"  # pagination is not supported
    }
    
    start_urls_with_no_pagination_set = {
        "https://jpt.mohe.gov.my/portal/index.php/ms/korporat/dokumen-dasar"
    }
        
    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        return response.xpath('//*[@id="adminForm"]/table/tbody/tr/td[1]/a/@href').getall()
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class= "uk-container"]//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//*[@id="article-16"]/div/p').getall())

    def get_images(self, response) -> list:
        return response.xpath('//*[@class="uk-margin-medium-top"]/p//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        curr_date=response.xpath('//div[@class= "uk-container"]//h1//text()').get().strip()
        curr_date1= curr_date[-4:]
        parsed_date = dateparser.parse(curr_date1, languages=['en', 'ms']) 
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return response.xpath('//div[@class="uk-margin-medium-top"]//a[not(contains(@href, ".jpg")) and not(contains(@href, ".png"))]/@href').getall()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 