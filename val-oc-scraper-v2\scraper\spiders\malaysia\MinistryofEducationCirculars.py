from scraper.OCSpider import OCSpider

class MinistryofEducationCirculars(OCSpider):
    name = "MinistryofEducationCirculars"

    start_urls_names = {
        'https://www.moe.gov.my/pekeliling': 'ministry'
    }

    start_urls_with_no_pagination_set = {}

    country = "Malaysia"

    HEADLESS_BROWSER_WAIT_TIME = 1000

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		}
	}

    charset = "utf-8"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_url_date_docUrl_mapping={}

    def get_articles(self, response) -> list:
        articles = response.xpath('//tr')
        for article in articles:
            title = article.xpath('.//td[1]//a/text()').get()
            child_url = article.xpath('.//td[1]//a/@href').get()
            date = article.xpath('.//td[2]/text()').get()
            if date and child_url:
                self.article_url_date_docUrl_mapping[child_url]=[title, date]
        return list(self.article_url_date_docUrl_mapping.keys())
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_date_docUrl_mapping.get(response.url)[0]

    def get_body(self, response) -> str:
        return ""
       
    def get_date(self, response) -> str:
        return self.article_url_date_docUrl_mapping.get(response.url)[1]

    def date_format(self) -> str:
        return "%d %b %Y"

    def get_images(self, response) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        urls = response.xpath('//a[@class="doclink btn btn-primary"]/@href').getall()
        if urls:
            return [response.urljoin(url) for url in urls]
        else:
            return []
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[@class="paginate_button item next"]/@href').get()