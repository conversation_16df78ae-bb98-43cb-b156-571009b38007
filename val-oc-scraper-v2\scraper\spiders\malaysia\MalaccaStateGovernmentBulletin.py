from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
import dateparser
from datetime import datetime, timezone

class MalaccaStateGovernmentBulletin(OCSpider):
    name = "MalaccaStateGovernmentBulletin"
    
    start_urls_names = {
        "https://www.melaka.gov.my/media2/penerbitan/buletin.html": "News"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='row']//div[@class='col-md-3'] | //div[@class='row']//div[@class='col-lg-3 col-md-3 col-sm-4 col-xs-12']"):
            url = article.xpath(".//a[text()='pdf']//@href").get()
            title = article.xpath(".//div//text()[normalize-space() != 'pdf'][1]").get()
            if not url or not title:
                continue
            if "drive.google.com" in url:
                self.logger.warning(f"Skipping Google Drive link: {url}")
                continue
            self.article_data_map[url] = {"title": title}
            articles.append(url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y-%m"
    
    def get_date(self, response): 
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        if "-" in title:
            date = title.split("-")[-1].strip()
            parsed_date = dateparser.parse(date, languages=['ms']) 
            return parsed_date.strftime("%Y-%m")
        elif '/' in title:
            date = title.split("/")[1].replace(" ", "").strip()
            parsed_date = dateparser.parse(date, languages=['ms']) 
            return parsed_date.strftime("%Y-%m")

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None