from typing import Optional, List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class Heng<PERSON>(OCSpider):
    name = "<PERSON>ng<PERSON>"

    start_urls_names = {
        "https://www.henglihydraulics.com/col39/list": "恒立液压",
        # These start URL are redirecting to same URL mentioned above
        # "https://www.henglihydraulics.com/zh-CN/news/Notify"
        # "https://www.henglihydraulics.com/zh-CN/news/OtherNotify"
        # "https://www.henglihydraulics.com/zh-CN/news/Information" 
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "private_enterprise"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="photoNews w1440"]//ul/li//a//@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return response.xpath('//div[@class="TitInfo tc"]/h1/text()').get()

    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath("//div[@class='w1440 NewsInfo']//p//text()").getall())

    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath("//div[@class='w1440 NewsInfo']//img//@src").getall()

    def date_format(self) -> str:
        return "发布时间：%Y-%m-%d ·"

    def get_date(self, response, entry=None) -> str:
        return response.xpath('//div[@class="other f18"]//time//text()').get()

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//div[@class="Pages"]//a[@class="arr a_next"]//@href').get()
        if next_page:
            return next_page
        else:
            return None