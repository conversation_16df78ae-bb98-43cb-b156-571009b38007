from datetime import datetime
from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from scraper.utils.helper import body_normalization

class ParliamentOfMakassarCity(OCSpider):
    name = "ParliamentOfMakassarCity"
    
    start_urls_names = {
        "https://dprd.mamujukab.go.id/category/berita/": "News",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 20000  # 20 seconds wait time
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="col-md-12 col-12"]//div[@class= "wpo-blog-item"]//div//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2//text()').get() 
      
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "wpo-blog-content"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class= "wpo-blog-item"]//div[@class= "wpo-blog-img"]//img/@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> Optional[str]:
        MONTH_MAP = {
            'Januari': 'January',
            'Februari': 'February',
            'Maret': 'March',
            'April': 'April',
            'Mei': 'May',
            'Juni': 'June',
            'Juli': 'July',
            'Agustus': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Desember': 'December'
        }
        raw_date = response.xpath('//div[@class= "wpo-blog-content"]//ul//li/text()').re_first(r'\d{1,2} \w+ \d{4}')
        if not raw_date:
            return None
        raw_date = raw_date.strip() 
        for indo_month, eng_month in MONTH_MAP.items():
            if indo_month in raw_date:
                raw_date = raw_date.replace(indo_month, eng_month)
                break
        try:
            date_obj = datetime.strptime(raw_date, "%d %B %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError as e:
            raise e
       
    def get_authors(self, response):
        return response.xpath('//div[@class="repshare__col"]/span[@class="repshare__nama"]/text()').get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None