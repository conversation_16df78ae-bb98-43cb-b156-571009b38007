from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class MinnesotaOfficeOfTheStateTreasurer(OCSpider):
    name = 'MinnesotaOfficeOfTheStateTreasurer'
    
    country = "US"
    
    start_urls_names = {
        'https://www.revenue.state.mn.us/newsroom/press-release-archive': 'Minnesota'
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath("//span[@class='field-content']/a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath("//*[@id='content']/text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class= 'clearfix text-formatted field field--name-body field--type-text-with-summary field--label-hidden field__item']//p//text()").getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='field__item']/time/text()").re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath("//li[@class= 'pager__item pager__item--next']//a/@href").get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:
                    return None
                return next_page_url
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None