
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class MalaysiaDepartmentOfIslamicDevelopmentPublications(OCSpider):
    name = "MalaysiaDepartmentOfIslamicDevelopmentPublications"

    start_urls_names = {
        "https://www.islam.gov.my/ms/e-penerbitan":"Publication"
    }
    
    start_urls_with_no_pagination_set={}

    charset = "iso-8859-1"
    
    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
        return response.xpath('//*[@id="adminForm"]/table/tbody/tr/td[2]/a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('/html/body/div[2]/div/div/div[2]/div[1]/dl/dd//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//span[@class="field-value"]//img//@src').getall()
       
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response) -> str:
        date= response.xpath('/html/body/div[2]/div/div/div[2]/div[1]/div[5]/div/dl/dd[1]/time//text()').get()
        parts = [part.strip() for part in date.split(":", 1)]
        part= parts[1]
        try:
            date_obj = datetime.strptime(part.strip(), "%Y-%m-%d")
        except ValueError:
            date_obj = dateparser.parse(part.strip(), languages=["ms", "en"])
            if date_obj:
                return date_obj.strftime("%d-%m-%Y")
    
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return response.xpath('/html/body//dl/dd[4]//a/@href').getall()
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        link = response.xpath('//*[@id="adminForm"]/div/ul/li[13]/a//@href').get()
        if link :
           return link
        else:
           return None