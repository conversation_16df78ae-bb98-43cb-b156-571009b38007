#RegistrationofCompanyAnnualReports
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class RegistrationofCompanyAnnualReports(OCSpider):
    name = "RegistrationofCompanyAnnualReports"

    start_urls_names = {
        "https://www.ssm.com.my/Pages/Publication/Annual_Report/Annual-Report.aspx": "News"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       articles= []
       for article in response.xpath('//div[@class="bullet-off"]//ul'):
           links = article.xpath(".//li//a//@href").getall()
           for link in links:
               if ".pdf" in link:
                articles.append(link)
                title= link.split("/")[-1].split(".")[0]
                self.article_data_map[link]={
                    "title":title,"link":link
                }
       return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        title=self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        date= title[-4:]
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 