from scraper.OCSpider import OCSpider
import scrapy
from datetime import datetime
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class CaliforniaStateTreasurersOffice(OCSpider):
    name="CaliforniaStateTreasurersOffice"

    country="USA"

    charset="iso-8859-1"

    language="English"

    start_urls_names={
        "https://www.treasurer.ca.gov/news/releases/2007/index.asp":"News",
    }

    api_start_urls = {
        "https://www.treasurer.ca.gov/news/releases/2007/index.asp": { 
            "url":"https://www.treasurer.ca.gov/news/releases/{current_page}/index.asp"
        },
    }

    article_data_map = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page", datetime.now().year)
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"].format(current_page=current_page)
        yield scrapy.Request(
            url = api_url,
            method = "GET",
            headers={
                "Content-Type": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;",
            },
            dont_filter=True,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "current_page": current_page,
            },
        )

    @property
    def source_type(self) -> str:
        return 'Ministry'

    @property
    def timezone(self):
        return "America/Los_Angeles"
         
    def get_articles(self, response) -> list:
        try:
            articles = response.xpath("(//*[@id='main_content']//div[contains(@class, 'add_padding')]/ul)[last()]/li")
            article_urls = []
            for article in articles:
                url = article.xpath(".//a/@href").get()
                title = article.xpath(".//a/text()").get()
                date = article.xpath("./text()").get()  
                if url and title and date:
                    full_url = response.urljoin(url.strip())
                    clean_date = date.strip().replace("\xa0", " ") 
                    self.article_data_map[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
                    article_urls.append(full_url)
            return article_urls
        except Exception as e:
            return []

    def get_href(self, entry) -> str:
        return entry
     
    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")

    def get_body(self, response) -> str:
        # Child article have PDF only
        return ""
    
    def get_images(self, response, entry=None) -> List[str]:
        # Child article have PDF only
        return []

    def get_authors(self, response, entry=None) -> list[str]:
        # Child article have PDF only
        return []
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")

    def date_format(self) -> str:
        return '%B %d, %Y'
    
    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        if response.status != 200:
            return None
        return str(int(current_page) - 1)

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            api_url = api_url.format(current_page=next_page)
            yield scrapy.Request(
                url=api_url,
                method='GET',
                headers={
                    "Content-Type": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;",
                },
                callback=self.parse_intermediate, 
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None