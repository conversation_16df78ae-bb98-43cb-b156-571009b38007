from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import urljoin, unquote

class KedahSyariahJuciaryDepartmentEnactment(OCSpider):
    name = "KedahSyariahJuciaryDepartmentEnactment"

    start_urls_names = {
        'https://syariah.kedah.gov.my/ms/Page?type=j6OS/5WDc0A=&pid=BJenAlRn08UDBJQZThwvCg==' : 'Press Clippings', # pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://syariah.kedah.gov.my/ms/Page?type=j6OS/5WDc0A=&pid=BJenAlRn08UDBJQZThwvCg==' 
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(response.url, timeout=20000), 
            callback=self.parse, 
            dont_filter=True, 
            meta ={
                "start_url" : response.url
            }
        )

    malay_days = {
        "Isnin": "Monday",
        "Selasa": "Tuesday",
        "Rabu": "Wednesday",
        "Khamis": "Thursday",
        "Jumaat": "Friday",
        "Sabtu": "Saturday",
        "Ahad": "Sunday"
    }

    malay_months = {
        "Januari": "January",
        "Februari": "February",
        "Mac": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }

    def get_articles(self, response):
        base_url = "https://syariah.kedah.gov.my" 
        mapping = {}
        articles =[]
        for row in response.xpath('//li[@class="item"]'):
            url = row.xpath('.//a/@href').get()
            full_url = urljoin(base_url, url)
            title = row.xpath('.//div[@class="title"]/text()').get()
            raw_date = row.xpath('.//div[@class="date"]/text()').get()
            date = raw_date.replace("Date modified:", "").strip() if raw_date else None
            if full_url and title and date:
                articles.append(full_url)
                mapping[full_url] = {
                    "title": title,
                    "date": date,
                    "pdf": full_url
                    }
        self.article_to_date_map.update(mapping)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('title')

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('date')

    def date_format(self) -> str:
        return "%m/%d/%Y %I:%M:%S %p"
    
    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None