import json
import urllib
from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class TheChineseEducationalArtAssociation(OCSpider):
    name="TheChineseEducationalArtAssociation"

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    charset = "utf-8"

    start_urls_names={
        "http://www.jyshxh.com/news/report":"新闻报道",
        "http://www.jyshxh.com/party/work":"党建工作",
    }

    api_start_urls = {
        "http://www.jyshxh.com/news/report": {
            "url": "http://101.201.60.139/index/newsreporting",
            "payload": {
                "page": "1",
                "limit": "5",
                "cat": "2",
            },
            "article_url": "http://www.jyshxh.com/news/reportMessage?fileid="
        },
        "http://www.jyshxh.com/party/work": {
            "url": "http://101.201.60.139/index/party",
            "payload": {
                "page": "1",
                "limit": "5",
            },
            "article_url": "http://www.jyshxh.com/party/workmsg?id=",
        },
    }

    article_data_map={} #  To fetch the date directly from the article url response 

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        article_url = api_data["article_url"]
        payload = api_data["payload"]
        payload["page"] = payload.get("page")
        params = urllib.parse.urlencode(payload)  
        full_url = f"{api_url}?{params}"
        yield scrapy.Request(
            url = full_url,
            method = "GET",
            headers={
                "Content-Type": "application/json;",
            },
            dont_filter=True,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["page"],
                "article_url": article_url,
            },
        )

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
         
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            articles = data.get("data", [])
            article_urls = [
                self.construct_article_url(response,article)
                for article in articles
                if article
            ]
            return article_urls
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []

    def get_href(self, entry) -> str:
        return entry
     
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='lawsMesMain_title']//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@id='content']//p//text()").getall())
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath("//div[@id='content']//img//@src").getall()

    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        full_url = f"{response.request.meta.get('entry')}"
        return self.article_data_map.get(f'{full_url}')

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        data = response.json().get('data', [])
        return str(int(current_page) + 1) if data!=[] else None
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        article_url = response.meta.get("article_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['page'] = next_page
            params = urllib.parse.urlencode(payload)  
            full_url = f"{api_url}?{params}"
            yield scrapy.Request(
                url=full_url,
                method='GET',
                headers={
                    "Content-Type": "application/json;",
                },
                body=json.dumps(payload),
                callback=self.parse_intermediate, 
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                        'payload': payload,
                        "article_url": article_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None

    def construct_article_url(self,response, article):
        hbp=HeadlessBrowserProxy()
        article_id = article.get('id')
        article_url = response.meta.get('article_url')
        if article_id:
            proxy_url = hbp.get_proxy(f"{article_url}{article_id}",timeout=10000) 
            full_url = f"{proxy_url}"
            self.article_data_map[full_url]=article.get('datetime') 
            return proxy_url  
        return None