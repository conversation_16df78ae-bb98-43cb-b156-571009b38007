from urllib.parse import parse_qs, urlparse
from scraper.OCSpider import OCSpider
import scrapy

class LouisianaWorkforceCommission(OCSpider):
    name = 'LouisianaWorkforceCommission'

    country = "US"

    start_urls_names = {
        "https://www.laworks.net/PublicRelations/PR_PressRelease.asp?Archives=Y&Year=2025&Month=3": "News"
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url", response.url)
        yield scrapy.Request(
            url=start_url,
            callback=self.parse,
            meta={
                'start_url': start_url
            },
            dont_filter=True
        )
        
    charset = "iso-8859-1"

    article_data_map = {}  # Mapping title, dates and PDF with respective articles

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return self.extract_articles_with_dates(response)

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")

    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return "" 

    def get_images(self, response) -> list:
        # Only PDF's are there to scrape
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")

    def get_authors(self, response):
        # Only PDF's are there to scrape
        return []

    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        query_params = parse_qs(urlparse(response.url).query)
        year = int(query_params["Year"][0])
        month = int(query_params["Month"][0])
        if 1< month <= 12:
            month -= 1
        else:
            year -= 1
            month = 12
        if year > 2006:  
            return f"https://www.laworks.net/PublicRelations/PR_PressRelease.asp?Archives=Y&Year={year}&Month={month}"
        return None

    def go_to_next_page(self, response, start_url, current_page=None):
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=next_page,
                callback=self.parse_intermediate,
                dont_filter=True
            )

    def extract_articles_with_dates(self, response):
        mapping = {}
        articles = []
        for article in response.xpath("//div[@id='prList']//div"):
            url = article.xpath(".//a/@href").get()
            title = article.xpath(".//a//@title").get()
            date = article.xpath(".//div//p/preceding-sibling::text()").get()
            if url and title and date:
                full_url = response.urljoin(url.strip())
                title = title.strip()
                clean_date = date.strip()
                articles.append(full_url)
                mapping[full_url] = {"title": title, "date": clean_date, "pdf": [full_url]}
        self.article_data_map.update(mapping)
        return articles