from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from typing import Optional
import re
from datetime import datetime
from urllib.parse import urljoin

class DepartmentOfOccupationalSafetyAndHealthNewsCoverage(OCSpider):
    name = "DepartmentOfOccupationalSafetyAndHealthNewsActs"
    
    start_urls_names = {
        "https://dosh.gov.my/perundangan/akta/akta-2/": "Act",
        "https://dosh.gov.my/perundangan/akta/akta-yang-telah-dimansuhkan/":"Repealed Acts",
    }

    charset = "iso-8859-1"
    
    article_data_map = {}

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list: 
        self.extract_articles_with_dates(response)
        return list(self.article_data_map.keys())

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        if "text/html" in response.headers.get("Content-Type", b"").decode("utf-8", "ignore"):
            self.extract_articles_with_dates(response)
        article_info = self.article_data_map.get(response.url)
        if article_info and "title" in article_info:
            return article_info["title"].strip()
        return "No title found"

    def get_body(self, article) -> str:   
        return ""

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%Y"   
 
    def get_date(self, response) -> Optional[str]:
        article_info = self.article_data_map.get(response.url)
        date_str = article_info.get("date") if article_info else None
        if date_str:
            try:
                datetime.strptime(date_str, "%m-%Y")
                return date_str
            except Exception:
                return None
        else:
            return None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_document_urls(self, response, entry=None) -> list:
        article_info = self.article_data_map.get(response.url)
        if article_info and "pdf" in article_info:
            return article_info["pdf"]
        return []
     
    def get_next_page(self, response):
        return None

    def extract_articles_with_dates(self, response):
        content_type = response.headers.get("Content-Type", b"").decode("utf-8", "ignore")
        if "text/html" not in content_type:
            return self.article_data_map
        mapping = {}
        links = response.xpath('//ul[contains(@class, "wp-block-list")]//li/a')
        for link in links:
            href = link.xpath('./@href').get()
            title = link.xpath('normalize-space(.)').get()
            if not href or not title:
                continue
            full_url = urljoin(response.url, href.strip())
            match = re.search(r"/(\d{4})/(\d{1,2})/", full_url)
            if match:
                year = match.group(1)
                month = match.group(2).zfill(2)
                date = f"{month}-{year}"
            else:
                date = None
            mapping[full_url] = {
                "title": title.strip(),
                "date": date,
                "pdf": [full_url]
            }
        self.article_data_map.update(mapping)
        return self.article_data_map
