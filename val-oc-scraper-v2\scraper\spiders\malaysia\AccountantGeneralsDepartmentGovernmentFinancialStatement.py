from typing import Optional
from scraper.OCSpider import OCSpider

class AccountantGeneralsDepartmentGovernmentFinancialStatement(OCSpider):
    name = "AccountantGeneralsDepartmentGovernmentFinancialStatement"
    
    start_urls_names = {
        "https://www.anm.gov.my/arkib/terbitan/penyata-kewangan-kerajaan-persekutuan": "Government Financial Statements"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.anm.gov.my/arkib/terbitan/penyata-kewangan-kerajaan-persekutuan"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for link in response.xpath("//div[@class='g-block size-33']"):
            url = link.xpath(".//a[contains(@href, '.pdf')]//@href").get()
            title = link.xpath(".//div[@class='alert alert-warning center']//text()").get()
            if url and title:
                full_url = response.urljoin(url)
                self.article_data_map[full_url] = {"title": title.strip()}
                articles.append(full_url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response):
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        date = title.split()[-1]
        return date

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None