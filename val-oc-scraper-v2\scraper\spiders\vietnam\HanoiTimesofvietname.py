from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import dateparser
from typing import Optional
import re
import json

class HanoiTimesofvietname(OCSpider):
    name = "HanoiTimesofvietname"

    start_urls_names = {
        "https://hanoitimes.vn/hanoi/news?com-2759=page-1": "News"
    }

    start_urls_with_no_pagination_set = {}

    api_start_url = {
        "https://hanoitimes.vn/hanoi/news?com-2759=page-1": {
        "url": "https://hanoitimes.vn/api/designs/get-data-results-by-id/2759?params=page-1_except-662508,662547,662548,662725,662731,663223,663282,663153,663141,663136",
        }
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        current_page = response.meta.get("current_page", 1)
        api_url = f"https://hanoitimes.vn/api/designs/get-data-results-by-id/2759?params=page-{current_page}_except-662508,662547,662548,662725,662731,663223,663282,663153,663141,663136"
        api_data["url"] = api_url
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers=headers,
            dont_filter=True,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "current_page": current_page
            },
        )

    articles_to_date = {}

    def get_articles(self, response) -> list:
        data = json.loads(response.text)  
        titles = [item["url"].strip() for item in data["item"]["C063460D32E2C766A135BEC563060BF0"]["items"]]   
        return titles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return  response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//div[@class="embed-portal content-article-HNT"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//figure//img//@src').getall()

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date= response.xpath('//div[@class="article-published-on"]//text()').get()
        date_obj = dateparser.parse(date, languages=['en'])
        return date_obj.strftime("%Y-%m-%d")

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return int(response.meta.get("current_page")) + 1

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_url.get(start_url)
        if not api_data:
           self.logger.error("API data not found for start_url")
           return

        api_url = api_data["url"]
        next_page = self.get_next_page(response, current_page)
        

        if next_page:
          yield scrapy.Request(
            url=api_url,
            method="GET",
            callback=self.parse_intermediate,
            meta={"current_page": next_page, "start_url": start_url},
            dont_filter=True,
           )
        else:
         return None