from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class MotherlandNewspaper(OCSpider):
    name = "MotherlandNewspaper"

    start_urls_names = {
        'https://scov.gov.vn/thoi-su' : 'Newspaper',
    }

    start_urls_with_no_pagination_set = {
        'https://scov.gov.vn/thoi-su'
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'unknown'

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        return response.xpath('(//div[@class="title-big"] | //div[@class="title-small"])//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="article-title common-title-detail title-detail "]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article-content"]//p//text()').getall())

    def get_date(self, response) -> str:
        date = response.xpath('(//span[@class="post-date left"]//text())[1]').get()
        match = re.search(r"\d{2}/\d{2}/\d{4}", date)
        return match.group(0).strip() if match else ""

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//figure[@class="image"]//img/@src').getall()  

    def get_document_urls(self, response, entry=None) -> list[str]:
        return []    

    def get_authors(self, response) -> str :
        return response.xpath('(//div[@class="article-content"]//p)[last()]//strong//text()').get()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None