import re
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import scrapy
from scraper.middlewares import HeadlessBrowserProxy

class MarylandDepartmentOfCommerce(OCSpider):
    name = 'MarylandDepartmentOfCommerce'

    country = "US"
    
    start_urls_names = {
        'https://commerce.maryland.gov/media/press-room': "Press",
        'https://commerce.maryland.gov/media/press-archives':"Press"
    }
    
    def parse_intermediate(self, response):
        # Extract unique article links
        all_articles = list(set(response.xpath("//div[@class='ms-rte-embedcode ms-rte-embedwp']//tr//td//a//@href").getall()))
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        if response.meta.get("use_proxy", True):
            hbp = HeadlessBrowserProxy()
            proxy_url = hbp.get_proxy(response.url, timeout=10000)
            request = scrapy.Request(
                url=proxy_url,
                callback=self.parse_intermediate,
                meta={
                    'start_url': start_url,
                    'use_proxy': False 
                },
                dont_filter=True
            )
            yield request
            return 
        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx,
                    'articles': all_articles,
                    'start_url': start_url
                },
                dont_filter=True
            )

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"
    
    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@data-name='Page Field: Title']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@data-name='Page Field: Page Content']//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%B %d, %Y'
    
    def get_date(self, response) -> str:
        text_raw = "".join(response.xpath("//div[@data-name='Page Field: Page Content']//text()").getall()).strip()
        match = re.search(r"\(([^()]*\d{1,2},\s*\d{4})\)", text_raw)
        if match:
            return match.group(1).strip()
        fallback = re.search(r"(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}", text_raw)
        if fallback:
            return fallback.group(0).strip()
        return None

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No more pages to scrape
        return None