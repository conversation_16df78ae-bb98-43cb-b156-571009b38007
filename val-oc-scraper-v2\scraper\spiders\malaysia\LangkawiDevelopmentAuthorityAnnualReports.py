from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import unquote,urljoin
import re
from scrapy import Selector

class LangkawiDevelopmentAuthorityAnnualReports(OCSpider):
    name = "LangkawiDevelopmentAuthorityAnnualReports"

    start_urls_names = {
        'https://www.lada.gov.my/laporan-tahunan-lada/' : 'Press Clippings',
    }

    start_urls_with_no_pagination_set = {
        'https://www.lada.gov.my/laporan-tahunan-lada/'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(hbp.get_proxy(response.url,timeout=20000), callback=self.parse, dont_filter=True, meta ={"start_url" : response.url})

    malay_months = {
        "Januari": "January",
        "Februari": "February",
        "Mac": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }

    def get_articles(self, response):
        sel = Selector(text=response.text)
        base_url = "https://www.lada.gov.my"
        mapping = {}
        articles = []
        blocks = sel.xpath('//div[contains(@class, "content-container")]')
        mapping = {}
        for block in blocks:
            url = block.xpath('.//a[@class="fusion-no-lightbox"]/@href').get()
            title = block.xpath('.//h4[contains(@class, "fusion-title-heading")]/text()').get()
            if not url and not title:
                continue  
            full_url = urljoin(base_url, url)
            year_match = re.search(r'(19|20)\d{2}', title)
            date = year_match.group(0) if year_match else None
            if full_url and title:
                articles.append(full_url)
                mapping[full_url] = {
                    "title": title.strip(),
                    "date": date,
                    "url": full_url
                }
        self.article_to_date_map.update(mapping)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('title')

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('date')

    def date_format(self) -> str:
        return "%Y"
    
    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None):
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('url')

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None