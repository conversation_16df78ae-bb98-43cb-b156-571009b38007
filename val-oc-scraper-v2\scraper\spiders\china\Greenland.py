import json
import re
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
import chardet
from datetime import datetime
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class Greenland(OCSpider):
    name = "Greenland"
    
    charset = "iso-8859-1"

    HEADLESS_BROWSER_WAIT_TIME = 100

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
           'scraper.middlewares.GeoProxyMiddleware': 350,
        },
        "DOWNLOAD_DELAY": 1,
    }
    
    start_urls_names = {
       "https://www.ldjt.com.cn/home/<USER>/zuixingonggao/": "Latest Announcements", 
       "https://www.ldjt.com.cn/home/<USER>/dingqibaogao/": "Regular Reports", 
         "https://www.ldjt.com.cn/home/<USER>/jituanxinwen/": "News" 
    }

    api_start_urls = {
        "https://www.ldjt.com.cn/home/<USER>/zuixingonggao/": {
            "url": "https://www.ldjt.com.cn/home/<USER>/index.html",
            "payload": {
                "class_id": "26",
                "year": "0",
                "month:": "",
                "page": "1",
                "keyword": "",
            }
        },
        "https://www.ldjt.com.cn/home/<USER>/dingqibaogao/": {
            "url": "https://www.ldjt.com.cn/home/<USER>/index.html",
            "payload": {
                "class_id": "31",
                "year": "0",
                "month:": "",
                "page": "1",
                "keyword": "",
            }
        },
    }

    article_data_map = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        if start_url == "https://www.ldjt.com.cn/home/<USER>/jituanxinwen/":
            article_urls = response.xpath('//ul[@class="ul-listb1"]//a[@class="con"]/@href').getall()
            for url in article_urls:
                yield scrapy.Request(response.urljoin(url), callback=self.parse_article,meta={"start_url": start_url})
            current_url = response.url
            next_page_url = self.get_next_page(response, current_url)
            if next_page_url and "ldjt.com.cn/home/<USER>/jituanxinwen" in next_page_url:
                yield scrapy.Request(next_page_url, callback=self.parse_intermediate, meta={'start_url': start_url})
            
        else:
            start_url = response.meta.get("start_url")
            api_data = self.api_start_urls.get(start_url)
            api_url = api_data["url"]
            payload = api_data["payload"]
            payload["page"] = payload.get("page")

            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers={
                    "Accept": "*/*",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Cache-Control": "no-cache",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",  
                    "Origin": "https://www.ldjt.com.cn",
                    "Pragma": "no-cache",
                    "Referer": "https://www.ldjt.com.cn/home/<USER>/zuixingonggao/",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "X-Requested-With": "XMLHttpRequest",
                },
                dont_filter=True,
                formdata=payload,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": payload["page"],
                },
            )

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            data = response.json()
            print(response.text)
            articles = data.get("data", [])
            article_data = []
            for article in articles:
                if article:
                    article_url = self.construct_article_url(article)
                    if article_url:
                        create_day = article.get("create_day")
                        create_month = article.get("create_month")
                        if create_day and create_month:
                            try:
                                year, month = create_month.split("-")
                                full_date = f"{year}-{month}-{create_day}"
                                date_obj = datetime.strptime(full_date, "%Y-%m-%d")
                                formatted_date = date_obj.strftime(self.date_format())
                            except ValueError:
                                self.logger.error(f"Invalid date format: {create_month} and {create_day}")
                                formatted_date = ""  
                        else:
                            formatted_date = ""
                        self.article_data_map[article_url] = {
                            "title": article.get("title"),
                            "date": formatted_date,
                            "pdf": [article_url]
                        }

                        article_data.append(article_url)
            return article_data
        
        except :
            article_urls = response.xpath('//ul[@class="ul-listb1"]//a[@class="con"]/@href').getall()
            full_urls = [response.urljoin(url) for url in article_urls]
            return full_urls 

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        entry = response.request.meta.get('entry')
        if entry:
            meta = self.article_data_map.get(entry)
            if meta:
                return meta.get("title")
        else:
            print(response.text)
            title = response.xpath('//div[@class="m-detailsb1"]/div[@class="wp"]/h1/text()').get()
        return title

    def get_body(self, response) -> str:
        body_nodes = response.xpath('//div[@class="m-detailsb1"]/div[@class="wp"]/div[@class="txt"]//p/text()').getall()
        if body_nodes:
            return "\n\n".join(body_nodes).strip()
        else:
            return ""

    def get_images(self, response, entry=None) -> List[str]:
        return []

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> List[str]:
        entry = response.request.meta.get('entry')
        if entry:
            meta = self.article_data_map.get(entry)
            if meta:
                return meta.get("pdf", [])
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        entry = response.request.meta.get('entry')
        if entry:
            meta = self.article_data_map.get(entry)
            if meta:
                return meta.get("date")
        
        date = response.xpath('//div[@class="m-detailsb1"]/div[@class="wp"]/div[@class="time"]/text()').get()
        if date:
            return date.strip()
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_url):
        if "ldjt.com.cn/home/<USER>/jituanxinwen" in current_url:
            if 'p=' in current_url:
                try:
                    current_page = int(current_url.split('p=')[-1])
                    next_page = current_page + 1
                    return current_url.split('?')[0] + f"?p={next_page}"
                except ValueError:
                    return None
            else:
                return current_url + "?p=2"
        elif 'last_page' in response.json():
            try:
                total_pages = int(response.json().get('last_page', 0))
                current_page_api = int(response.meta.get('current_page', 1))
                return str(current_page_api + 1) if current_page_api < total_pages else None
            except (json.JSONDecodeError, ValueError, TypeError):
                return None
        return None

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return

        next_page = self.get_next_page(response, response.url)
        if next_page:
            payload = response.meta.get('payload')
            payload['page'] = next_page
            yield scrapy.http.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "Accept": "*/*",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Cache-Control": "no-cache",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "Origin": "https://www.ldjt.com.cn",
                    "Pragma": "no-cache",
                    "Referer": start_url,
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "X-Requested-With": "XMLHttpRequest",
                },
                callback=self.parse_api_response,
                dont_filter=True,
                meta={
                    "api_url": api_url,
                    'current_page': next_page,
                    'start_url': start_url,
                    'payload': payload,
                }
            )
        else:
            logging.info("No more pages to fetch for API URL.")
            yield None

    def construct_article_url(self, article):
        article_id = article.get('content_id')
        if article_id:
            return f"https://www.ldjt.com.cn/home/<USER>/view/{article_id}.pdf"
        return None