from scraper.OCSpider import OCSpider
from datetime import datetime
from scraper.utils.helper import body_normalization
import re

class MinistryofHigherEducationPressReleases(OCSpider):
    name = "MinistryofHigherEducationPressReleases"
    
    start_urls_names = {
        "https://www.mohe.gov.my/hebahan/kenyataan-media?start=0" : "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='content-category']//td[@class='list-title']/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='uk-margin-top']//h1//text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='uk-margin-top']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='uk-margin-top']//img//@src").getall()
        
    def date_format(self) -> str:
        return "%Y/%m"

    def get_date(self, response):
        MALAY_TO_ENGLISH_MONTH = {
            "Januari": "January",
            "Februari": "February",
            "Mac": "March",
            "April": "April",
            "Mei": "May",
            "Jun": "June",
            "Julai": "July",
            "Ogos": "August",
            "September": "September",
            "Oktober": "October",
            "November": "November",
            "Disember": "December"
        }
        raw_date = response.xpath("//ul[@class='uk-list']/li/time/text()").get()
        if raw_date:
            raw_date = raw_date.strip()
            for malay, english in MALAY_TO_ENGLISH_MONTH.items():
                if malay in raw_date:
                    raw_date = raw_date.replace(malay, english)
                    break
            try:
                dt = datetime.strptime(raw_date, "%d %B %Y")
                return dt.strftime(self.date_format())
            except ValueError:              
                return ""
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//div[@class="uk-margin-top"]//p//a//@href').getall()
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return re.sub(r'start=(\d+)', lambda m: f"start={int(m.group(1)) + 10}", response.url)