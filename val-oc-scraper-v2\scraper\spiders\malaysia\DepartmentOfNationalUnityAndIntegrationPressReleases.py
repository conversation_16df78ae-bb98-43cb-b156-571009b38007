from scraper.OCSpider import OCSpider

class DepartmentOfNationalUnityAndIntegrationPressReleases(OCSpider):
    name = "DepartmentOfNationalUnityAndIntegrationPressReleases"
    
    start_urls_names = {
        "https://www.jpnin.gov.my/web/kenyataan-media": "News"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//table//tbody[@id='kenyataan']//tr"):
            url = article.xpath(".//td[@class='border-name']//a//@href").get()
            title = article.xpath(".//td[@class='border-name']//a//text()").get()
            date = article.xpath(".//td[@class='text-center']//text()").get()
            if url and title and date:
                self.article_data_map[url] = {"date":date.strip(), "title" : title}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")    
        
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None