from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class OhioTreasurerOfState(OCSpider):
    name = "OhioTreasurerOfState"

    country="US"

    Language="English"

    start_urls_names = {
        "https://www.tos.ohio.gov/Newsroom" :"Newsroom",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'Ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
       return response.xpath('//div[@class="news-list"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="mb-3"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="col-xl-9 col-md-8 col-12"]//p//text()').getall())
    
    def get_images(self, response, entry=None) :
        return []
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="article-header"]/label//text()').get()
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        # No Pagination, All the pages extracted from the first page only
        return None