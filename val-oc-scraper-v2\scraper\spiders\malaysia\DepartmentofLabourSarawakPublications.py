from scraper.OCSpider import OCSpider
import re

class DepartmentofLabourSarawakPublications(OCSpider):
    name = "DepartmentofLabourSarawakPublications"
    
    start_urls_names = {
        "https://www.jtkswk.gov.my/v2/?page_id=966": "News"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.jtkswk.gov.my/v2/?page_id=966"
    }

    charset = "iso-8859-1"

    article_data_map = {}

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='elementor-accordion']//ol//li"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//a//text()").get()
            if url and title:
                self.article_data_map[url]={"url":url, "title": title}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response):
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        if "Buku Panduan Cegah & Banteras Amalan Buruh Paksa dan Jenayah Pemerdagangan Orang" in title:
            return "2024"
        year_match = re.search(r'(?:^|\D)(\d{4})(?:\D|$)', response.url)
        if year_match:
            year = year_match.group(1)
            if 1900 <= int(year) <= 2100:
                return year
        return "2025"

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response):
        return None