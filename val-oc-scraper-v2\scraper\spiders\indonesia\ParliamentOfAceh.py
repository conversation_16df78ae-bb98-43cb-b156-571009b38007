from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class ParliamentOfAceh(OCSpider):
    name = "ParliamentOfAceh"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"
    
    start_urls_names = {
       "https://dprd.jatengprov.go.id/category/berita/": "Berita",
    #    "https://jdih.dprd.jatengprov.go.id/portal/posts": "Posts",
    }

    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    # def get_articles(self, response) -> list:
    #     response.xpath("//div[@class='jeg_posts jeg_load_more_flag']/article//div[@class='jeg_thumb']//a/@href").getall()
    #     return response.xpath("//a[starts-with(@href, 'https://jdih.dprd.jatengprov.go.id/portal/posts/')]/@href").getall()

    def get_articles(self, response) -> list:
        # First try for dprd.jatengprov.go.id
        articles = response.xpath("//div[@class='jeg_posts jeg_load_more_flag']/article//div[@class='jeg_thumb']//a/@href").getall()
        print("Articles:", articles)
        
        if not articles:
            # Fallback to jdih.dprd.jatengprov.go.id structure
            articles = response.xpath("//a[starts-with(@href, 'https://jdih.dprd.jatengprov.go.id/portal/posts/')]/@href").getall()
        
        # self.logger.info(f"Found {len(articles)} articles.")
        return articles


    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = response.xpath("//div[contains(@class, 'mb-8')]//a[contains(@class, 'fs-2') and contains(@class, 'fw-bold')]/text()").get()
        if title:
            return title.strip()
        else:
            return response.xpath("//h1//text()").get()

    # def get_title(self, response) -> str:
    #     # Try the first XPath
    #     title = response.xpath("//div[contains(@class, 'mb-8')]//a[contains(@class, 'fs-2') and contains(@class, 'fw-bold')]/text()").get()
    #     if title:
    #         return title.strip()
        
    #     # Fallback to generic h1
    #     title = response.xpath("//h1//text()").get()
    #     if title:
    #         return title
        
    #     return ""

        
    def get_body(self, response) -> str:
        body = body_normalization(response.xpath('//div[@id="post-container"]/p/text()').getall())
        if body:    
            return body
        else:
            return body_normalization(response.xpath("//div[@class='content-inner']//p//text()").getall())

    # def get_body(self, response) -> str:
    #     body_paragraphs = response.xpath('//div[@id="post-container"]/p/text()').getall()
    #     if body_paragraphs:
    #         return body_normalization(body_paragraphs)

    #     fallback_paragraphs = response.xpath("//div[@class='content-inner']//p//text()").getall()
    #     return body_normalization(fallback_paragraphs)


    def get_images(self, response) -> list:
        image = response.xpath("//div[contains(@class, 'mb-8')]//img[@class='w-100']/@src").getall()
        if image:
            return image
        else:
            return response.xpath("//div[@class='jeg_featured featured_image ']//img/@src").getall()

    def date_format(self) -> str:
        return"%m-%d-%Y"

    # def get_date(self, response) -> Optional[str]:
    #     raw_date = response.xpath("(//div[contains(@class, 'd-flex') and contains(@class, 'flex-wrap')]//div[contains(@class, 'me-9')])[1]//span/text()").get()
    #     raw_date = raw_date.strip().split(" - ")[0]  # Get "23 Mei 2025"       
    #     parsed = dateparser.parse(raw_date, languages=["id"])
    #     if parsed:
    #         return parsed.strftime(self.date_format())  # "%m-%d-%Y"    
    #     else:
    #         return response.xpath("//div[@class='jeg_meta_date']//text()").get()

    def get_date(self, response) -> str:
        """
        Always returns a date string in self.date_format().
        Falls back in this order:
            1. Primary XPath (the flex-wrap <span>)
            2. Secondary XPath (.jeg_meta_date)
            3. <time datetime="...">
            4. Today’s date (guarantees non-None)
        """
        xpaths = [
            "(//div[contains(@class,'d-flex') and contains(@class,'flex-wrap')]"
            "//div[contains(@class,'me-9')])[1]//span/text()",
            "//div[@class='jeg_meta_date']//text()",
            "//time/@datetime",
        ]

        for xp in xpaths:
            raw = response.xpath(xp).get()
            if raw:
                # clean up and try to parse
                raw = raw.strip().split(" - ")[0]        # drop “ - …” if present
                parsed = dateparser.parse(raw, languages=["id"])
                if parsed:
                    return parsed.strftime(self.date_format())

        # Last-chance fallback → today’s date
        return datetime.now().strftime(self.date_format())


    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="page-item"]/a[@rel="next"]/@href').get()
        if next_page:
            return next_page
        return None
