from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy

class AuditingNewspaper(OCSpider):
    name = "AuditingNewspaper"

    start_urls_names = {
        "http://baokiemtoan.vn/chinh-tri": "News" # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "http://baokiemtoan.vn/chinh-tri"
    } 
    
    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:  
        return response.xpath('//h3[@class="b-grid__title"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="c-detail-head__title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="b-maincontent"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="b-maincontent"]//img/@src').getall()

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return response.xpath('//span[@class="c-detail-head__time"]//text()').re_first("\d{1,2}/\d{1,2}/\d{4}")

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
       return None