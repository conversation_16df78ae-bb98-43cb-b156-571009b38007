import re
from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfMamujuRegency(OCSpider):
    name = "ParliamentOfMamujuRegency"
    
    start_urls_names = {
        "https://dprd.mamujukab.go.id/category/berita/": "News"
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "sek-pg-content"]//h2//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "czr-wp-the-content"]//p/text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class= "czr-wp-the-content"]//p//img/@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response): 
        url = response.url
        match = re.search(r'/(\d{4})/(\d{2})/(\d{2})/', url)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month}-{day}"
        return None

    def get_authors(self, response):
        return response.xpath('//div[@class="repshare__col"]/span[@class="repshare__nama"]/text()').get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None