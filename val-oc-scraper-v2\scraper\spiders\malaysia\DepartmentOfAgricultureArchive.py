from scraper.OCSpider import OCSpider
import re

class DepartmentOfAgricultureArchive(OCSpider):
    name = "DepartmentOfAgricultureArchive"

    start_urls_names = {
        "https://www.doa.gov.my/index.php/pages/view/1196?mid=405": "Archive"
    }

    start_urls_with_no_pagination_set = {
        "https://www.doa.gov.my/index.php/pages/view/1196?mid=405"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//a[contains(@href, '.pdf')]"):
                url = article.xpath(".//@href").get()
                title = article.xpath(".//text()").get()
                if url and title:
                    full_url = url
                    title= title.strip()
                    self.article_data_map[full_url]={"title": title, "full_url": url}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        match = re.search(r'(\d{4})', response.url)
        if match:
            return match.group(1)
        return ""

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None