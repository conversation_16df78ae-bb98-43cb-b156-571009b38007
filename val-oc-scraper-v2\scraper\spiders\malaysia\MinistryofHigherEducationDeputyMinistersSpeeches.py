from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import dateparser
from typing import Optional

class MinistryofHigherEducationDeputyMinistersSpeeches(OCSpider):
    name = "MinistryofHigherEducationDeputyMinistersSpeeches"
    
    start_urls_names = {
        "https://www.mohe.gov.my/en/broadcast/teks-ucapan-yb-timbalan-menteri": "Speeches"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"    
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='content-category']//td[@class='list-title']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h2//text() | //h1//text()").get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='uk-margin-top']//p//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='uk-margin-top']//img//@src").getall()

    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> Optional[str]:
        date_str = response.xpath("//div[@class='uk-margin-top']//h4//text() | //p[@class='uk-margin-top uk-margin-remove-bottom uk-article-meta']/time/text()").get()
        if date_str:
            parts = [part.strip() for part in date_str.split('|') if part.strip()]
            for part in parts:
                parsed_date = dateparser.parse(part, languages=['ms', 'en'])
                if parsed_date:
                    return parsed_date.strftime(self.date_format())
        return None

    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath("//div[@class='uk-margin-top']//p//a//@href").getall()
    
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return response.xpath("//a[contains(@class, 'next')]//@href").get()