from typing import Optional
from scraper.OCSpider import OCSpider

class PenangStateGovernmentAnnualReports(OCSpider):
    name = "PenangStateGovernmentAnnualReports"
    
    start_urls_names = {
        "https://www.penang.gov.my/index.php/pejabat-suk/penerbitan/laporan-tahunan?lang=ms" : "PenangAnnualReports"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}  # Mapping title to child articles from start URL

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='sppb-container-inner']//div[@class='clearfix ']//div[@class='sppb-addon sppb-addon-header sppb-text-center']"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//h6//text()").get()
            if url and title:
                self.article_data_map[url] = {"title" : title}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response): 
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        date = title.split()[-1]
        return date

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to crawl
        return None