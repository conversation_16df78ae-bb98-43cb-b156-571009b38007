import logging
from typing import Optional, Union
from urllib.parse import urljoin
import scrapy
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class OrientSecurities(OCSpider):
    name = 'OrientSecurities'
    
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 20000
    
    article_data_map ={}
    
    # custom_settings = {
    #         "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    # }

    start_urls_names = {
        'https://www.dfzq.com.cn/osoa/views/main/aboutus/companynews/index.shtml': 'Company News',
        'https://www.dfzq.com.cn/osoa/views/main/aboutus/mediacoverage/index.shtml': '',
    #     # 'https://www.dfzq.com.cn/osoa/views/main/investorrelations/stockinformation/index.shtml': '',# just information page nothing to extract
    #      'https://www.dfzq.com.cn/osoa/views/main/investorrelations/periodicreport/index.shtml': '',
    #      'https://www.dfzq.com.cn/osoa/views/main/investorrelations/circulars/index.shtml': ''
    }
    
    api_start_url = {
        'https://www.dfzq.com.cn/osoa/views/main/aboutus/companynews/index.shtml': {
            'url': 'https://www.dfzq.com.cn/servlet/json',
            'payload': {
                "rows": "9",
                "curnum": "1",
                "catalogId": "64",
                "isCutOutDate":"1",
                "keyword":"",
                "year":"",
                "start_date":"",
                "end_date":"",
                "timeFlag":"",
                "funcNo":"501012"
            },
        },
        'https://www.dfzq.com.cn/osoa/views/main/aboutus/mediacoverage/index.shtml': {
            'url': 'https://www.dfzq.com.cn/servlet/json',
            'payload': {
                "rows": "9",
                "curnum": "1",
                "catalogId": "245",
                "isCutOutDate":"1",
                "keyword":"",
                "year":"",
                "start_date":"",
                "end_date":"",
                "timeFlag":"",
                "funcNo":"501012"
            },
        },
        'https://www.dfzq.com.cn/osoa/views/main/investorrelations/periodicreport/index.shtml': {
            'url': 'https://www.dfzq.com.cn/servlet/json',
            'payload': {
                "rows": "6",
                "curnum": "1",
                "catalogId": "81",
                "isCutOutDate":"1",
                "keyword":"",
                "year":"2024",
                "start_date":"",
                "end_date":"",
                "timeFlag":"",
                "funcNo":"501012"
            },
        },
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["curnum"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Origin": "https://www.dfzq.com.cn",
            "Referer": "https://www.dfzq.com.cn/osoa/views/main/aboutus/companynews/index.shtml",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "X-Requested-With": "XMLHttpRequest",
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
    
    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
    # data = response.json()
    # articles = []

    # # base_url = "https://www.dfzq.com.cn/"
    # for item in data.get("results", [])[0].get("data", []):
    #     url = item.get("link_url")
    #     title = item.get("title")
    #     date = item.get("publish_date")

    #     if url and title and date:
    #         full_url = url
    #         clean_title = title.strip()

    #         try:
    #             # Decode garbled Chinese characters (from latin1 to utf-8)
    #             clean_title = clean_title.encode("latin1").decode("utf-8")
    #         except UnicodeDecodeError:
    #             pass  # Keep original if decoding fails

    #         clean_date = date.strip()

    #         # Check if it's a PDF
    #         pdf = full_url if ".pdf" in full_url.lower() else "None"

    #         # Store in your article map
    #         self.article_data_map[full_url] = {
    #             "title": clean_title,
    #             "date": clean_date,
    #             "pdf": pdf
    #         }

    #         articles.append(full_url)

    # return articles

 
        
        try:
            data = response.json()
            link_urls = [item['link_url'] for item in data['results'][0]['data']]
            articles = []

            for link in link_urls:
                full_url = f"https://www.dfzq.com.cn/osoa/views{link}"
                articles.append(full_url)
                self.logger.debug(f"Article URL: {full_url}")  # Better than print in Scrapy

            return articles  # Return after the loop

        except Exception as e:
            self.logger.error("An Exception occurred in parse method.")
            self.logger.error(str(e))
            return []
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h4//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="word word_style1"]//p//span//text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%Y/%m/%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="top"]//p//text()').get()
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return ""
    
    def get_next_page(self, response, current_page) -> Union[None, str]:
        if response.status!=200:
            return None
        else:
            return current_page + 1
    
        
    def get_page_flag(self) -> bool:
        return True

    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            logging.info("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        print("THE VALUE OF NEXT PAGE IS:", next_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                method="POST",
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url, 'api_url': api_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None