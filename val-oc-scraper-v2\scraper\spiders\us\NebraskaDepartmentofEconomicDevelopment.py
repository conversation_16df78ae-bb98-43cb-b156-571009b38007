from scraper.utils.helper import body_normalization
from scraper.OCSpider import OCSpider

class NebraskaDepartmentOfEconomicDevelopment(OCSpider):
    name = 'NebraskaDepartmentOfEconomicDevelopment'

    country = "US"

    start_urls_names = {
        "https://opportunity.nebraska.gov/news/":"News",
    }

    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            "scraper.middlewares.HeadlessBrowserProxy": 350,
        },
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "DOWNLOAD_DELAY": 2,
        "CONCURRENT_REQUESTS": 1,
        "CONCURRENT_REQUESTS_PER_DOMAIN": 1,
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        articles = []
        selectors = [
            "//article//a[contains(@href, 'opportunity.nebraska.gov') and not(contains(@href, '/author/'))]/@href",
            "//div[contains(@class, 'content')]//a[contains(@href, 'opportunity.nebraska.gov') and not(contains(@href, '/author/')) and not(contains(@href, '/category/'))]/@href",
            "//main//a[contains(@href, 'opportunity.nebraska.gov') and not(contains(@href, '/author/')) and not(contains(@href, '/category/'))]/@href",
            "//a[contains(@href, 'opportunity.nebraska.gov') and contains(@href, '2025') and not(contains(@href, '/author/')) and not(contains(@href, '/category/'))]/@href"
        ]
        for selector in selectors:
            found_articles = response.xpath(selector).getall()
            if found_articles:
                articles.extend(found_articles)
                break
        unique_articles = list(set(articles))
        valid_articles = []
        for article in unique_articles:
            if article.startswith('http'):
                valid_articles.append(article)
            elif article.startswith('/'):
                valid_articles.append(response.urljoin(article))
        if not valid_articles:
            all_links = response.xpath("//a/@href").getall()
            for link in all_links:
                if ('opportunity.nebraska.gov' in link and 
                    'author' not in link and 
                    'category' not in link and
                    'wp-content' not in link and
                    'wp-admin' not in link and
                    link.count('/') > 3):
                    if link.startswith('http'):
                        valid_articles.append(link)
                    elif link.startswith('/'):
                        valid_articles.append(response.urljoin(link))
        return list(set(valid_articles))

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title_selectors = [
            "//h1[@class='entry-title']//text()",
            "//h1[contains(@class, 'title')]//text()",
            "//h1//text()",
            "//title//text()",
            "//h1[@class='wp-block-post-title']//text()",
            "//header//h1//text()"
        ]
        for selector in title_selectors:
            title = response.xpath(selector).get()
            if title:
                title = title.strip()
                if ' - Nebraska Department of Economic Development' in title:
                    title = title.replace(' - Nebraska Department of Economic Development', '')
                return title
        
        return ""
        
    def get_body(self, response) -> str:
        body_selectors = [
            "//div[@class='entry-content']//p//text()",
            "//div[contains(@class, 'content')]//p//text()",
            "//article//p//text()",
            "//main//p//text()",
            "//div[contains(@class, 'post-content')]//p//text()",
            "//div[@class='wp-block-group__inner-container']//p//text()"
        ]
        for selector in body_selectors:
            body_parts = response.xpath(selector).getall()
            if body_parts:
                return body_normalization(body_parts)
        all_paragraphs = response.xpath("//p//text()").getall()
        return body_normalization(all_paragraphs)

    def get_images(self, response) -> list:
        image_selectors = [
            "//div[@class='entry-content']//img/@src",
            "//div[contains(@class, 'content')]//img/@src",
            "//article//img/@src",
            "//main//img/@src",
            "//img/@src"
        ]
        for selector in image_selectors:
            images = response.xpath(selector).getall()
            if images:
                absolute_images = []
                for img in images:
                    if img.startswith('http'):
                        absolute_images.append(img)
                    elif img.startswith('/'):
                        absolute_images.append(response.urljoin(img))
                return absolute_images
        return []
    
    def date_format(self) -> str:
        return "%b %d, %Y"
    
    def get_date(self, response) -> str:
        date_selectors = [
            "//p[@class='post-meta']/span[@class='published']/text()",
            "//time/@datetime",
            "//time//text()",
            "//span[contains(@class, 'date')]//text()",
            "//div[contains(@class, 'meta')]//text()",
            "//p[contains(@class, 'meta')]//text()",
            "//span[contains(@class, 'published')]//text()"
        ]
        for selector in date_selectors:
            date_text = response.xpath(selector).get()
            if date_text:
                date_text = date_text.strip()
                if date_text:
                    return date_text
        url = response.url
        import re
        date_match = re.search(r'/(\d{4})/(\d{2})/(\d{2})/', url)
        if date_match:
            year, month, day = date_match.groups()
            from datetime import datetime
            date_obj = datetime(int(year), int(month), int(day))
            return date_obj.strftime("%b %d, %Y")
        return ""
    
    def get_authors(self, response):
        author_selectors = [
            "//span[contains(@class, 'author')]//text()",
            "//div[contains(@class, 'author')]//text()",
            "//p[contains(@class, 'meta')]//a[contains(@href, 'author')]//text()",
            "//a[contains(@href, 'author')]//text()"
        ]
        for selector in author_selectors:
            author = response.xpath(selector).get()
            if author:
                return author.strip()
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page_selectors = [
            "//div[@class='pagination clearfix']//div[@class='alignleft']//a/@href",
            "//a[contains(@class, 'next')]/@href",
            "//a[contains(text(), 'Next')]/@href",
            "//a[contains(text(), 'Older')]/@href",
            "//nav[contains(@class, 'pagination')]//a[contains(@class, 'next')]/@href",
            "//div[contains(@class, 'nav-links')]//a[contains(@class, 'next')]/@href"
        ]
        for selector in next_page_selectors:
            next_page = response.xpath(selector).get()
            if next_page:
                if next_page.startswith('http'):
                    return next_page
                elif next_page.startswith('/'):
                    return response.urljoin(next_page)
        return None