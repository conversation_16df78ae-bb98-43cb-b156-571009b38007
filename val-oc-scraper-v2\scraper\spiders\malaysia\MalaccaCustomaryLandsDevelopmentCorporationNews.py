from scraper.OCSpider import OCSpider 
from scraper.utils.helper import body_normalization

class MalaccaCustomaryLandsDevelopmentCorporationNews(OCSpider):
    name = "MalaccaCustomaryLandsDevelopmentCorporationNews"

    start_urls_names = {
        'https://ptg.melaka.gov.my/en/news/view': 'Latest News'
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        return response.xpath('//a[@class="read-more-btn"]/@href').getall() 
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        # print( response.xpath('//section[@class="page-heading"]//h3//text()').get() )
        return( response.xpath('//section[@class="page-heading"]//h3//text()').get() )

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//section[@class="page-content"]//text()').getall())

    def get_date(self, response) -> str:
       return response.xpath('//div[@class="news-date"]//text()').get()

    def date_format(self) -> str:
        return "%d %b %Y"

    def get_images(self, response) -> list[str]:
       return []
    
    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[@class="page-link" and @rel="next"]//@href').get()