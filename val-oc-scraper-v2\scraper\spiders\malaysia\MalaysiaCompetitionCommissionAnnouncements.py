from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MalaysiaCompetitionCommissionAnnouncements(OCSpider):
    name = "MalaysiaCompetitionCommissionAnnouncements"

    start_urls_names = {
        'https://www.mycc.gov.my/announcement' : 'Announcements',
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        hrefs = response.xpath('//div[@class="announce-title"]//a//@href').getall()
        return [response.urljoin(href.strip()) for href in hrefs if href.strip()]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="page-header"]//span//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="NP-body"]//p').getall())
    
    def date_format(self) -> str:
       return "%d %b %Y"

    def get_date(self, response) -> str:
        date = response.xpath('(//div[@class="NP-date"]//text())[2]').get()
        if date :
            return date
        self.logger(f'No date found for : {response.url}')
        return None

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None):
        pdf_href = response.xpath('//div[@class="NP-pdf"]//a/@href').get()
        return [response.urljoin(pdf_href)] if pdf_href else []

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        next_page_relative = response.xpath('//li[contains(@class, "pager__item--next")]/a/@href').get()
        return response.urljoin(next_page_relative)