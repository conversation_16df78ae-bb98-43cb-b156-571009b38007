from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
from scraper.middlewares import HeadlessBrowserProxy

class TheHealthandLifeNewspaper(OCSpider):
    name = "TheHealthandLifeNewspaper"

    start_urls_names = {
        f"https://suckhoedoisong.vn/timelinelist/1691474/{1}.htm": "News" # Pagination is not suported
        }
    
    start_urls_with_no_pagination_set = {
        "https://suckhoedoisong.vn/timelinelist/1691474/1.htm"  
    }
    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('/html/body/div/div/h3/a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@itemprop="articleBody"]//p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//div[@itemprop="articleBody"]//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//*[@id="admwrapper"]/div[3]/div[2]/div/div/div[1]/div/div[1]/div[1]/div[2]/div[1]/div/span//text() | //*[@id="admwrapper"]/div[3]/div[3]/div/div[1]/div[1]//div/span//text()').get()
        clean_text = date.strip()
        date_part = clean_text.split("|")[0].strip().split(" ")[0]
        date_obj = dateparser.parse(date_part, languages=['vi'])
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None