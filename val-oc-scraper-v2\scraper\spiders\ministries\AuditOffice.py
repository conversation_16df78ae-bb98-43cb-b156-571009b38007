import re
from typing import Optional
from bs4 import BeautifulSoup
from scraper.OCSpider import OCSpider

class AuditOffice(OCSpider):

    name = 'AuditOffice'

    start_urls_names = {
        'http://www.audit.gov.cn/n4/n20/n526/index.html': '审计署动态',
        'http://www.audit.gov.cn/n4/n19/index.html':'审计要闻',
        'http://www.audit.gov.cn/n4/n20/n524/index.html':'地方动态'

    }

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        if "/index.html" in response.url:
            #return response.xpath("//span[@id='comp_192']//table//tr//a")
            return response.xpath('//*[@id="comp_10044770"]/dl/dt/a')
        else:
            return response.xpath("//table//tr//a")

    regex_id = re.compile(r".*\/(.*)\.")
    
    def get_id(self, entry) -> Optional[str]:
        href = self.get_href(entry)
        return self.regex_id.search(href).group(1)

    def get_href(self, entry) -> str:
        article_link = entry.attrib['href']
        return article_link


    def get_title(self, response) -> str:
        title = response.xpath('//meta[@name="ArticleTitle"]//@content').extract_first()
        return title

    def get_body(self, response) -> str:
        # return response.css('#con_con').get()
        return response.xpath('//*[@id="textSize"]/p//text()').get()

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        # date = response.xpath("//span[@id='con_time']//text()").extract_first()
        date = response.xpath('/html/body/div[3]/div[2]/div/div[2]/dl/dd//text()').extract_first()

        date = date.replace("【发布时间：",'').replace("年","-").replace("月","-").replace("日","").replace("】","")
        return date
        
    def get_images(self, response) -> list:
        images = []

        for imgTag in BeautifulSoup(self.get_body(response), features="lxml").find_all('img'):
            images.append(response.urljoin(imgTag['src']))
        return images

    def get_authors(self, response):
        author = response.xpath('//meta[@name="Author"]/@content').extract_first()
        return author

    current_page = 0

    def get_next_page(self, response) -> str:
        if "404 Not Found" in response.text:
            return None
        get_next_page_url =  response.urljoin(f"index_192_{self.current_page+1 }.html")
        self.current_page += 1
        return get_next_page_url