from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import dateparser

class Parliamentofmojokertocity(OCSpider):
    name = "Parliamentofmojokertocity"

    start_urls_names = {
        "https://dprd.mojokertokota.go.id/berita": "News",
        "https://dprd.mojokertokota.go.id/prokum": "News"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self):
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Jakarta"

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='entry-title title-sm']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='single-event']//h4//text()").get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='col-md-8 col-lg-9']//p//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="col-md-8 col-lg-9"]//img//@src').getall()

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        raw_date = response.xpath('//div[@class="entry-meta"]//li//text()').get()
        if not raw_date:      
            return
        raw_date = raw_date.strip()
        try:
            date_obj = datetime.strptime(raw_date, "%d %B %Y")
            return date_obj.strftime(self.date_format())
        except ValueError as e:
            try:
                parsed_date = dateparser.parse(raw_date, languages=['id'])
                if parsed_date:
                    return parsed_date.strftime(self.date_format())
            except Exception:
                pass

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return response.xpath('//div[@class="d-flex justify-content-center mt-4"]//a[@rel="next"]//@href').get()