from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class ThePublicHealthNewspaper(OCSpider):
    name = "ThePublicHealthNewspaper"

    start_urls_names = {
        "https://suckhoecongdongonline.vn/suc-khoe-doi-song/": "News",# Pagination is not supported
        }
    
    start_urls_with_no_pagination_set = {
        "https://suckhoecongdongonline.vn/suc-khoe-doi-song/"
    }

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//*[@id="pagecate_update"]/div/div/div[1]/div[1]/div/div[2]/h3/a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="container"]//p[@style="text-align: justify;"]//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//figure//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date_text = response.xpath('(//div[@class="time-detail"]//text())[2]').get().strip()
        dt = datetime.strptime(date_text, "%d/%m/%Y %H:%M")
        formatted_date = dt.strftime("%Y-%m-%d")
        return formatted_date

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None