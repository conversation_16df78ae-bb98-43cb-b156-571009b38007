from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MinistryOfConstructionVietnam(OCSpider):
    name = "MinistryOfConstructionVietnam"

    start_urls_names = {
        "https://moc.gov.vn/vn/chuyen-muc/1205/tin-tuc.aspx": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="nhomtinct_group_content"]//a[contains(@href, "/vn/tin-tuc/")]/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="News_Detail_Title"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="divArticleDescription2"]//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return  response.xpath('//span[@class="News_Time_Post"]/text()').re_first(r'\d{2}/\d{2}/\d{4}')

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None