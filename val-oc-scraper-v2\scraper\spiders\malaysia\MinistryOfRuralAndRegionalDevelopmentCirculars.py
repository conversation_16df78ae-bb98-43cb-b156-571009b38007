from typing import List
from scraper.OCSpider import OCSpider
import re

class MinistryOfRuralAndRegionalDevelopmentCirculars(OCSpider):
    name = "MinistryOfRuralAndRegionalDevelopmentCirculars"

    start_urls_names = {
        "https://www.rurallink.gov.my/" : "rural_pekeliling"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='uc_post_list_title']//a//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath("//h2[@class='elementor-heading-title elementor-size-default']//text()").get().strip()
    
    def get_body(self, response, entry=None) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response, entry=None) -> str:
        raw_date = response.xpath('//div[@class="elementor-widget-container"]/p[contains(text(), "Tarikh Dikemaskini")]/text()').get()
        if raw_date:
            raw_date = raw_date.strip()
            match = re.search(r'(\d{1,2} \w+ \d{4})', raw_date)
            if match:
                return match.group(1)
        return ""

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath(
            '//a[contains(@href, "docs.jpa.gov.my/docs/flipbook/Buku-Setitik-Nila")]/@href | '
            '//div[@class="toolbar"]//a//@href'
        ).getall()
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath("//div[@class='elementor-widget-container']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        # No next page to crawl
        return None
