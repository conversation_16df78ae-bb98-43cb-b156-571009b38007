from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import urljoin,unquote
from scraper.utils.helper import body_normalization

class BankRakyatNews(OCSpider):
    name = "BankRakyatNews"

    start_urls_names = {
       "https://www.bankrakyat.com.my/portal-main/news" : "News"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    # article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def get_articles(self, response) -> list: 
        articles = response.xpath('//div[@class="col-lg-12 col-md-12 col-sm-12"]//a/@href').getall()
        print(articles)
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="elementor-heading-title elementor-size-default"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content entry clearfix"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//span[@class="date meta-item tie-icon"]//text()').get()

    def get_authors(self, response):
        return response.xpath('//span[@class="meta-author"]//text()').getall()

    def get_document_urls(self, response, entry=None):
        return []
  
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        base_url = "https://www.bankrakyat.com.my"
        next_page_url = response.xpath('//li[@class="next"]//a/@href').get()
        # full_url = urljoin(base_url,next_page_url)