# import scrapy
# from scraper.OCSpider import OCSpider
# from typing import List
# from scraper.utils.helper import body_normalization

# class WesternSecurities(OCSpider):
#     name = "WesternSecurities"

#     start_urls_names = {
#         "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200270001":"西部证券",
#         "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200270002":"西部证券",
#         "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200260001":"西部证券",
#     }
    
#     api_start_urls = {
#         'https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200270001': {
#             "url": "https://www.west95582.com/jdw/public/list.jsp",
#             "headers":{
#                 "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
#                 },
#             "payload": {
#                 "area" :"list",
#                 "classid":"001000200270001",
#                 "pageIndex":"1",
#                 "pageSize":"20",
#                 "year":"0"
#             },
#         },    
#         'https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200270002' : {
#             "url": "https://www.west95582.com/jdw/public/list.jsp",
#             "headers":{
#                 "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
#                 },
#             "payload": {
#                 "area" :"list",
#                 "classid":"001000200270002",
#                 "pageIndex":"1",
#                 "pageSize":"20",
#                 "year":"0"
#             },
#         },    
#         'https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200260001' : {
#             "url": "https://www.west95582.com/jdw/public/list.jsp",
#             "headers":{
#                 "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
#                 },
#             "payload": {
#                 "area" :"list",
#                 "classid":"001000200260001",
#                 "pageIndex":"1",
#                 "pageSize":"20",
#                 "year":"0"
#             },
#         },    
#     }

#     custom_settings = {
#         "DOWNLOADER_MIDDLEWARES": {
#             'scraper.middlewares.HeadlessBrowserProxy': 350,
#         },
#         "DOWNLOAD_DELAY": 10, 
#         "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
#     }
    
#     HEADLESS_BROWSER_WAIT_TIME = 20000

#     article_data_map ={}

#     def parse_intermediate(self, response):
#         start_url = response.meta.get("start_url")
#         api_data = self.api_start_urls.get(start_url)
#         if not api_data:
#             self.logger.error(f"No API configuration found for start_url: {start_url}")
#             return
#         api_url = api_data["url"]
#         payload = api_data["payload"]
#         headers= api_data["headers"]
#         current_page = payload["pageIndex"]
#         yield scrapy.FormRequest(
#             url=api_url,
#             method="POST",
#             headers=headers,
#             formdata=payload,
#             callback=self.parse,
#             dont_filter=True,
#             meta={
#                 "start_url": start_url,
#                 "api_url": api_url,
#                 "payload": payload,
#                 "current_page": current_page
#             },
#         )

#     charset = "iso-8859-1"

#     @property
#     def source_type(self) -> str:
#         return 'SOE'

#     @property
#     def timezone(self):
#         return "Asia/Shanghai"
    
#     def get_articles(self, response) -> list:
#         self.extract_articles_with_dates(response)
#         return [response.urljoin(link) for link in response.xpath("//div[@class='txtmain']//a//@href").getall()]
        
#     def get_href(self, entry) -> str:
#         return entry   

#     def get_title(self, response) -> str:
#         return self.article_data_map[response.request.meta.get('entry')].get("title")
    
#     def get_body(self, response) -> str:
#         return body_normalization(response.xpath("//div[@id='contentDiv']//p//text()").getall())

#     def get_images(self, response, entry=None) -> List[str]:
#         return response.xpath("//div[@id='contentDiv']//img//@src").getall()

#     def get_authors(self, response, entry=None) -> list[str]:
#         return [] 
    
#     def date_format(self) -> str:
#         return "%Y-%m-%d"
    
#     def get_date(self, response) -> str:
#         date = self.article_data_map[response.request.meta.get('entry')].get("date")
#         date=date.replace("Notice","").replace("Warehousing","").replace("Western","").replace("Announcement","")
#         return date

#     def get_authors(self, response, entry=None) -> list[str]:
#         return []
    
#     def get_document_urls(self, response, entry=None):
#         pdf = response.xpath("//div[@class='mainlist maintexts']//a[contains(@href,'.jsp')]//@href").getall()
#         if pdf:
#             return pdf
#         return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
#     def get_page_flag(self) -> bool:
#         return False 

#     def get_next_page(self, response):
#         current_page = int(response.meta.get("current_page"))
#         next_page = int(current_page)+1
#         if next_page == 3:
#             return None
#         if next_page:
#             return str(next_page)
#         return None

#     def go_to_next_page(self, response, start_url=None, current_page=None):
#         api_url = response.meta.get("api_url")
#         payload = response.meta.get("payload")
#         headers = response.meta.get("headers")
#         next_page = self.get_next_page(response)
#         payload["pageIndex"] = next_page
#         if next_page:
#             yield scrapy.FormRequest(
#                 url=api_url,
#                 method='POST',
#                 formdata=payload,
#                 headers=headers,
#                 callback=self.parse_intermediate,
#                 meta={
#                     "start_url": start_url,
#                     "api_url": api_url,
#                     "payload": payload,
#                     "current_page": next_page
#                 }
#             )
#         else:
#             self.logger.info(f"No more pages to crawl for start_url: {start_url}") 
    
#     def extract_articles_with_dates(self, response):
#         mapping = {}
#         for article in response.xpath("//div[@class='txtmain']"):
#             url = article.xpath(".//a//@href").get()
#             title = article.xpath(".//a//text()").get()
#             date = article.xpath(".//span[@class='txttime']//text()").get()
#             if url and title and date:
#                 full_url = response.urljoin(url.strip())
#                 clean_date=date.strip()
#                 mapping[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
#             self.article_data_map.update(mapping)
#         return self.article_data_map
import scrapy
from scraper.OCSpider import OCSpider
from typing import List
from scraper.utils.helper import body_normalization

class WesternSecurities(OCSpider):
    name = "WesternSecurities"

    start_urls_names = {
        "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200270001": "西部证券",
        "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200270002": "西部证券",
        "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200260001": "西部证券",
    }

    api_start_urls = {
        key: {
            "url": "https://www.west95582.com/jdw/public/list.jsp",
            "headers": {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
            },
            "payload": {
                "area": "list",
                "classid": key.split("classid=")[-1].replace("0001", "001").replace("0002", "002"),
                "pageIndex": "1",
                "pageSize": "20",
                "year": "0"
            },
        }
        for key in start_urls_names.keys()
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 10,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 20000
    charset = "iso-8859-1"
    article_data_map = {}

    @property
    def source_type(self) -> str:
        return 'SOE'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        current_page = response.meta.get("pageIndex")

        # Parse article list
        self.extract_articles_with_dates(response)
        article_links = response.xpath("//div[@class='txtmain']//a//@href").getall()
        for link in article_links:
            full_url = response.urljoin(link.strip())
            yield scrapy.Request(
                url=full_url,
                callback=self.parse_article,
                meta={"entry": full_url}
            )

        # Continue pagination
        next_page = self.get_next_page(response)
        if next_page:
            payload["pageIndex"] = next_page
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers=self.api_start_urls[start_url]["headers"],
                formdata=payload,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )

    # def parse_article(self, response):
    #     yield {
    #         "url": response.url,
    #         "title": self.get_title(response),
    #         "date": self.get_date(response),
    #         "body": self.get_body(response),
    #         "authors": self.get_authors(response),
    #         "images": self.get_images(response),
    #         "documents": self.get_document_urls(response)
    #     }

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath("//div[@class='txtmain']//a//@href").getall()]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.meta.get("entry"), {}).get("title")

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@id='contentDiv']//p//text()").getall())

    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath("//div[@id='contentDiv']//img//@src").getall()

    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None):
        pdf = response.xpath("//div[@class='mainlist maintexts']//a[contains(@href,'.jsp')]//@href").getall()
        if pdf:
            return pdf
        return self.article_data_map.get(response.meta.get("entry"), {}).get("pdf")

    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.meta.get("entry"), {}).get("date", "")
        return date.replace("Notice", "").replace("Warehousing", "").replace("Western", "").replace("Announcement", "")

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_next_page(self, response):
        current_page = int(response.meta.get("current_page"))
        next_page = current_page + 1
        return str(next_page) if next_page < 3 else None
    
    def get_page_flag(self) -> bool:
        return False

    def extract_articles_with_dates(self, response):
        mapping = {}
        for article in response.xpath("//div[@class='txtmain']"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//a//text()").get()
            date = article.xpath(".//span[@class='txttime']//text()").get()
            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date = date.strip()
                mapping[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
        self.article_data_map.update(mapping)
