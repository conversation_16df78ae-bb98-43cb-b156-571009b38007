# from scraper.OCSpider import <PERSON><PERSON>pider
# from scraper.utils.helper import body_normalization

# class ParliamentofMimikaRegency(OCSpider):
#     name = "ParliamentofMimikaRegency"
    
#     country = "ID"

#     start_urls_names = {
#         "https://dprd.mimikakab.go.id/contents/berita",
#          "https://dprd.mimikakab.go.id/jdih/ "
#     }
    
#     charset = "utf-8"

#     @property
#     def language(self): 
#         return "Indonesian"

#     @property
#     def source_type(self) -> str:
#         return "ministry"
    
#     @property
#     def timezone(self):
#         return "Asia/Jakarta"
    
#     def get_articles(self, response) -> list:
#         return response.xpath("//h2//a//@href").getall()

#     def get_href(self, entry) -> str:
#         return entry
    
#     def get_title(self, response) -> str:
#         return response.xpath("//div[@class='post-title-wrapper']//h1//text()").get().strip()
        
#     def get_body(self, response) -> str:
#         return body_normalization(response.xpath("//div[@class='entry-content clearfix']//p//text()").getall()) 
        
#     def get_images(self, response) -> list:
#         return response.xpath("//div[@class='entry-content clearfix']//p//img//@src").getall()
    
#     def date_format(self) -> str:
#         return "%d %B %Y"
    
#     def get_date(self, response) -> str:
#         return response.xpath("//div[@class='entry-meta clearfix']//span[@class='date']//text()").get().strip()
    
#     def get_authors(self, response):
#         return response.xpath("//div[@class='entry-meta clearfix']//span[@class='by-author author vcard']//text()").getall()
    
#     def get_page_flag(self) -> bool:
#         return False
     
#     def get_next_page(self, response): 
#         return response.xpath("//li[@class='previous']//a//@href").get()





from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List
import scrapy
from urllib.parse import urljoin

class ParliamentofMimikaRegencyss(OCSpider):
    name = 'ParliamentofMimikaRegencyss'

    country = "ID"  # Indonesia
    HEADLESS_BROWSER_WAIT_TIME = 10000
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scrapy.downloadermiddlewares.redirect.RedirectMiddleware': None,
            'scraper.middlewares.StrictRedirectMiddleware': 600,
        },
        "DOWNLOAD_DELAY": 2,
        "USER_AGENT": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "ROBOTSTXT_OBEY": False,
        "REDIRECT_ENABLED": True,
        "REDIRECT_MAX_TIMES": 2
    }
    
    allowed_domains = ['dprd.mimikakab.go.id']
    start_urls = [
        'https://dprd.mimikakab.go.id/contents/berita',
        'https://dprd.mimikakab.go.id/jdih/'
    ]

    charset = "utf-8"

    def parse(self, response):
        # First check if we're on an article page
        if self.is_article_page(response):
            yield self.scrap_article(response)
        else:
            # Handle pagination and article collection
            yield from self.parse_intermediate(response)

    def is_article_page(self, response):
        # Check if this looks like an article page
        if 'berita' in response.url:
            return response.xpath("//h1[contains(@class, 'text-primary')]").get() is not None
        elif 'jdih' in response.url:
            return response.xpath("//h4[contains(@class, 'card-title')]").get() is not None
        return False

    def parse_intermediate(self, response):
        # Prevent following redirects outside our domain
        if not any(d in response.url for d in self.allowed_domains):
            return

        if 'berita' in response.url:
            # For news section
            articles = response.xpath("//div[contains(@class, 'col-lg-4')]//a/@href").getall()
            next_page = response.xpath("//a[contains(@class, 'page-link') and contains(text(), 'Next')]/@href").get()
        else:
            # For JDIH section
            articles = response.xpath("//div[contains(@class, 'card-body')]//a/@href").getall()
            next_page = response.xpath("//li[contains(@class, 'page-item') and not(contains(@class, 'disabled'))]/a[contains(@class, 'page-link')]/@href").get()

        # Process found articles
        for article_url in articles:
            yield scrapy.Request(
                url=urljoin(response.url, article_url),
                callback=self.parse,
                meta={'section': 'berita' if 'berita' in response.url else 'jdih'}
            )

        # Follow pagination if exists
        if next_page:
            yield scrapy.Request(
                url=urljoin(response.url, next_page),
                callback=self.parse_intermediate,
                dont_filter=True
            )

    @property
    def source_type(self) -> str:
        return 'government'

    @property
    def language(self):
        return "Indonesian"

    @property
    def timezone(self):
        return "Asia/Jakarta"

    def get_title(self, response) -> str:
        title = None
        if response.meta.get('section') == 'berita':
            title = response.xpath("//h1[contains(@class, 'text-primary')]/text()").get()
        else:
            title = response.xpath("//h4[contains(@class, 'card-title')]/text()").get()
        return title or ""  # Return empty string if None

    def get_body(self, response) -> str:
        if response.meta.get('section') == 'berita':
            body_elements = response.xpath("//div[contains(@class, 'blog-details')]//p//text() | //div[contains(@class, 'blog-details')]//ul//li//text()")
        else:
            body_elements = response.xpath("//div[contains(@class, 'card-body')]//p//text() | //div[contains(@class, 'card-body')]//ul//li//text()")
        return body_normalization(body_elements.getall())

    def get_images(self, response) -> list:
        if response.meta.get('section') == 'berita':
            images = response.xpath("//div[contains(@class, 'blog-details')]//img/@src").getall()
        else:
            images = response.xpath("//div[contains(@class, 'card-body')]//img/@src").getall()
        return [urljoin(response.url, img) for img in images if img]

    def date_format(self) -> str:
        return '%d/%m/%Y'

    def get_date(self, response) -> str:
        if response.meta.get('section') == 'berita':
            date = response.xpath("//span[contains(@class, 'text-muted')]/text()").get()
            return date.strip() if date else ""
        return ""

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return None