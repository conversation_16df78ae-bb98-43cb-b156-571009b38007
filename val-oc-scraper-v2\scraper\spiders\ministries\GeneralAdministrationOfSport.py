import re
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

# Use this pattern if you want to use headless browser
# you can copy the custom settings and headless browser wait time

class GeneralAdministrationOfSport(OCSpider):
    name = 'GeneralAdministrationOfSport'

    start_urls_names = {
        "https://www.sport.gov.cn/n323/n10516/index.html": "",
        "https://www.sport.gov.cn/n315/n20001395/index.html": "",
        "https://www.sport.gov.cn/n315/n20067425/index.html": ""
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 10000 # 10 seconds
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES" :
        {
            # for using geo targeted proxy, add this middleware
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY" : 2,
    }


    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return [
            response.urljoin(url)
            for url in
            response.xpath("//ul[@class='newslist']/li/a/@href").getall()
        ]


    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='wztitle']/p/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@id='zoom']//p//text()").getall())

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return response.xpath("//meta[@name='PubDate']/@content").get()

    def get_images(self, response) -> list:
        return [
            response.urljoin(url)
            for url in response.xpath("//div[@id='zoom']//img/@src").getall()
        ]

    def get_authors(self, response):
        return response.xpath("//meta[@name='ContentSource']/@content").get()


    def get_next_page(self, response) -> str:
        
        regex = r"'([^']*)'"
        page_list_url = [
            (re.search(regex, text).group(1))
            for text in response.xpath("//*[@class='pages']//a/@href").getall()
        ]
        
        regex = r'_(\d+)\.html'

        next_page_index = [
            index
            for index, text in enumerate(response.xpath("//*[@class='pages']/tbody/tr/td/*").getall())
            if "font" in text
        ]
        if next_page_index:
            return page_list_url[next_page_index[0]]
        else:
            match = re.search(regex, response.url)
            if match:
                page_no = match.group(1) 
                next_page_no = int(page_no) - 1 # the page no. is in reverse order eg: in the url it starts with 87, 86,...

                if next_page_no < 0: return None # the page index goes to 0
                else:
                    # '../../n323/n10516/index_20166744_86.html' I want to change this 86 to 85
                    return response.url.replace(f"_{page_no}.html", f"_{next_page_no}.html")