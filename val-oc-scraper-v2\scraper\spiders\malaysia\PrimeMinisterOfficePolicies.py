from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class PrimeMinisterOfficePolicies(OCSpider):
    name = "PrimeMinisterOfficePolicies"

    start_urls_names = {
        "https://www.pmo.gov.my/gov-policies/" : "Gov. Policies"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def parse_intermediate(self, response):
        start_url = response.request.meta.get('start_url')
        current_page = response.meta.get('current_page', 1)
        hbp = HeadlessBrowserProxy()
        url = hbp.get_proxy(f"{start_url}?_page={current_page}",timeout=10000)
        yield scrapy.Request(
            url=url,
            callback=self.parse,
            dont_filter=True,
            meta={
                'start_url': start_url,
                'current_page': current_page,
                'url': url
            }
        )

    def get_articles(self, response) -> list:  
        return response.xpath('//h5[@class="pt-cv-title"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="entry-title"]//text()').get()
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="entry-content"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//time//text()').get()

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return response.xpath('//a[contains(text(), "Download")]/@href').getall() or response.xpath('//div[@class="ghostkit-button-wrapper-inner"]//a/@href').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        current_page = response.meta.get('current_page')
        next_page = str(int(current_page)+1)
        if response.status!=200:
            return None
        else :
            return next_page
        
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = "https://www.pmo.gov.my/gov-policies/" 
        next_page = self.get_next_page(response)
        if not next_page:
            return
        full_url = f"{start_url}?_page={next_page}"
        hbp = HeadlessBrowserProxy()
        proxied_url = hbp.get_proxy(full_url, timeout=10000)
        yield scrapy.Request(
            url=proxied_url,
            callback=self.parse_intermediate,
            meta={
                'start_url': start_url,
                'current_page': next_page,
                'proxied_url': proxied_url
            }
        )