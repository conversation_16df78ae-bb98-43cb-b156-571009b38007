from typing import Optional, List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import re

class ShaanxiCoalIndustryCompanyLimited(OCSpider):
    name = "ShaanxiCoalIndustryCompanyLimited"

    start_urls_names = {
        "https://www.shxcoal.com/news/news": "陕西煤业",
        "https://www.shxcoal.com/news/notice": "陕西煤业",
        "https://www.sse.com.cn/assortment/stock/list/info/company/index.shtml?COMPANY_CODE=601225&tabActive=1":"陕西煤业", #No articles
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath('//ul[@class="list-unstyled"]//li//div[@class="news-con"]//h1/a/@href ').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return response.xpath('//div[@class="danpian-h1"]//text()').get().strip()

    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="danpian-con"]//text()').getall())

    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="danpian-con"]//img[contains(@src,".jpg")]//@src | //div[@class="danpian-con"]//img[contains(@src,".jpeg")]//@src | //div[@class="danpian-con"]//img[contains(@src,".png")]//@src').getall()

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        date_text = response.xpath('//div[@class="danpian-h2"]/text()').get()
        if not date_text:
            return None
        match = re.search(r'\d{4}-\d{2}-\d{2}', date_text)
        return match.group() if match else None

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False  

    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//li[@class="page-item"]/a[@rel="next"]/@href').get()
        if next_page:
            return next_page
        else:
            return None