import calendar
import time
import scrapy

class ArticleItem(scrapy.Item):
    id = scrapy.Field()
    start_url = scrapy.Field()
    source = scrapy.Field()
    source_name = scrapy.Field()
    source_type = scrapy.Field()
    url = scrapy.Field()
    title = scrapy.Field()
    body = scrapy.Field()
    images = scrapy.Field()
    authors = scrapy.Field()
    raw_html = scrapy.Field()
    date = scrapy.Field()
    error = scrapy.Field()
    oc = scrapy.Field()
    country = scrapy.Field()
    news_line = scrapy.Field()
    oc_nlp_engine = scrapy.Field()
    start_url = scrapy.Field()
    language = scrapy.Field()
    meta = scrapy.Field()
    subhead = scrapy.Field()
    document_urls = scrapy.Field()
   
    def __repr__(self):
        authors = self.get('authors', [])
        if len(authors) > 0:
            authors = [{"name" : author} for author in authors]

        images = self.get('images', [])
        if len(images) > 0:
            images = [{"url": image, "caption": ""} for image in images]

        return repr({
            u"id": self.get('id'),
            u"source": self.get('source_name'),
            u"title": self.get('title'),
            u"body": self.get('body'),
            u"images": images,
            u"authors": authors,
            u"date": self.get('date'),
            u"start_url": self.get('start_url'),
            u"inserted": calendar.timegm(time.gmtime()),
            u"meta" : self.get("meta", None),
            u"article_url" : self.get('url'),
            u"newspaper": self.get('source'),
            u"country": self.get('country', 'China'),
            u"news_line": self.get('source_type'),
            u"language": self.get("language", "Chinese"),
            u"subhead": self.get("subhead", None),
            u"document_urls": self.get("document_urls", [])
        })

    
class OfficialLineItem(ArticleItem):

    article_number = scrapy.Field()
    article_url = scrapy.Field()
    start_url = scrapy.Field()
    images = scrapy.Field()
    body_html = scrapy.Field()
    author = scrapy.Field()
    newspaper = scrapy.Field()
    meta = scrapy.Field()
    country = scrapy.Field()
    news_line = scrapy.Field()
    original_link = scrapy.Field()
    
    def __repr__(self):
        """only print out attr1 after exiting the Pipeline"""
        return repr({ 
            u"id": self.get('id'),
            u"title": self.get('title'),
            u"body": self.get('body'),
            u"images": self.get('images'),
            u"authors": self.get('authors'),
            u"date": self.get('date'),
            u"inserted": calendar.timegm(time.gmtime()),
            u"meta" : self.get('meta'),
            u"article_url" : self.get('article_url'),
            u"newspaper": self.get('newspaper'),
            u"country": self.get('country', "China"),
            u"news_line": self.get('news_line'),
            u"start_url": self.get('start_url'),
            u"language": self.get("language", "Chinese"),
            u"subhead": self.get("subhead", None)
        })
    
    
    