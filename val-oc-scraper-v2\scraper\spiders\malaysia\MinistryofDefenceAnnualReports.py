
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class MinistryofDefenceAnnualReports(OCSpider):
    name = "MinistryofDefenceAnnualReports"

    start_urls_names = {
        "https://www.mod.gov.my/index.php/en/others/qlink-laporan-tahunan": "News"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=30000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:
        articles= []
        for article in response.xpath('//div[contains(@class, "uk-card-hover")]'):
           link = article.xpath(".//a[contains(@class, 'uk-position-cover')]/@href").get()
           title = article.xpath(".//a[contains(@class, 'uk-position-cover')]/@title").get()
           if ".pdf" in link:
               articles.append(link)
               self.article_data_map[link]={"title":title,"link":link} 
        return (list(set(articles)))
    
    def get_href(self, entry) -> str:
        return f'https://www.mod.gov.my{entry}'
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
         return ""
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        get_date =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        date=get_date.split(" ")[1]
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 
