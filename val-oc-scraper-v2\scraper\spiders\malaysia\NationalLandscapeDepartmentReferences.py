from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class NationalLandscapeDepartmentReferences(OCSpider):
    name = "NationalLandscapeDepartmentReferences"
    
    start_urls_names = {
        "https://www.jln.gov.my/index.php/pages/view/522": "News"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"    
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='editable']//table//tbody//tr//td//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        list = response.xpath("//div[@class='row']//div[@class='col-md-8']//span[@id='code']//text()").getall()
        return list[1].strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='col-md-6']//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> str:
        list = response.xpath("//div[@class='row']//div[@class='col-md-8']//span[@id='code']//text()").getall()
        date =list[5].replace(" ","").strip() 
        try:
            datetime.strptime(date, "%d/%m/%Y")
            return date
        except ValueError:
            return list[4].strip()

    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath("//div[@id='preview-document']//embed//@src").getall()
    
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        # No next page to scrape
        return None