from typing import Optional
from scraper.OCSpider import OCSpider

class DepartmentOfAgriculturePressReleases(OCSpider):
    name = "DepartmentOfAgriculturePressReleases"

    start_urls_names = {
        "https://www.doa.gov.my/index.php/pages/view/1023?mid=407": "Press Releases"  # Pagination is not supported
    }
    
    start_urls_with_no_pagination_set = {
        "https://www.doa.gov.my/index.php/pages/view/1023?mid=407"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//table//tbody//tr[@height='55']"):
                url = article.xpath(".//a//@href").get()
                title = article.xpath(".//a//text()").get()
                date = article.xpath(".//td[@align='center'][2]//text()[1]").get()
                if url and title and date:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    self.article_data_map[full_url]={"title": title, "date": date, "full_url": url}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d %b %Y"
    
    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        month_map = {
        'Januari': 'Jan', 'Februari': 'Feb', 'Mac': 'Mar', 'April': 'Apr',
        'Mei': 'May', 'Jun': 'Jun', 'Julai': 'Jul', 'Ogos': 'Aug',
        'September': 'Sep', 'Oktober': 'Oct', 'November': 'Nov', 'Disember': 'Dec'
        }
        for malay, eng in month_map.items():
            if malay in date:
                date = date.replace(malay, eng)
                break
        return date
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None