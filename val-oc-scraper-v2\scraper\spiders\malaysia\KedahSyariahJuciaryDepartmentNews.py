from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
from datetime import datetime
import re
import scrapy

class KedahSyariahJuciaryDepartmentNews(OCSpider):
    name = "KedahSyariahJuciaryDepartmentNews"

    start_urls_names = {
        'https://syariah.kedah.gov.my/ms/Page?type=ZwETIGYxo0w=&pid=R2HILGbZA1o=': 'speeches'
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    # custom_settings = { 
	# 	"DOWNLOADER_MIDDLEWARES" : {
	# 		'scraper.middlewares.HeadlessBrowserProxy': 350,
	# 	},
	# 	"DOWNLOAD_DELAY" : 2,
    #     "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
	# }

    # HEADLESS_BROWSER_WAIT_TIME = 10000

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    malay_days = {
        "Isnin": "Monday",
        "Selasa": "Tuesday",
        "Rabu": "Wednesday",
        "Khamis": "Thursday",
        "Jumaat": "Friday",
        "Sabtu": "Saturday",
        "Ahad": "Sunday"
    }

    malay_months = {
        "Januari": "January",
        "Februari": "February",
        "Mac": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }

    def parse_intermediate(self,response):
        start_url = response.meta.get('start_url')
        current_year = response.meta.get('current_year',datetime.now().year)
        url = f"{start_url}&ye={current_year}"
        yield scrapy.Request(
            url,
            callback = self.parse,
            meta = {
                "start_url": start_url,
                "current_year": current_year
            }
        )

    def get_articles(self, response) -> list:
        articles = []
        map = {}
        elements = response.xpath('//li[@class="item"]')
        for element in elements:
            full_url = response.urljoin(element.xpath('.//a/@href').get())
            title = element.xpath('.//div[@class="title FontColor"]/text()').get()
            date = element.xpath('.//div[@class="date FontColor"]/text()').get()
            if full_url and date and title:
                articles.append(full_url)
                map[full_url]={'title':title,'date':date}
        self.article_to_date_map.update(map)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_to_date_map.get(response.url).get('title')

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content text"]//p//text()').getall())

    def get_date(self, response) -> str:
        raw_date = self.article_to_date_map.get(response.url).get('date')
        for malay_day, eng_day in self.malay_days.items():
            if malay_day in raw_date:
                raw_date = raw_date.replace(malay_day, eng_day)
                break
        for malay_month, eng_month in self.malay_months.items():
            if malay_month in raw_date:
                raw_date = raw_date.replace(malay_month, eng_month)
                break
        return raw_date


    def date_format(self) -> str:
        return "%A, %d %B %Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="content text"]//img/@src').getall()

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return []

    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response):
        next_year = str(int(response.meta.get('current_year'))-1)
        if response.status !=200:
            return 
        return next_year
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get('start_url')
        next_year = self.get_next_page(response)
        if next_year :
            url = f"{start_url}&ye={next_year}"
            yield scrapy.Request(
            url,
            callback = self.parse,
            meta = {
                "start_url": start_url,
                "current_year": next_year
            }
        )
        
