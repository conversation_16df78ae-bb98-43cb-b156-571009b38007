# from typing import Optional
# from scraper.OCSpider import OCSpider

# class AccountantGeneralsDepartmentCircular(OCSpider):
#     name = "AccountantGeneralsDepartmentCircular"

#     start_urls_names = {
#         "https://www.anm.gov.my/pekeliling/surat-pekeliling-akauntan-negara-malaysia": "Circular"
#     }

#     charset = "iso-8859-1"
#     country = "Malaysia"

#     @property
#     def language(self):
#         return "Bahasa Malaysia"

#     @property
#     def source_type(self) -> str:
#         return "official_line"

#     @property
#     def timezone(self):
#         return "Asia/Kuala_Lumpur"

#     article_data_map = {}

#     def get_articles(self, response) -> list:
#         articles = []
#         rows = response.xpath("//div[@class='moduletable ']//div[@class='ari-data-tables jui-smoothness']//table[@class='tablesorter ts-init']")
#         for row in rows:
#             url = row.xpath(".//tr//a[contains(@href, '.pdf')]/@href").get()
#             title = row.xpath(".//td[@class='ari-tbl-col-1']//text()").get()
#             date = row.xpath(".//td[@class='ari-tbl-col-0  col_32243497']//text()").get()
#             print(url,title,date)
#             if url and title and date:
#                 full_url = response.urljoin(url)
#                 clean_title = (title or "").replace(date or "", "").strip()
#                 self.article_data_map[full_url] = {
#                     "title": clean_title,
#                     "date": date.strip() if date else ""
#                 }
#                 articles.append(full_url)
#         return articles

#     def get_href(self, entry) -> str:
#         return entry

#     def get_title(self, response) -> str:
#         return self.article_data_map.get(response.request.meta.get("entry"), {}).get("title", "")

#     def get_body(self, response) -> str:
#         return ""

#     def get_images(self, response) -> list:
#         return []

#     def date_format(self) -> str:
#         return "%Y"

#     def get_date(self, response):
#         return self.article_data_map.get(response.request.meta.get("entry"), {}).get("date", "")

#     def get_document_urls(self, response, entry=None) -> list:
#         return [response.url]

#     def get_authors(self, response):
#         return []

#     def get_page_flag(self) -> bool:
#         return False

#     def get_next_page(self, response) -> Optional[str]:
#         return None







from typing import Optional
from urllib.parse import unquote
import scrapy
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy

class AccountantGeneralsDepartmentCircular(OCSpider):
    name = "AccountantGeneralsDepartmentCircular"

    start_urls_names = {
        "https://www.anm.gov.my/pekeliling/surat-pekeliling-akauntan-negara-malaysia": "Circular"
    }

    charset = "iso-8859-1"
    country = "Malaysia"

    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request
    
    # def get_articles(self, response) -> list: 
    #     mapping = {}
    #     articles = [] 
    #     containers = response.xpath("//div[@class='moduletable ']//div[@class='ari-data-tables jui-smoothness']//table[@class='tablesorter ts-init']")
    #     for box in containers:
    #         url = box.xpath(".//tr//a[contains(@href, '.pdf')]/@href").get()
    #         title = box.xpath('.//td[@class="ari-tbl-col-1"]//text()').get()
    #         date = box.xpath(".//td[@class='ari-tbl-col-0  col_32243497']//text()").get()
    #         print(url,title,date)
    #         if url and title and date:
    #             articles.append(url)
    #             self.article_data_map[url] = {
    #                     'title': title,
    #                     'pdf': url,    
    #                     'date': date
    #                 }
    #     print(articles,len(articles))
    #     return articles

    def get_articles(self, response) -> list: 
        mapping = {}
        articles = [] 
        containers = response.xpath("//div[@class='moduletable ']//div[@class='ari-data-tables jui-smoothness']//table[@class='tablesorter ts-init']")
        
        print(f"[DEBUG] Total tables found: {len(containers)}")

        for container_index, box in enumerate(containers):
            rows = box.xpath(".//tr[position()>1]")  # skip header
            print(f"[DEBUG] Table {container_index+1}: Found {len(rows)} rows")

            for row_index, row in enumerate(rows):
                url = row.xpath(".//a[contains(@href, '.pdf')]/@href").get()
                title = row.xpath('.//td[@class="ari-tbl-col-1"]//text()').get()
                date = row.xpath(".//td[@class='ari-tbl-col-0  col_32243497']//text()").get()

                if not (url and title and date):
                    print(f"[SKIPPED] Table {container_index+1} Row {row_index+1}: Missing fields - URL: {url}, Title: {title}, Date: {date}")
                    continue

                print(f"[ADDED] Table {container_index+1} Row {row_index+1}: URL: {url}, Title: {title}, Date: {date}")
                articles.append(url)
                self.article_data_map[url] = {
                    'title': title.strip(),
                    'pdf': url.strip(),
                    'date': date.strip()
                }

        print(f"[SUMMARY] Total articles added: {len(articles)}")
        print(f"[SUMMARY] URLs: {articles}")
        return articles


    def get_href(self, entry) -> str:
        return f"https://www.anm.gov.my{entry}"

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get("entry"), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        date=self.article_data_map.get(response.request.meta.get("entry"), {}).get("date", "")
        date = date.split("/")[-1]
        return date
        
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return None