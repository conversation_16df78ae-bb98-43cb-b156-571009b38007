from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class TennesseeDepartmentOfLaborAndWorkforceDevelopment(OCSpider):
    name = "TennesseeDepartmentOfLaborAndWorkforceDevelopment"

    country = "US"

    start_urls_names = {
        "https://www.tn.gov/workforce/general-resources/news.html": "Newsroom",
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath('//h2/a[starts-with(@href, "/workforce/general-resources/news/")]/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[contains(@class, "tn-pagetitle")]//h1/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[contains(@class, "textimage-text")]//p//text()').getall())
 
    def get_images(self, response) -> list:
        return response.xpath('//div[contains(@class, "textimage-top")]//img/@src').getall()

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        raw_date = (response.xpath('normalize-space(//div[@class="date"]/text())').get() or "").strip()
        date_str = raw_date.split("|")[0].strip()
        date_obj = datetime.strptime(date_str, "%A, %B %d, %Y")
        return date_obj.strftime(self.date_format())
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//ul[@class="inline-list pager"]//li/a[@aria-label="Next Page"]/@href').get()
        return response.urljoin(next_page) if next_page else None