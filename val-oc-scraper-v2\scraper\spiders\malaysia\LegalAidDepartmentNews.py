from scraper.OCSpider import OCSpider
from scraper.utils.helper  import body_normalization

class LegalAidDepartmentNews(OCSpider):
    name = "LegalAidDepartmentNews"

    start_urls_names = {
        'https://www.jbg.gov.my/index.php/ms-my/semua-aktiviti' : 'Annual Reports',
        'https://www.jbg.gov.my/index.php/ms-my/icons-2/91-berita-2024' : 'Annual Reports',
        'https://www.jbg.gov.my/index.php/ms-my/icons-2/87-berita-2023' : 'Annual Reports',
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    malay_to_english_month = {
        "Januari": "January",
        "Februari": "February",
        "Mac": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }

    def get_articles(self, response) -> list:
       start_url = response.meta.get('start_url')
       if "semua-aktiviti" in start_url:
           return response.xpath('//table[@class="category table table-bordered"]//tr//a/@href').getall()
       else:
           return list(set(response.xpath('//li[@class="list-group-item"]//a/@href').getall()))

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@itemprop="headline"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@dir="auto"]//p//text()').getall() or response.xpath('//div[@itemprop="articleBody"]//text()').getall())
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        date_text = (response.xpath('//div[@dir="auto"]//p//text()').re_first(r"\b\d{1,2}\s+\w+\s+\d{4}\b") or response.xpath('//div[@itemprop="articleBody"]//text()').re_first(r"\b\d{1,2}\s+\w+\s+\d{4}\b"))
        for malay,eng in self.malay_to_english_month.items():
           if malay in date_text:
               date = date_text.replace(malay,eng)
               break
        return date

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None)->list:
        return []

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('(//a[@aria-label="Go to sebelumnya page"]/@href)[last()]').get()