from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MinistryofNaturalResourcesEnvironmentSustainabilityNewsArchive(OCSpider):
    name = "MinistryofNaturalResourcesEnvironmentSustainabilityNewsArchive"

    start_urls_names = {
        'https://www.nres.gov.my/ms-my/pustakamedia/Pages/ArkibBerita.aspx': 'ministry'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.nres.gov.my/ms-my/pustakamedia/Pages/ArkibBerita.aspx'
    }
    
    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        hrefs = response.xpath('//ul[@class="dfwp-column dfwp-list"]//li//a/@href').getall()
        for i in range(len(hrefs)):
            cleaned_href = "".join(hrefs[i].split())
            hrefs[i] = response.urljoin(cleaned_href)
        return hrefs

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//th/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//table//tr//text()').getall()[2:])
       
    def get_date(self, response) -> str:
        return response.xpath('//table//tr[2]/th/text()').get()

    def date_format(self) -> str:
        return "%d %B %Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//table//tr//img/@src').getall()
    
    def get_document_urls(self, response, entry=None) -> list:
        return []

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None