from scraper.OCSpider import OCSpider
import scrapy
import re
from scraper.middlewares import HeadlessBrowserProxy

class PerakStateGovernmentChiefMinistersSpeeches(OCSpider):
    name = "PerakStateGovernmentChiefMinistersSpeeches"
    
    start_urls_names = {
        "https://www.perak.gov.my/index.php/kerajaan-negeri/info-umum/koleksi-ucapan-yab-mb-perak": "News"
    }
    
    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"
        self.article_data_map = {}

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        articles = []
        print(response.xpath("//div[@class='uk-margin']//tbody//tr//td//a//@href").getall())
        for article in response.xpath("//div[@class='uk-margin']//tbody//tr//td"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//a//text()").get()
            print(url,title)
            if title and url:
                self.article_data_map[url] = {
                    "title": title,
                }
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return f"https://www.perak.gov.my/{entry}"
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"  
    
    def get_date(self, response):
        year_match = re.search(r'(?:^|\D)(\d{4})(?:\D|$)',  response.url)
        if year_match:
            year = year_match.group(1)
            if 1900 <= int(year) <= 2100:
                return year
        return "2020"
    
    def get_document_urls(self, response, entry=None) -> list:
        if '.pdf' in response.url:
            return [response.url]
        return []
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response):
        # No next page to scrape
        return None