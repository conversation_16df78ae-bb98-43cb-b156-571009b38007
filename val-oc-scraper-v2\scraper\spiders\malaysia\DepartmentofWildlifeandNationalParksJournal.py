from scraper.OCSpider import OCSpider
import re

class DepartmentOfWildlifeAndNationalParksJournal(OCSpider):
    name = "DepartmentOfWildlifeAndNationalParksJournal"

    start_urls_names = {
        'https://www.wildlife.gov.my/index.php/penerbitan/103-jurnal': 'wildlife'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.wildlife.gov.my/index.php/penerbitan/103-jurnal'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    articles_url_date_mapping = {}

    def get_articles(self, response) -> list:
        articles_list = []
        articles = response.xpath('//span[@style="font-family: arial, helvetica, sans-serif; font-size: 10pt;"]//a')
        for article in articles:
            href = article.xpath('./@href').get()
            full_url = response.urljoin(href)
            title = article.xpath('./text()').get()
            match = re.search(r'\b\d{4}\b', title)
            if match and href:
                self.articles_url_date_mapping[full_url] = [title , match.group(0)]
                articles_list.append(full_url)
        return articles_list

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.articles_url_date_mapping.get((response.request.url),[])[0]

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        return self.articles_url_date_mapping.get((response.request.url),[])[1]

    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.url
    
    def get_next_page(self, response):
        return None