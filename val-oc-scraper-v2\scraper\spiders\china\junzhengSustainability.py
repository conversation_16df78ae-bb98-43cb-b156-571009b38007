from typing import Optional, List
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy
import urllib.parse
from bs4 import BeautifulSoup


class junzhengSustainability(OCSpider):
    name = "junzhengSustainability"

    start_urls_names = {
        "https://www.junzhenggroup.com/investor/info": "Disclosure of Information",
        # "https://www.junzhenggroup.com/news/index": "News Center",
    }

    api_start_urls = {
        "https://www.junzhenggroup.com/news/index": [
            {
                "url": "https://www.junzhenggroup.com/news/ajax_get_news_for_page",
                "payload": {
                    "page": "1",
                    "tab": "1"
                }
            }
        ],
        "https://www.junzhenggroup.com/investor/info": [
            {
                "url": "https://www.junzhenggroup.com/investor/ajax_get_notice_for_page",
                "payload": {
                    "page": "1",
                    "jzjt_notice_category_id": "1"
                }
            }
        ]
    }

    charset = "utf-8"

    # custom_settings = {
    #     'RETRY_TIMES': 5,
    #     'RETRY_HTTP_CODES': [500, 502, 503, 504, 522, 524, 408],
    #     'DOWNLOADER_MIDDLEWARES': {
    #         'scrapy.downloadermiddlewares.retry.RetryMiddleware': 90,
    #         'scrapy.downloadermiddlewares.httpproxy.HttpProxyMiddleware': 110,
    #         'scraper.middlewares.GeoProxyMiddleware': 350,
    #     },
    #     'DOWNLOAD_DELAY': 2,
    # }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
        "Content-type": "application/x-www-form-urlencoded; charset=UTF-8"
    }

    article_to_pdf_urls_mapping = {}

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    # @property
    # def charset(self):
    #     start_url = getattr(self, "current_start_url", "")
    #     if "junzhenggroup.com/investor/info" in start_url:
    #         return "iso-8859-1"
    #     return "utf-8"

    def parse_intermediate(self, response):
        
        start_url = response.meta.get("start_url")

        # if "junzhenggroup.com/investor/info" in start_url:
        #     self.__class__.charset = "iso-8859-1"
        # else:
        #     self.__class__.charset = "utf-8"

        api_data_list = self.api_start_urls.get(start_url)
        # if not api_data_list:
        #     return

        for api_data in api_data_list:
            payload = api_data["payload"].copy()
            api_url = api_data["url"]
            full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
            yield scrapy.Request(
                url=full_api_url,
                method="POST",
                headers=self.headers,
                # callback=self.handle_entries,  # use custom handler
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": int(payload.get("page", 1)),
                }
            )

    # def handle_entries(self, response):
    #     start_url = response.meta.get("start_url")
    #     entries = self.get_articles(response)

    #     for entry in entries:
    #         if entry.lower().endswith(".pdf"):
    #             yield {
    #                 "url": entry,
    #                 "type": "PDF",
    #                 "source": self.name,
    #                 "skipped_in_parse_article": True,
    #             }
    #             continue

    #         yield scrapy.Request(
    #             url=entry,
    #             headers=self.headers,
    #             meta={
    #                 "start_url": start_url,
    #                 "entry": entry
    #             },
    #         )

    #     yield from self.go_to_next_page(response, start_url, response.meta.get("current_page"))

    def get_articles(self, response) -> list:
        print("*********", response.text)
        start_url = response.meta.get("start_url", "")
        self.article_to_pdf_urls_mapping = {}
        entries = []

        if "news/index" in start_url:
            articles = response.xpath('//ul[@id="table0"]//li/a/@href').getall()
            # for rel_url in articles:
            #     full_url = response.urljoin(rel_url)
            #     entries.append(full_url)
            # return entries

        elif "investor/info" in start_url:
            blocks = response.xpath('//ul[@id="table0"]/li')
            for li in blocks:
                title = li.xpath('.//p/text()').get(default="").strip()
                print("Title&&&&&&", title)
                date = li.xpath('.//div[@class="time"]/text()').get(default="").strip()
                pdf_href = li.xpath('.//div[@class="btn-box"]/a[contains(@href, ".pdf")]/@href').get(default="").strip()
                if not pdf_href:
                    continue
                full_url = response.urljoin(pdf_href)
                print("PDF%%%%", full_url)
                entries.append(full_url)
                self.article_to_pdf_urls_mapping[full_url] = {
                    "title": title,
                    "date": date,
                    "pdf_url": full_url,
                }
        return entries

    def get_entry_metadata(self, url: str) -> dict:
        base_url = url.split("?")[0]
        return self.article_to_pdf_urls_mapping.get(base_url, {})

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        start_url = response.meta.get("start_url", "")
        if "news/index" in start_url:
            return response.xpath('//div[@class="title"]/h1/text()').get(default="").strip()
        elif entry:
            return self.article_to_pdf_urls_mapping.get(entry, {}).get("title", "")
        return ""

    def get_body(self, response, entry=None) -> str:
        if "news/index" in response.meta.get("start_url", ""):
            return body_normalization(response.xpath('//div[@class="content"]//p//text()').getall())
        return ""

    def get_images(self, response, entry=None) -> List[str]:
        return []

    def get_date(self, response, entry=None) -> str:
        start_url = response.meta.get("start_url", "")
        if "news/index" in start_url:
            raw = response.xpath('//div[@class="title"]/p/text()').get()
            if raw:
                return raw.replace("发布时间：", "").strip()
        if not entry:
            entry = response.url

        metadata_date = self.get_entry_metadata(entry).get("date")
        if metadata_date:
            return metadata_date

        extracted_map = self.extract_date_pdf_map(response.text)
        for date, link in extracted_map:
            full_link = response.urljoin(link)
            if full_link == entry:
                return date

        return ""

    def extract_date_pdf_map(self, html: str):
        soup = BeautifulSoup(html, 'html.parser')
        items = soup.select("ul#table0 > li")
        date_pdf_map = []
        for item in items:
            date_tag = item.select_one("div.time")
            link_tag = item.select_one("div.btn-box a[download]")
            if date_tag and link_tag:
                date = date_tag.get_text(strip=True)
                pdf_link = link_tag["href"]
                date_pdf_map.append((date, pdf_link))
        return date_pdf_map

    def get_authors(self, response, entry=None) -> List[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return current_page + 1 if current_page < 70 else None

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        if not self.get_page_flag():
            return  # ⛔ STOP pagination if flag is False

        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)

        if not next_page:
            return

        payload["page"] = str(next_page)
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"

        yield scrapy.Request(
            url=full_api_url,
            method="POST",
            headers=self.headers,
            # callback=self.handle_entries,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            }
        )