from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class NationalDefenseSecurityandForeignAffairsCommitteeofVietnam(OCSpider):
    name = "NationalDefenseSecurityandForeignAffairsCommitteeofVietnam"

    start_urls_names = {
        "https://quochoi.vn/uybanquocphongvaanninh/tintuc/Pages/tin-hoat-dong.aspx": "News",
        }
    
    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Vietnam"

    custom_settings = {
          "DOWNLOADER_MIDDLEWARES": {
              'scraper.middlewares.HeadlessBrowserProxy': 500
          },
          "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
      }
    
    HEADLESS_BROWSER_WAIT_TIME = 2000

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//div/p[1]/a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detail"]//p[@style="text-align:justify;"]').getall())
   
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="detail"]//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//p[@class="date"]//span//text()').get().strip()
        date_obj = dateparser.parse(date, languages=["vi"])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[normalize-space(text())="Sau"]/@href').get()