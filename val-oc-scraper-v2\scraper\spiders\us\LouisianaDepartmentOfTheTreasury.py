from scraper.OCSpider import <PERSON>CSpider
from typing import List, Union
import scrapy
from datetime import datetime

class LouisianaDepartmentOfTheTreasury(OCSpider):
    name = 'LouisianaDepartmentOfTheTreasury'

    country = "US"
    
    start_urls_names = {
        'https://www.treasury.la.gov/2017-2018-press-releases': 'Press',
        'https://www.treasury.la.gov/2019-2021-press-releases': 'Press',
        'https://www.treasury.la.gov/2022-2024-press-releases': 'Press',
    }
    
    charset = 'iso-8859-1'
    
    article_data_map = {}
    
    def parse_intermediate(self, response):
        articles = set(response.xpath("//div[@class='FVGvCX']//p//span//a//@href").getall())
        total_articles = len(articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url")
        for start_idx in range(0, total_articles, articles_per_page):  # Indexing for virtual pagination to extract more than 100 articles from single page
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={'start_idx': start_idx, 'start_url': start_url},
                    dont_filter=True
            )
    
    @property
    def language(self):
        return "English"
    
    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Eastern"
     
    def get_articles(self, response) -> list:
        try:
            articles_with_duplicate = response.xpath("//div[@class='FVGvCX']//p")
            all_articles = set()
            for article in articles_with_duplicate:
                full_url = article.xpath(".//span//a/@href").get()
                title = article.xpath(".//span[@style='text-decoration:underline;']//text()").get()
                date = article.xpath(".//span[@style='font-family:times new roman,times,serif;']//text()").get()
                if full_url:
                    if not title:
                        title = response.xpath("//title/text()").get(default="").strip()
                    if not date:
                        date = response.xpath("//meta[@name='date']/@content").get(default="").strip()
                    date = date.strip().replace("\xa0", " ")
                    self.article_data_map[full_url] = {  # Mapping done for indexing articles and PDF's from start URL
                        "title": title.strip(),
                        "date": date,
                        "pdf": [full_url]
                    }
                    all_articles.add(full_url)
            all_articles = list(all_articles)
            start_idx = response.meta.get('start_idx', 0)  # Indexing should be called from parse_intermediate only
            end_idx = start_idx + 100
            return all_articles[start_idx:end_idx]   # Article url's are extracted and returned
        except Exception as e:
            return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrap
        return ""
    
    def get_images(self, response) -> List[str]:
        # Only PDF's are there to scrap
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        date_str = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "").strip()
        if "Sept " in date_str:
            date_str = date_str.replace("Sept", "Sep")
            date_obj = datetime.strptime(date_str, "%b %d, %Y")
        if not date_str:
            return ""
        for fmt in ("%B %d, %Y", "%b %d, %Y", "%b %d,%Y"):
            try:
                date_obj = datetime.strptime(date_str, fmt)
                return date_obj.strftime("%B %d, %Y")
            except ValueError:
                continue
        return ""
    
    def get_authors(self, response):
        # Only PDF's are there to scrap
        return []
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", [])
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Union[None, List[str]]:
        return None