from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ArizonaDepartmentOfTransportation(OCSpider):
    name = "ArizonaDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://azdot.gov/adot-news": "News",
    }

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # Using geoproxy
                'scraper.middlewares.GeoProxyMiddleware': 500,
            },
        "DOWNLOAD_DELAY": 6,
    }

    charset = "utf-8"
    
    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Phoenix"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="view-content"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="field field--name-title field--type-string field--label-hidden"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="clearfix text-formatted field field--name-body field--type-text-with-summary field--label-hidden field__item"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return [] 

    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = (response.xpath('//span[@class="field field--name-created field--type-created field--label-inline"]/text()').get()).strip()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//a[@class="button"]/@href').get()
        if not next_page:
           return None
        else:
            return next_page