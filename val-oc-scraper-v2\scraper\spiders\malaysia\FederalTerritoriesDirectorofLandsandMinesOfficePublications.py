from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
from scraper.middlewares import GeoProxyMiddleware
import scrapy
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin, unquote
import re

class FederalTerritoriesDirectorofLandsandMinesOfficePublications(OCSpider):
    name = "FederalTerritoriesDirectorofLandsandMinesOfficePublications"

    start_urls_names = {
        # "https://www.ptgwp.gov.my/portal/web/guest/penerbitan" : "Tahunan PPTG WP",
        #Child url contains a starting page which then have article urls 
        ## No way for handling pagination 
        # These cover all available years manually (no dynamic discovery or "Next" button possible)
        "https://www.ptgwp.gov.my/portal/521" : "Makluman",
        # "https://www.ptgwp.gov.my/portal/498" : "Makluman",
        # "https://www.ptgwp.gov.my/portal/443" : "Maklum<PERSON>",
        # "https://www.ptgwp.gov.my/portal/386" : "Makluman",
        # "https://www.ptgwp.gov.my/portal/320" : "Makluman",
        # "https://www.ptgwp.gov.my/portal/236" : "Makluman",
        # "https://www.ptgwp.gov.my/portal/190" : "Makluman",
        # "https://www.ptgwp.gov.my/portal/94" : "Makluman", 
    }

    charset = "iso-8859-1"

    proxy_country = "my"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # for using geo targeted proxy, add this middleware
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_to_pdf_mapping = {}

    article_to_date_mapping = {}

    # def parse_intermediate(self, response):
    #     start_url = response.meta.get('start_url')
    #     if start_url == "https://www.ptgwp.gov.my/portal/web/guest/penerbitan":
    #         hbp = HeadlessBrowserProxy()
    #         request = scrapy.Request(hbp.get_proxy(response.url, timeout=30000), callback=self.parse
    #         )
    #         request.meta['start_url'] = response.request.meta['start_url']
    #         yield request

    def get_articles(self, response) -> list: 
        start_url = response.meta.get("start_url")
        press_release_data = {}
        mapping = {}
        base_url = "https://www.ptgwp.gov.my/"
        articles = [] 
        if "guest/penerbitan" in start_url:
            containers = response.xpath('//td//a[@splwpk-publication="splwpk-publication"]')
            for box in containers:
                link = box.xpath('./@href').get()
                full_url = urljoin(base_url, link)
                title = box.xpath('./text()').get()
                date = box.xpath('./text()').re_first(r'\d{4}')
                if link and '.pdf' not in link.lower():
                    print(f"[NOT PDF] {full_url}")
                    continue
                if link and title and date:
                    normalized_link = unquote(full_url).rstrip('/')
                    articles.append(normalized_link)
                    press_release_data[normalized_link] = {
                        'title': title,
                        'pdf': normalized_link,    
                        'date': date
                    }
        else:
            tr_nodes = response.xpath('//div[@class="content-item"]//tr')
            for i in range(0, len(tr_nodes) - 1, 3):
                # Get all <a> tags from current <tr>
                links = tr_nodes[i].xpath('.//a')
                title = None
                raw_date = None
                article_url = None
                for a in links:
                    href = a.xpath('./@href').get()
                    text = a.xpath('normalize-space(string())').get()

                    if href and href.startswith("/portal/") and not "/web/guest/-" in href:
                        article_url = urljoin(base_url, href)
                        title = text
                        break  # use only the first valid one
                # Now get the date from next row
                # Look ahead for date: search next 1–2 rows
                for j in range(i + 1, min(i + 4, len(tr_nodes))):
                    combined_text = ' '.join(tr_nodes[j].xpath('.//text()').getall()).strip()
                    match = re.search(r'Tarikh\s+Kemaskini:\s*(.+)', combined_text)
                    if match:
                        raw_date = match.group(1).strip()
                        raw_date = re.sub(r'\s+', ' ', raw_date).strip()
                        break
                print(raw_date,"raw date is raw_date")
                if article_url:
                    normalized_url = unquote(article_url).rstrip('/')
                    articles.append(normalized_url)
                    mapping[normalized_url] = {
                        'title': title,
                        'date': raw_date
                    }
        print(f"[INFO] Extracted {len(articles)} valid articles.")
        print(mapping)
        self.article_to_pdf_mapping.update(press_release_data)
        self.article_to_date_mapping.update(mapping)
        print(articles)
        return articles
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        is_html = 'text/html' in response.headers.get('Content-Type', b'').decode()
        if not is_html:
            return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")
        return self.article_to_date_mapping.get(normalized_url, {}).get("title", "")

    
    def get_body(self, response) -> str:
        if 'text/html' not in response.headers.get('Content-Type', b'').decode():
            return ''
        else :
            body = body_normalization(response.xpath('//div[@id="printableArea"]//p//text()').getall())
            print(body)

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        article_url = response.url
        article_date = self.article_to_date_mapping.get(article_url, None)
        if article_date:
            return article_date
        else:
            self.logger.error(f"No date found for URL: {article_url}")
            return None
    
        # start_url = response.meta.get("start_url")
        # print(response.url)
        # print("\n=== self.article_to_date_mapping ===")
        # for key, value in self.article_to_date_mapping.items():
        #     print(f"{key} -> {value}")
        # print("=== end of mapping ===\n")
        # # print("response url",response.url)
        # # content_type = response.headers.get('Content-Type', b'').decode()
        # # normalized_url = unquote(response.url).rstrip('/')
        # print(self.article_to_pdf_mapping.get(response.url, {}))
        # print("normalized url ",normalized_url)
        # date = None
        # if 'text/html' not in content_type:
        #     date = self.article_to_pdf_mapping.get(normalized_url, {}).get("date")
        #     return str(date)
        # else:
        #     print(start_url)
        #     date = self.article_to_date_mapping.get(normalized_url, {}).get("date")
        #     print(date)

        # if not date:
        #     print(f"[WARN] Date not found for {normalized_url}")

        # print(date)
        # return date if date else None

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        start_url = response.meta.get('start_url', '')
        content_type = response.headers.get('Content-Type', b'').decode()
        normalized_url = unquote(response.url).rstrip('/')
        pdf=[]
        if 'text/html' not in content_type:
                pdf = self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
                return [pdf] if pdf else []
        else :
            return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None