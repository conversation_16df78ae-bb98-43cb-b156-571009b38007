from scraper.OCSpider import OCSpider
import json
import scrapy
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
from urllib.parse import urljoin

class UDAHoldingsBerhadPressReleases(OCSpider):
    name = "UDAHoldingsBerhadPressReleases"

    country = "Malaysia"

    start_urls_names = {
            'https://www.uda.com.my/media/press-release' : 'Annual Reports'
        }
    
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    charset = "iso-8859-1"

    api_start_urls = {
        'https://www.uda.com.my/media/press-release': {
            "url": "https://www.uda.com.my/views/ajax?_wrapper_format=drupal_ajax",
            "payload": {
                "view_name": "press_release",
                "view_display_id": "block_2",
                "view_args": "all",
                "view_path": "/node/20",
                "view_base_path": "",
                "view_dom_id": "951c3f8125e3cb049476bc453fe6967067abbb706cac8e81974828479a867c0a",
                "pager_element": "0",
                "type" : "press_release",
                "year": "all",
                "page": "1",
                "_drupal_ajax": "1",
                "ajax_page_state[theme]": "osb5",
                "ajax_page_state[theme_token]" : "",
                "ajax_page_state[libraries]": "better_exposed_filters/general,bootstrap5/bootstrap5-js-latest,bootstrap5/global-styling,classy/base,classy/messages,core/normalize,core/picturefill,google_analytics/google_analytics,osb5/global-styling,paragraphs/drupal.paragraphs.unpublished,system/base,views/views.ajax,views/views.module,views_infinite_scroll/views-infinite-scroll"
            },
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["page"] = str(payload.get("page", "1")) 
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            callback=self.parse,
            formdata=payload,
            headers={
                "X-Requested-With": "XMLHttpRequest",
                "Referer": "https://www.uda.com.my/media/press-release",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
            }, 
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["page"],
            },
        )

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'SOE'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        article_urls = []
        try:
            json_text = response.xpath('//textarea/text()').get()
            data = json.loads(json_text)
            html_data = data[1].get("data", "")
            selector = scrapy.Selector(text=html_data)
            articles = selector.xpath("//div[@data-aos='fade-up']")
            for article in articles:
                url = article.xpath(".//a/@href").get()
                if url:
                    full_url = response.urljoin(url.strip())
                    article_urls.append(full_url)
            return self.get_proxy_articles(article_urls)
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="font-l fw-bold navy mb-3"]//text()').get(default="").strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//article[contains(@role, "article")]//p/text()').getall())
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        day = response.xpath('//div[contains(@class, "font-3xl")]/text()').get()
        month = response.xpath('//div[@class="text-end"]/div[1]/text()').get()
        year = response.xpath('//div[@class="text-end"]/div[2]/text()').get()
        date_str = f"{day} {month} {year}"
        return date_str

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None) -> list:
        base_url = "https://www.uda.com.my/media/"
        return [urljoin(base_url,url) for url in response.xpath('//a[contains(@href, ".pdf")]/@href').getall()]

    def get_authors(self, response):
        return [
            text.split(",")[0].strip()
            for text in response.xpath('//article[contains(@role, "article")]//p//strong/text()').getall()
            if "," in text and text.split(",")[0].strip()
        ]

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        json_text = response.xpath('//textarea/text()').get()
        data = json.loads(json_text)
        html_chunk = data[1].get("data", "")
        has_next = 'title="Load more items"' in html_chunk or 'rel="next"' in html_chunk
        next_page = int(current_page) + 1 if has_next else None
        return next_page
    
    def go_to_next_page(self, response, start_url, current_page: int = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            self.logger.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        print('The next page', next_page)
        if next_page is not None:
            payload = response.meta.get('payload')
            payload['page'] = str(next_page)  # Ensure it's a string
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "X-Requested-With": "XMLHttpRequest",
                    "Referer": "https://www.uda.com.my/media/press-release",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
                },
                callback=self.parse,
                meta={
                    'current_page': next_page,
                    'start_url': start_url,
                    'api_url': api_url,
                    'payload': payload,
                }
            )
        else:
            self.logger.info("No more pages to fetch.")

    def get_proxy_articles(self, articles):
        try:
            hbp = HeadlessBrowserProxy()
            proxy_urls = [hbp.get_proxy(url, timeout = 50000) for url in articles]
            return proxy_urls
        except Exception as e:
            self.logger.error(f"Failed to fetch proxy articles: {e}")
            return []