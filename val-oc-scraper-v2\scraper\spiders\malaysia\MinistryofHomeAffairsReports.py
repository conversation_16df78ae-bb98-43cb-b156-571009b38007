#https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/penerbitan-laporan

from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MinistryofHomeAffairsReports(OCSpider):
    name = "MinistryofHomeAffairsReports"
    start_urls_names = {
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/penerbitan-laporan": "News"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       articles= []
       for article in response.xpath('//div[contains(@class, "rl_sliders") and contains(@class, "nn_sliders") and contains(@class, "accordion")]//ul//li'):
        print(article)
        title = article.xpath('.//a/text()').get()
        link = article.xpath('.//a/@href').get()
        print("title============",title,"LINK==============",link)    
        if link:
               articles.append(link)
               self.article_data_map[link]={
                    "title":title,    
                    "link":link
                }
       
       print(self.article_data_map)
       return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
        #//*[@id="transaksi-perkhidmatan-online-2025"]/ul/li/a//@href response.xpath('//*[@id="transaksi-perkhidmatan-online-2025"]/ul/li/a//@href').get()
    def get_images(self, response) -> list:
         return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        title =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        date = title[-4:]
        parsed_date = dateparser.parse(date, languages=['ms','en'])
    
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")]
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None