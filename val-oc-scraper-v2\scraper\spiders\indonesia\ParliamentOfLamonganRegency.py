from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class ParliamentOfLamonganRegency(OCSpider):
    name = "ParliamentOfLamonganRegency"
    
    start_urls_names = {
        "https://setwan.lamongankab.go.id/kategori/berita?page=1": "News"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"
        
    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='col-12 col-md-6 col-lg-4 mb-4']//a//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='card-text']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='card shadow-sm mb-5']//img//@src").getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response): 
        date = response.xpath("//div[@class='card-body']//div[@class='d-flex align-items-center']//span//text()").get()
        indonesian_months = {
                "Januari": "January",
                "Februari": "February",
                "Maret": "March",
                "April": "April",
                "Mei": "May",
                "Juni": "June",
                "Juli": "July",
                "Agustus": "August",
                "September": "September",
                "Oktober": "October",
                "November": "November",
                "Desember": "December"
            }
        for indo, eng in indonesian_months.items():
            if indo in date:
                date = date.replace(indo, eng)
                break
        return date
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if response.status == 404:
            return None
        current_url = response.url
        match = re.search(r"\?page=(\d+)", current_url)
        if match:
            current_page_num = int(match.group(1))
            next_page_num = current_page_num + 1
            next_page_url = current_url.replace(
                    f"?page={current_page_num}",
                    f"?page={next_page_num}"
                )
        else:
            return None
        return next_page_url