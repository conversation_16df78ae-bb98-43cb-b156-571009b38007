from scraper.OCSpider import OCSpider 
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy

class EducationServiceCommissionAnnouncements(OCSpider):
    name = "EducationServiceCommissionAnnouncements"

    start_urls_names = {
        'https://www.spp.gov.my/': 'ministry'
    }

    start_urls_with_no_pagination_set = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_year = response.meta.get("current_year",datetime.now().year)
        url = f"{start_url}pengumuman/{current_year}"
        yield scrapy.Request(
            url= url,
            callback= self.parse,
            meta = {
                "start_url" : start_url,
                "current_year" : current_year,
            }
        )

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        return response.xpath('//span[@class="detail_data"]/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h2[@itemprop="headline"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="com-content-article__body"]//text()').getall())

    def get_date(self, response) -> str:
        return response.xpath('//time/@datetime').get() 

    def date_format(self) -> str:
        return "%Y-%m-%dT%H:%M:%S%z"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="com-content-article__body"]//img/@src').getall()
    
    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[@aria-label="Go to next page"]/@href').get()
        
    def go_to_next_page(self, response, start_url, current_page=None):
        current_year = int(response.meta.get("current_year"))
        start_url = response.meta.get("start_url")
        next_page = self.get_next_page(response)
        if next_page:
            full_url = response.urljoin(next_page)
            yield scrapy.Request(
                url = full_url,
                callback=self.parse,
                meta={
                    "current_year": current_year,
                    "start_url": start_url
                }
            )
        else:
            previous_year = current_year - 1
            url = f"{start_url}pengumuman/{previous_year}"
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                meta={
                    "current_year": previous_year,
                    "start_url": start_url
                }
            )