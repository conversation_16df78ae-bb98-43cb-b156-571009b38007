from scraper.OCSpider import OCSpider
import re
from typing import Dict

class MinistryOfInvestmentTradeAndIndustryStrategicPlan(OCSpider):
    name = "MinistryOfInvestmentTradeAndIndustryStrategicPlan"
    
    start_urls_names = {
        "https://www.miti.gov.my/index.php/pages/view/contente3f6.html": "MITI Strategic Plan"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.miti.gov.my/index.php/pages/view/contente3f6.html"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_date_map = {}

    def get_articles(self, response) -> list:
        if not hasattr(self, "article_date_map") or not self.article_date_map:
            self.extract_articles_with_dates(response)
        return list(self.article_date_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        if not hasattr(self, "article_date_map") or not self.article_date_map:
            self.extract_articles_with_dates(response)
        entry = response.request.meta.get("entry")
        return self.article_date_map.get(entry, {}).get("title", "").strip()

    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        if not hasattr(self, "article_date_map") or not self.article_date_map:
            self.extract_articles_with_dates(response)
        entry = response.request.meta.get("entry")
        return self.article_date_map.get(entry, {}).get("date", "").strip()

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No next page to scrape
        return None

    def extract_articles_with_dates(self, response) -> Dict[str, Dict[str, str]]:
        self.article_date_map = {}
        date_text = response.xpath('//em[contains(text(), "Last Updated")]/text()').get()
        date = None
        if date_text:
            match = re.search(r'Last Updated (\d{4}-\d{2}-\d{2})', date_text)
            if match:
                date = match.group(1)
        if not date:
            return self.article_date_map
        rows = response.xpath('//table//tr')
        for row in rows:
            title = row.xpath('.//p/strong/text()').get()
            if title:
                title = title.replace('\n', '').replace('\r', '').strip()
            link = row.xpath('.//a[contains(@href, ".pdf")]/@href').get()
            if link:
                full_url = response.urljoin(link)
            else:
                continue
            if title and date:
                self.article_date_map[full_url] = {
                    "title": title,
                    "date": date
                }
        return self.article_date_map