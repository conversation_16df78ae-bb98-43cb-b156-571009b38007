from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from typing import Optional

class FederalTerritorySportsCouncilWIPERSNews(OCSpider):
    name = "FederalTerritorySportsCouncilWIPERSNews"
    
    start_urls_names = {
        "https://www.wipers.gov.my/berita-terkini/": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"    
    
    def get_articles(self, response) -> list:
        return response.xpath("//h2//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//section[@data-id='fec3611']//p//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//section[@data-id='fec3611']//figure//img//@src").getall()

    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> Optional[str]:
        date_str = response.xpath("//div[@class='elementor-widget-container']//time//text()").get()
        if date_str:
            parsed_date = dateparser.parse(date_str, languages=['ms', 'en'])
            if parsed_date:
                return parsed_date.strftime(self.date_format())
        return None

    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return response.xpath("//ul//li//a[@class='next page-numbers']//@href").get()