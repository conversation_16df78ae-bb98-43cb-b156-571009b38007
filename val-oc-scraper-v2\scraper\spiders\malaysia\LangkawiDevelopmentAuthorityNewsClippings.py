from scraper.OCSpider import <PERSON>CSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
import re
from urllib.parse import unquote

class LangkawiDevelopmentAuthorityNewsClippings(OCSpider):
    name = "LangkawiDevelopmentAuthorityNewsClippings"

    start_urls_names = {
        'https://anyflip.com/bookcase/krjip' : 'Rak Artikel Langkawi 2025',  # Pagination is not supported
        'https://anyflip.com/bookcase/wrmya': 'Rak Artikel Langkawi 2024',  # Pagination is not supported
        'https://anyflip.com/bookcase/bscpt' : 'Rak Artikel Langkawi 2023',  # Pagination is not supported
        'https://anyflip.com/bookcase/kckii' : 'Rak Artikel Langkawi 2022',  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://anyflip.com/bookcase/krjip',
        'https://anyflip.com/bookcase/wrmya',
        'https://anyflip.com/bookcase/bscpt',
        'https://anyflip.com/bookcase/kckii'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(response.url,timeout=20000),
            callback=self.parse, 
            dont_filter=True, 
            meta ={
                "start_url" : response.url
                }
            )

    def get_articles(self, response):
        mapping = {}
        articles = []
        blocks = response.xpath('//div[@class="book-wrapper"]')
        mapping = {}
        for block in blocks:
            url = block.xpath('.//img[@class="book-img"]/@src').get()
            title = block.xpath('.//span/text()').get()
            if not url and not title:
                continue  
            year_match = re.search(r'(\d{4}\.\d{2})', title)
            date = year_match.group(0) if year_match else None
            if url and title:
                articles.append(url)
                mapping[url] = {
                    "title": title.strip(),
                    "date": date,
                    "img" :url
                }
        self.article_to_date_map.update(mapping)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('title')

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url).get('date')

    def date_format(self) -> str:
        return "%Y.%m"
    
    def get_images(self, response) -> list[str]:
        normalized_url = unquote(response.url)
        return [self.article_to_date_map.get(normalized_url).get('img')]

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None