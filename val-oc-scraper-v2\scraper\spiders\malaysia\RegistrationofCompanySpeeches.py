from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import re

class RegistrationofCompanySpeeches(OCSpider):
    name = "RegistrationofCompanySpeeches"

    start_urls_names = {
        'https://www.ssm.com.my/Pages/Publication/Speeches/Speeches.aspx': 'speeches'
    }

    charset = "utf-8"

    country = "Malaysia"

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
	}

    HEADLESS_BROWSER_WAIT_TIME = 3000

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
	}

    HEADLESS_BROWSER_WAIT_TIME = 10000

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    

    def get_articles(self, response) -> list:
        return response.xpath('//td[@class="ms-vb2"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title_parts = response.xpath('//td[@id="SPFieldText"]//text()').getall()
        title = ' '.join(part.strip() for part in title_parts if part.strip())
        return title

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="ms-rtestate-field"]//text()').getall())

    def get_date(self, response) -> str:
        date_data = str(response.xpath('//td[@id="SPFieldDateTime"]//text()').getall())
        match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', date_data)
        if match:
            return match.group(1)
        else:
            raise ValueError(f"[ERROR] Date not found in response from {response.url}")

    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_document_urls(self, response, entry=None):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        onclick = response.xpath('//td/a[@href="javascript:"]/@onclick').get()
        if onclick:
            match = re.search(r'RefreshPageTo\(event,\s*"([^"]+)"', onclick)
        if match:
            relative_url = match.group(1)
            return response.urljoin(relative_url)
