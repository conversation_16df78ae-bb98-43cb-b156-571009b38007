from scraper.OCSpider import <PERSON>CSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
import re

class SelangorStateGovernmentStateSecretaryCirculars(OCSpider):
    name = "SelangorStateGovernmentStateSecretaryCirculars"

    start_urls_names = {
        "https://www.selangor.gov.my/index.php/database_stores/store_view/11": "11",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/18": "18",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/21": "21",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/26": "26",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/27": "27",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/29": "29",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/31": "31",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/28": "28",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/30": "30",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/32": "32",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/16": "16",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/17": "17",
        "https://www.selangor.gov.my/index.php/database_stores/store_view/19": "19"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def get_articles(self, response) -> list:  
        base_url = "https://www.selangor.gov.my"
        articles =[]
        mapping = {}
        for entry in response.xpath('//div[@class="grid-box-wrap col-1-1"]'):
            url= entry.xpath('.//a/@href').get()
            full_url = base_url + url if url else None
            if full_url:
                articles.append(full_url)
            title = entry.xpath('.//h3//text()').get()
            raw_date = entry.xpath('.//h3//text()').get()
            bil_match = re.search(r'\bBIL\.\s*(\d+)', raw_date or "")
            tahun_match = re.search(r'\bTAHUN\s*(\d{4})', raw_date or "")
            bil_number = bil_match.group(1) if bil_match else None
            year = tahun_match.group(1) if tahun_match else None
            date =bil_number + " " + year if bil_number and year else None
            mapping[full_url] = {
                'title': title.strip() if title else None,
                'date': date.strip() if date else None,
                'pdf_url': full_url
            }
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        article_url = response.url
        if article_url in self.article_to_pdf_mapping:
            title = self.article_to_pdf_mapping[article_url].get('title')
            if title:
                print(title)
                return title.strip()
    
    def get_body(self, response) -> str:
        ## body is pdf file
        return " "

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %Y"
    
    def get_date(self, response) -> str:
        article_url = response.url
        if article_url in self.article_to_pdf_mapping:
            date = self.article_to_pdf_mapping[article_url].get('date')
            if date:
                print(date)
                return date.strip()

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        article_url = response.url
        if article_url in self.article_to_pdf_mapping:
            pdf_url = self.article_to_pdf_mapping[article_url].get('pdf_url')
            if pdf_url:
                return [pdf_url]
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None