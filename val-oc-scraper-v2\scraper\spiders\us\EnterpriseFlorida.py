from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy
from parsel import Selector
import json
from urllib.parse import urlencode

class EnterpriseFlorida(OCSpider):
    name = "EnterpriseFlorida"

    country = "US"

    start_urls_names = {
        "https://selectflorida.org/news-resources/": "News and Media",
    }

    api_start_urls = {
        "https://selectflorida.org/news-resources/": {
            "url": "https://selectflorida.org/wp-json/wp/v2/posts",
            "payload": {
                "per_page": 10,
                "page": 1
            }
        }
    }

    proxy_country = "us"

    custom_settings = {
    "DOWNLOADER_MIDDLEWARES": {
        "scraper.middlewares.HeadlessBrowserProxy": 350,
        "scraper.middlewares.GeoProxyMiddleware": 350,
    },
    "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    "DOWNLOAD_DELAY": 1
}
    HEADLESS_BROWSER_WAIT_TIME = 30

    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/110.0.0.0 Safari/537.36"
        )
    }
    charset = "utf-8"

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/New_York"

    def _load_json(self, response) -> list:
        text = response.text
        if "<pre>" in text:
            text = Selector(text=text).css("pre::text").get() or ""
        return json.loads(text)

    def parse_intermediate(self, response):
        start_url = response.meta["start_url"]
        api_data = self.api_start_urls[start_url]
        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]
        full_api_url = f"{api_url}?{urlencode(payload)}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse,         
            meta={
                "start_url": start_url,
                "payload": payload,
                "current_page": payload["page"],
            }
        )

    def get_articles(self, response) -> list:
        data = self._load_json(response)
        return [
            entry["link"]
            for entry in data
            if isinstance(entry, dict) and entry.get("link")
        ]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath(
            '//h2[@class="mb-3 leading-none text-1.75 '
            'md:text-2 xl:text-2.5"]/text()'
        ).get().strip()

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath(
                '//div[@class="overview-content news-content"]//p//text()'
            ).getall()
        )

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        date_str = response.xpath(
            '//span[@class="mb-1 text-sm uppercase font-bold '
            'font-antonio text-blue-500 tracking-wider '
            'md:text-base xl:text-1.375 xl:mb-2"]/text()'
        ).get().strip()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")

    def get_authors(self, response):
        return ""

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        data = self._load_json(response)
        next_page = current_page + 1 if data else None
        return next_page

    def get_page_flag(self) -> bool:
        return True

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls[start_url]
        api_url = api_data["url"]
        payload = response.meta["payload"].copy()
        next_page = self.get_next_page(response, current_page)
        if not next_page:
            return
        payload["page"] = next_page
        full_api_url = f"{api_url}?{urlencode(payload)}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "payload": payload,
                "current_page": next_page
            }
        )