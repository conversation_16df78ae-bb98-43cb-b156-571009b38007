from typing import List, Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re
class MetallurgicalCorporationofChinaLimited(OCSpider):
    name = "MetallurgicalCorporationofChinaLimited"

    start_urls_names = {
        'http://www.mcc.com.cn/xwzx_7388/lddt/index.html': "中国中冶",
        'http://www.mcc.com.cn/xxgk_7595/qygg/index.html': "中国中冶",
        'http://www.mcc.com.cn/tzzgx_7555/yjtj/index.html': "中国中冶",
        'http://www.mcc.com.cn/tzzgx_7555/yjbg/index.html': "中国中冶",
        'http://www.mcc.com.cn/tzzgx_7555/aggg/index.html': "中国中冶",
        'http://www.mcc.com.cn/tzzgx_7555/hggg/index.html': "中国中冶",
        'http://www.mcc.com.cn/tzzgx_7555/gddh/index.html': "中国中冶",
    }

    article_data_map = {}  # Mapping child article with title, date and PDF from start URL

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//div[@class="sortlist"]//ul//li | //div[@class="tzzgx_yjbg_con sortlist"]//ul//li'):
            url = article.xpath(".//a/@href").get()
            title = article.xpath(".//a/@title | .//a//p//text()").get()
            date = article.xpath(".//span//text()").get()
            if url and title and date:
                full_url = response.urljoin(url)
                title = title.strip()
                pdf = full_url if '.pdf' in full_url.lower() else "None"
                clean_date = date.strip()
                self.article_data_map[full_url] = {
                    "title": title,
                    "date": clean_date,
                    "pdf": pdf
                }
                articles.append(full_url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        else :
            body =body_normalization(response.xpath('//div[@class="main qs_clear xl"]//text()').getall())
            if not body:
                return body_normalization(response.xpath('//div[@class="contant"]//text()').getall())
            return body
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> str:
        date_from_map = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if date_from_map:
            return date_from_map
        xpath_date = response.xpath("//div[@class='xl_xx qs_clear']//span//text()").get()
        return xpath_date.strip() if xpath_date else ""
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        if pdf_url and pdf_url != "None":
            return [pdf_url]  
        else:
            pdf= response.xpath('//div[@class="main qs_clear xl"]//p//a[contains(@href,".pdf")]//@href | //div[@class="contant"]//p//a//@href').getall()
            for i in pdf:
                f'{response.url}{i}'

    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return []
        return response.xpath('//div[@class="fck_uploadpics"]//img//@src | //div[@class="contant"]//img//@src').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if response.status == 404:
            return None
        current_url = response.url
        if "index.html" in current_url:
            next_page_url = current_url.replace("index.html", "index_1.html")
        else:
            match = re.search(r"index_(\d+)\.html", current_url)
            if match:
                current_page_num = int(match.group(1))
                next_page_num = current_page_num + 1
                next_page_url = current_url.replace(
                    f"index_{current_page_num}.html",
                    f"index_{next_page_num}.html"
                )
            else:
                return None
        return next_page_url