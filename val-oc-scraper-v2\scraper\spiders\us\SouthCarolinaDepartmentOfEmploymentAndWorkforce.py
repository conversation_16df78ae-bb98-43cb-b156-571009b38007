from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class SouthCarolinaDepartmentOfEmploymentAndWorkforce(OCSpider):
    name = "SouthCarolinaDepartmentOfEmploymentAndWorkforce"

    country = "US"

    start_urls_names = {
        "https://www.dew.sc.gov/news/archive": "News Archive",
    }

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }
    
    charset = "utf-8"
    
    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="views-row"]//h2[@class="views-field views-field-title"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@id="block-dewpalmetto-page-title"]//h1/span/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="mainContent group"]//p[not(contains(@class, "text-align-center")) and not(contains(text(), "###"))]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="card__date"]/text()').get().strip().split(', ')[1]
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="pager__item pager__item--next"]//a/@href').get()
        if not next_page:
           return None
        else:
            return next_page