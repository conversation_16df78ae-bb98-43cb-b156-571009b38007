from scraper.OCSpider import OCSpider
import re

class FederalTerritorySportsCouncilWIPERSSpeeches(OCSpider):
    name = "FederalTerritorySportsCouncilWIPERSSpeeches"

    start_urls_names = {
        "https://www.wipers.gov.my/teks-ucapan/": "News"
    }

    start_urls_with_no_pagination_set = {
        "https://www.wipers.gov.my/teks-ucapan/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for link in response.xpath("//div[@class='tab-content']//ol//li"):
            url = link.xpath(".//a//@href").get()
            title = link.xpath(".//a//text()").get()
            if url and title:
                full_url = response.urljoin(url)
                self.article_data_map[full_url] = {"title": title.strip()}
                articles.append(full_url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y/%m"

    def get_date(self, response):
        match = re.search(r"/(\d{4})/(\d{2})/", response.url)
        if match:
            year = match.group(1)
            month = match.group(2)
            return f"{year}/{month}"  
        return None
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None