from scraper.OCSpider import OCSpider
from datetime import datetime
import urllib.parse as urlparse 
from urllib.parse import urlencode, urlunparse, parse_qs

class LandPublicTransportAuthorityPressReleases(OCSpider):
    name = "LandPublicTransportAuthorityPressReleases"

    start_urls_names = {
        'https://www.apad.gov.my/sumber-maklumat1/media/kenyataan-media-1?category[0]=39&category_children=1' : 'Press Release',
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    MALAY_TO_ENGLISH_MONTHS = {
        "Jan": "January",
        "Feb": "February",
        "Mac": "March",      
        "Apr": "April",
        "Mei": "May",
        "Jun": "June",
        "Jul": "July",
        "Ogs": "August",     
        "Sep": "September",
        "Okt": "October",
        "Nov": "November",
        "Dis": "December"
    }

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//table[@class="table table-striped"]//tbody//tr')
        for row in rows:
           parts = row.xpath('.//span[@itemprop="name"]/text()').getall()
           title = ''.join(parts).strip()
           date = row.xpath('.//td//time/text()').get()
           pdf_link = row.xpath('.//td//a/@href').get()
           if title and date and pdf_link:
                title = title.strip()
                date = date.strip()
                pdf_link = response.urljoin(pdf_link.strip())
                self.article_title_pdf_mapping[pdf_link] = (title, pdf_link, date)
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        date = self.article_title_pdf_mapping.get(response.url, ("", "", ""))[2]
        for malay, english in self.MALAY_TO_ENGLISH_MONTHS.items():
            if malay in date:
                date = date.replace(malay, english)
                break
        try:
            return datetime.strptime(date, "%d %B %Y").strftime("%d %B %Y")
        except Exception as e:
            self.logger.error(f"Date conversion failed for '{date}': {e}")
            return ""

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None) ->list:
        return [self.article_title_pdf_mapping.get(response.url, ("", "", ""))[1]]

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        current_url = response.url
        parsed = urlparse.urlparse(current_url)
        query_params = parse_qs(parsed.query)
        current_start = int(query_params.get('limitstart', ['0'])[0])
        limit = int(query_params.get('limit', ['20'])[0]) 
        next_start = current_start + limit
        MAX_LIMITSTART = 40  
        if next_start > MAX_LIMITSTART:
            return None  
        query_params['limitstart'] = [str(next_start)]
        new_query = urlencode(query_params, doseq=True)
        new_url = urlunparse(parsed._replace(query=new_query))
        return new_url
        