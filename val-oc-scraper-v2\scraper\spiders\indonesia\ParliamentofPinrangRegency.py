from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfPinrangRegency(OCSpider):
    name = "ParliamentOfPinrangRegency"

    start_urls_names = {
        "https://dprd.pinrangkab.go.id/berita/":"berita"
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h3/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[contains(@class, 'content')]//p//text()").getall()) 

    def get_images(self, response) -> list:
        return response.xpath("//div[contains(@class, 'content')]//img/@src").getall()

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        date_text = response.xpath("//time/text()").get()
        if date_text:
            date_text = date_text.strip()
            indonesian_months = {
                'Januari': 'January', 'Februari': 'February', 'Maret': 'March',
                'April': 'April', 'Mei': 'May', 'Juni': 'June',
                'Juli': 'July', 'Agustus': 'August', 'September': 'September',
                'Oktober': 'October', 'November': 'November', 'Desember': 'December'
            }
            for indo_month, eng_month in indonesian_months.items():
                if indo_month in date_text:
                    date_text = date_text.replace(indo_month, eng_month)
                    break
            return date_text
        return "No date found"

    def get_authors(self, response):
        author = response.xpath("//a[contains(@href, 'author')]/text()").get()
        if author:
            return [author.strip()]
        return ["DPRD Pinrang"]
    
    def get_page_flag(self, response=None) -> bool:
        return False
     
    def get_next_page(self, response):
        # No next page to scrape
        return None