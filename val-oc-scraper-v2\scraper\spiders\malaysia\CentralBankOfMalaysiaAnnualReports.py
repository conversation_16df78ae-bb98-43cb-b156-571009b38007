from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import urljoin,unquote
import re

class CentralBankOfMalaysiaAnnualReports(OCSpider):
    name = "CentralBankOfMalaysiaAnnualReports"

    start_urls_names = {
       "https://www.bnm.gov.my/publications/ar" : "Feature Articles"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "Bank"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        # Articles till year 2024 
        current_year = response.meta.get("current_year",2024)
        url = f"{start_url}{current_year}"
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(url, timeout=50000),
            callback=self.parse,
            meta = {
                "start_url" : start_url,
                "current_year": current_year
            }
        )

    def get_articles(self, response) -> list: 
        mapping = {}
        current_year=response.meta.get("current_year")
        articles = [] 
        base_url = "https://www.bnm.gov.my"
        containers = response.xpath('//div[@class="col-12 col-sm-6 col-lg-4 px-2 mb-0"]')
        for box in containers:
            link = box.xpath('.//a/@href').get()
            title = box.xpath('.//a/text()').get()
            date = current_year
            if link:
                full_url = urljoin(base_url,link)
                normalized_link = unquote(full_url).rstrip('/')
                articles.append(normalized_link)
                mapping[normalized_link] = {
                        'title': title,
                        'pdf': normalized_link,    
                        'date': date
                    }
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ''
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        date = self.article_to_pdf_mapping.get(response.url,{}).get('date')
        if date:
            return str(date)

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
  
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        current_year = response.meta.get("current_year")
        next_year = str(int(current_year)-1)
        if response.status == 200:
            return next_year
        else :
            return 
        
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        next_year = self.get_next_page(response)
        if next_year:
            url = f"{start_url}{next_year}"
            hbp = HeadlessBrowserProxy()
            yield scrapy.Request(
                hbp.get_proxy(url, timeout=20000),
                callback=self.parse,
                meta = {
                    "start_url" : start_url,
                    "current_year": next_year
                }
            )