from scraper.OCSpider import OCSpider
import dateparser

class DepartmentofWaterSupplyAnnouncementsAndActivities(OCSpider):
    name = "DepartmentofWaterSupplyAnnouncementsAndActivities"

    start_urls_names = {
        "https://www.jba.gov.my/?cat=24": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"
    
    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        return response.xpath('//*[@id="content"]/div/article/h2//a//@href').getall()   
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//*[@id="content"]/header/h1//text() | //span[@class="breadcrumb_last"]//text()').get()
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return response.xpath('//*[@id="content"]/div/figure/img//@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m"
    
    def get_date(self, response) -> str:
        curr_date1=  response.xpath('//*[@id="content"]/header/h1//text()').get()
        curr_date=curr_date1[-7:-1]
        parsed_date = dateparser.parse(curr_date, languages=['en']) 
        return parsed_date.strftime("%Y-%m")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath("//div[@class='nav-previous']//a//@href").get()