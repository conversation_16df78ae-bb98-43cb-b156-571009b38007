import re
from typing import Optional
from datetime import datetime
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization


class LegislativeResearchInstituteVietnam(OCSpider):
    name = "LegislativeResearchInstituteVietnam"

    start_urls_names = {
        "https://quochoi.vn/viennghiencuulapphap/tintuc/Pages/hoat-dong-chung-cua-vien.aspx": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 100
        },
        "DOWNLOAD_DELAY": 3,
        "RANDOMIZE_DOWNLOAD_DELAY": 1.0,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    country = "Vietnam"

    @property
    def language(self):
        return "Vietnamese"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="container"]//p//a//@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="container"]//h1//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath('//div[@class="container"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="container"]//img//@src').getall()

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> Optional[str]:
        text = response.xpath('//div[@class="box-date"]//p//text()').getall()
        combined = " ".join([t.strip() for t in text if t.strip()])
        match = re.search(r"(\d{2}/\d{2}/\d{4})", combined)
        if match:
            try:
                parsed_date = datetime.strptime(match.group(1), "%d/%m/%Y")
                return parsed_date.strftime(self.date_format())
            except ValueError:
                return match.group(1)
        return None

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return response.xpath('//div[@class="paging"]//a[contains(text(), "Sau")]/@href').get()