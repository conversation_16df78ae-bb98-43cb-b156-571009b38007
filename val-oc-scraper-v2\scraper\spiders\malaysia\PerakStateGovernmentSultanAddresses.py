from scraper.OCSpider import OCSpider
import dateparser
import scrapy
from scraper.middlewares import HeadlessBrowserProxy

class PerakStateGovernmentSultanAddresses(OCSpider):
    name = "PerakStateGovernmentSultanAddresses"
    
    start_urls_names = {
       'https://www.perak.gov.my/index.php/kerajaan-negeri/info-umum/koleksi-ucapan-dymm-paduka-seri-sultan-perak':''
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"
     
    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    charset = "iso-8859-1"
    
    article_data_map = {}
    
    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request 
        
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//tbody//tr//td[@valign='top']"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//a//text()").get()
            print(url,title)
            if url and title :
                title = title.strip() 
                self.article_data_map[url] = {"title": title}
                articles.append(url) 
        return articles
    
    def get_href(self, entry) -> str:
        return f'https://www.perak.gov.my{entry}'
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        if '-' in title:
            date = title.split("-")[-1]
        else:
            date = title.split("Belas")[-1]
        parsed_date = dateparser.parse(date, languages=['ms',"en"])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None):
        return [response.url]
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return None