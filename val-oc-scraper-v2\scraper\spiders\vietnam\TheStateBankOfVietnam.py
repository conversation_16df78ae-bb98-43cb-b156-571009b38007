from scraper.OCSpider import OCSpider
from datetime import datetime
import re

class TheStateBankOfVietnam(OCSpider):
    name = "TheStateBankOfVietnam"

    start_urls_names = {
        "https://www.sbv.gov.vn/vi/trang-chu#%40%3F_afrLoop%3D18616590075363774%26centerWidth%3D80%2525%26leftWidth%3D20%2525%26rightWidth%3D0%2525%26showFooter%3Dfalse%26showHeader%3Dfalse%26_adf.ctrl-state%3Dp14p8o2e_136": "Press release"
    }

    start_urls_with_no_pagination_set = {
        "https://www.sbv.gov.vn/vi/trang-chu#%40%3F_afrLoop%3D18616590075363774%26centerWidth%3D80%2525%26leftWidth%3D20%2525%26rightWidth%3D0%2525%26showFooter%3Dfalse%26showHeader%3Dfalse%26_adf.ctrl-state%3Dp14p8o2e_136"   #Pagination is not supported
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "bank"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:  
        relative_urls = response.xpath('//div[@class="policy-item"]//a[@class="policy-title-link"]/@href').getall()
        return [response.urljoin(url) for url in relative_urls]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="title-page detail"]/text()').get()

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        raw = response.xpath('//time[@class="author-time"]/@datetime').get()    
        clean = re.sub(r'\b\d{1,2}:\d{2}(:\d{2})?\b', '', raw).strip()
        for fmt in ("%d/%m/%Y", "%m/%d/%Y", "%Y-%m-%d"):
            try:
                dt = datetime.strptime(clean, fmt)
                return dt.strftime("%d/%m/%Y")
            except ValueError:
                continue
        return raw
    
    def get_author(self, response) -> str:
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.urljoin(response.xpath('//div[@class="faq-send-upload"]//span/a/@href').get())

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):  
        return None
