from typing import List, Union
import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from bs4 import BeautifulSoup
import json
import re

class FiiFoxconn(OCSpider):
    name = "FiiFoxconn"

    start_urls_names = {
        "https://www.fii-foxconn.com/NewsList": "工业富联",
        "https://www.fii-foxconn.com/NewsMedialist": "工业富联",
        "https://www.fii-foxconn.com/TemporaryAnnouncement": "工业富联",
        "https://www.fii-foxconn.com/InveStmentFinancial":"工业富联"
    }

    api_start_url = {
        'https://www.fii-foxconn.com/NewsList': {
            'url': 'https://panel.fii-foxconn.com/GetData/getDataSearch2',
            'payload': {
                "model": "article",
                "tid": "24",
                "limit": "9",
                "word": "",
                "tagtype": "",
                "page": "1",
            },
        },
        'https://www.fii-foxconn.com/NewsMedialist': {
            'url': 'https://panel.fii-foxconn.com/GetData/getDataSearch',
            'payload': {
                "model": "article",
                "tid": "",
                "limit": "9",
                "page": "1",
            },
        },
        'https://www.fii-foxconn.com/TemporaryAnnouncement': {
            'url': 'https://panel.fii-foxconn.com/GetData/index',
            'payload': {
                "model": "article",
                "tid": "115",
                "isshow": "1",
                "orders": "orders desc,id desc",
                "limit": "20",         
            },
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["page"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
        
    article_data_map ={}  # Mapping title, date and PDF with child articles from start URL

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        start_url = response.meta.get("start_url", "").strip()
        articles = []
        if "panel.fii-foxconn.com/GetData" in response.url:
            try:
                data = json.loads(response.text).get("data", [])
                for entry in data:
                    title = entry.get("title", "").strip()
                    date = entry.get("addtime", "").strip()
                    target_url = entry.get("target", "").strip()
                    body = entry.get("body", "")
                    id_ = entry.get("id")
                    htmlurl = entry.get("htmlurl", "").strip()
                    molds = entry.get("molds", "article").strip()

                    # if id_:
                    if target_url:
                        full_url = target_url
                    elif id_:
                        full_url = f"https://www.fii-foxconn.com/NewsDetail?id={id_}"
                    elif htmlurl and molds and id_:
                        full_url = f"https://www.fii-foxconn.com/{molds}/{htmlurl}/{id_}.html"
                    else:
                        continue
                    pdf = full_url if full_url.lower().endswith(".pdf") else "None"
                    if full_url and title and date:
                        self.article_data_map[full_url] = {
                            "title": title,
                            "date": date,
                            "pdf": pdf,
                            "body": body,
                        }
                        articles.append(full_url)
                    else:
                        return
            except Exception as e:
                return
            return articles
        articles = response.xpath('//ul[@class="ul-newslist1 news libpic-t href"]//li/a/@href | //ul[@class="ul-newslist1 libpic-t href"]//li/a/@href').getall()
        if articles:
            return articles
        for article in response.xpath('//ul[@id="table0"]//li'):
            url = article.xpath('.//div[@class="btn-box"]//a/@href').get()
            title = article.xpath('//div[@class="flex-box"]//p//text()').get()
            date = article.xpath('//div[@class= "time"]//text()').get()
            if url and title and date:
                title = title.strip()
                clean_date = date.strip()
                pdf = url if url.lower().endswith(".pdf") else "None"
                self.article_data_map[url] = {"title": title, "date": clean_date, "pdf": pdf}
                articles.append(url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        entry_url = response.request.meta.get('entry')
        if not entry_url:
            entry_url = response.url
        if ".pdf" in entry_url.lower():
            return self.article_data_map.get(entry_url, {}).get("title", "")
        title = self.article_data_map.get(entry_url, {}).get("title", "")
        if title:
            return title
        return response.xpath('//td[@class="hei16b" and @align="center"]/text() | //div[@class="tits"]/div[@class="tit"]/text()').get(default="")
    
    def get_body(self, response) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath('//div[@class="txts"]//p//text() | //div[@class="rich_media_wrp"]//p//text() |//td[@class="black14"]//founder-content//p//text() | //div[@class="txts"]//div[@class="txtblock mb40" or name()="div"]//text()').getall())
    
    def get_images(self, response) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath('//div[@class="txts"]//img/@src').getall()
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        entry_url = response.request.meta.get('entry')
        if not entry_url:
            entry_url = response.url
        if ".pdf" in entry_url.lower():
            return self.article_data_map.get(entry_url, {}).get("date", "")
        date = self.article_data_map.get(entry_url, {}).get("date", "")
        if date:
            return date.strip()
        date = response.xpath('//td[@class="black12"]/text()').re_first(r"\d{4}-\d{2}-\d{2}")
        if not date:
            zh_date = response.xpath('//div[@class="txtblock mb40"]/i/text()').re_first(r'(\d{1,2})月(\d{1,2})日，(\d{4})年')
            if zh_date:
                match = re.search(r'(\d{1,2})月(\d{1,2})日，(\d{4})年', zh_date)
                if match:
                    month, day, year = match.groups()
                    return f"{year}-{int(month):02d}-{int(day):02d}"
        return ""
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
    def get_next_page(self, response, current_page) -> Union[None, str]:
        data = response.text
        soup = BeautifulSoup(data, 'html.parser')
        hrefs = [a['href'] for a in soup.find_all('a', href=True)]
        if hrefs:
            return current_page + 1
        else:
            return None
    
    def get_page_flag(self) -> bool:
        return False
    
    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={
                    'current_page': next_page, 
                    'start_url': start_url, 
                    'api_url': api_url
                }
            )
        else:
            yield None