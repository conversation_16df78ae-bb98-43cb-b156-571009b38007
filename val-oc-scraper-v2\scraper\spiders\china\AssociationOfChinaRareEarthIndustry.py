from typing import Optional
import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import json

class AssociationOfChinaRareEarthIndustry(OCSpider):
    name = "AssociationOfChinaRareEarthIndustry"

    start_urls_names = {
        "https://ac-rei.org.cn/module/17b78aa3-dc90-428b-8ad6-40ad3a696861" : "通知公告", 
        "https://ac-rei.org.cn/module/2756d2c3-0db3-4eeb-a699-6fd4c9b82177" : "协会动态",
        "https://ac-rei.org.cn/module/cc0dbee1-cac0-4e93-ace1-040a8c268e2a" : "新闻中心",
        "https://ac-rei.org.cn/module/50da11c1-cd65-4de0-8148-39f7a43d0c9f" : "国际交流",
        "https://ac-rei.org.cn/module/eaf33ec3-6273-423e-b933-9433d01f9e01" : "会员风采"
    }

    api_start_urls = {
        'https://ac-rei.org.cn/module/17b78aa3-dc90-428b-8ad6-40ad3a696861': {
            "url": "https://ac-rei.org.cn/module/list.json",
            "payload": {
                "moduleId": "17b78aa3-dc90-428b-8ad6-40ad3a696861",
                "start": "1",
                "limit": "10"
            },
        },    
        'https://ac-rei.org.cn/module/2756d2c3-0db3-4eeb-a699-6fd4c9b82177' : {
            "url": "https://ac-rei.org.cn/module/list.json",
            "payload": {
                "moduleId": "2756d2c3-0db3-4eeb-a699-6fd4c9b82177",
                "start": "1",
                "limit": "10"
            },
        },    
        'https://ac-rei.org.cn/module/cc0dbee1-cac0-4e93-ace1-040a8c268e2a' : {
            "url": "https://ac-rei.org.cn/module/list.json",
            "payload": {
                "moduleId": "cc0dbee1-cac0-4e93-ace1-040a8c268e2a",
                "start": "1",
                "limit": "10"
            },
        },    
        'https://ac-rei.org.cn/module/50da11c1-cd65-4de0-8148-39f7a43d0c9f' : {
            "url": "https://ac-rei.org.cn/module/list.json",
            "payload": {
                "moduleId": "50da11c1-cd65-4de0-8148-39f7a43d0c9f",
                "start": "1",
                "limit": "10"
            },
        },    
        'https://ac-rei.org.cn/module/eaf33ec3-6273-423e-b933-9433d01f9e01' : {
            "url": "https://ac-rei.org.cn/module/list.json",
            "payload": {
                "moduleId": "eaf33ec3-6273-423e-b933-9433d01f9e01",
                "start": "1",
                "limit": "10"
            },               
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload["start"]
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers={"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"},
            formdata=payload,
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
        )

    charset = "UTF-8"

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        json_data = json.loads(response.text)
        articles = [f"https://ac-rei.org.cn/article/{item['id']}" for item in json_data.get('data', {}).get('list', [])]
        return articles if articles else []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="news_title"]/text()').get()

    def get_body(self, response) -> str:
        body = body_normalization(response.xpath('//section[@class="met-editor clearfix"]//p//text()').getall())
        if not body:
            body = body_normalization(response.xpath('//div[@class="news_info"]//p//text()').getall())
        if not body:
            body = body_normalization(response.xpath('//div[@class="news_detail no-layui"]//text()').getall())    
        return body
    
    def date_format(self) -> str:
        return "%Y-%m-%d %H:%M:%S.%f"

    def get_date(self, response):
        return response.xpath('//div[@class="news_info"]/div[@class="news_author"]/text()').re_first(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+)')

    def get_authors(self, response) -> str:
        return []
    
    def get_images(self, response) -> list:
        images = response.xpath('//div[@class="news_detail no-layui"]//img/@src').getall()
        return [response.urljoin(img) for img in images] if images else []
    
    def get_page_flag(self) -> bool:
        return False 

    def get_next_page(self, response):
        current_page = int(response.meta.get("current_page"))
        total_articles = response.json().get('data', {}).get('count', 1)
        page_size = 10
        max_pages = int((total_articles / page_size) + 1)
        if current_page < max_pages:
            current_page = current_page + 1
            return str(current_page)
        else:
            return None

    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        next_page = self.get_next_page(response)
        payload["start"] = next_page
        if next_page:
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded;"},
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}") 