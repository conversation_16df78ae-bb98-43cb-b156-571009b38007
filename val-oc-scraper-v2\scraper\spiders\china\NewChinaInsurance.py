from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class NewChinaInsurance(OCSpider):
    name = "NewChinaInsurance"

    start_urls_names = {
        "https://www.newchinalife.com/spage/cn/newsCenter/index.html": "新华保险",
        "https://www.newchinalife.com/node/398": "新华保险",
        "https://www.newchinalife.com/node/400": "新华保险",
        "https://www.newchinalife.com/node/581": "新华保险",
        "https://www.newchinalife.com/node/582": "新华保险",
        "https://www.newchinalife.com/spage/cn/shareholderEquity/index.html": "新华保险",
        "https://www.newchinalife.com/node/378": "新华保险",
        "https://www.newchinalife.com/node/584": "新华保险",
        "https://www.newchinalife.com/node/585": "新华保险",
        "https://www.newchinalife.com/node/2070": "新华保险",
        "https://www.newchinalife.com/spage/cn/accidentInsurance/index.html": "新华保险",
        "https://www.newchinalife.com/spage/cn/investorHistoricalDividends/48939.html": "新华保险",
        "https://www.newchinalife.com/info/57254": "新华保险"
    }

    article_data_map ={}  # Mapping title, date and PDF with child articles from start URL

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            articles = []
            for article in response.xpath("//div[@class='presentationList']//ul//li"):
                url = article.xpath(".//a/@href").get()
                title = article.xpath(".//a//@title | .//a//p//text()").get()
                date = article.xpath(".//span//text()").get()
                if url and title and date:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"
                    clean_date = date.strip()
                    self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                    articles.append(full_url) 
            return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        else:
            return body_normalization(response.xpath("//div[@id='page-content']//p//text() | //div[@class='shareholder-table']//p//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        else:
            return response.xpath("//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='paget']//ul//li[last()]/a/@href").get()
        if next_page:
            return next_page
        else:
            return None