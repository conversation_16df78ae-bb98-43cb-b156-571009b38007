from scraper.OCSpider import OCSpider

class ParliamentOfSintangRegency(OCSpider):
    name = "ParliamentOfSintangRegency"

    start_urls_names = {
        "https://dprd.sintang.go.id/ppid/berita": "PPID"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:   
        return response.xpath("//div[@class='row']//a[@class='link-dark']//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="post-header"]/h1//text()').get().strip()
    
    def get_body(self, response) -> str:
        # Only images to scrape
        return ""
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="classic-view"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%d %b %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//li[@class="post-date"]/span/text()').get()
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None