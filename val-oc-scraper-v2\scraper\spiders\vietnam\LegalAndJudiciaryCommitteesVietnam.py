from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class LegalAndJudiciaryCommitteesVietnam(OCSpider):
    name = "LegalAndJudiciaryCommitteesVietnam"

    start_urls_names = {
        "https://quochoi.vn/uybanphapluat/tintuc/Pages/tin-hoat-dong.aspx": "Tin hoạt động",
    }

    start_urls_with_no_pagination_set = {}

    country = "Vietnam"

    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return list(set(response.xpath('//div[@class="container"]/p[@class="title"]/a/@href').getall()))

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="container"]/h1/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='detail' and @id='anhnoidung']//p//text()").getall())

    def get_images(self, response) -> list:
        return [response.urljoin(url) for url in response.xpath('//div[@class="ExternalClass428B7BE32C9948EEB84700CE565D4E5D"]//img/@src').getall()]

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return response.xpath('//div[@class="box-date"]//p[@class="date"]/span/text()').get().strip()
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        next_url = response.xpath('//div[@class="paging"]//a[contains(text(),"Sau")]/@href').get()
        return response.urljoin(next_url) if next_url else None
