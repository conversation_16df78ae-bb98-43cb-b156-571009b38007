from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MinistryOfDigitalAnnouncements(OCSpider):
    name = "MinistryOfDigitalAnnouncements"

    start_urls_names = {
        f"https://www.digital.gov.my/en-GB/siaran?page={pg}": "Announcements"
        for pg in range(13, 0, -1)
    }

    start_urls_with_no_pagination_set = {
        f"https://www.digital.gov.my/en-GB/siaran?page={pg}"
        for pg in range(13, 0, -1)
    }

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='grid grid-cols-1 gap-3 md:grid-cols-2 md:gap-6 lg:grid-cols-3 lg:px-6']//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//p[@class='text-2xl font-semibold text-foreground']//text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='richTextdiv']//text()").getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d %b %Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath("//div[@class='flex items-center gap-2 text-sm text-dim-500']//time//text()").get().strip()
        parts = date_str.split()
        if len(parts) == 3:
            day, month, year = parts
            month = month[:3]
            trimmed_date = f"{day} {month} {year}"
            return trimmed_date
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        # No next page to scrape
        return None