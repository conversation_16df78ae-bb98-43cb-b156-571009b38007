from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfIslandsTidoreCity(OCSpider):
    name = "ParliamentOfIslandsTidoreCity"

    start_urls_names = {
        "https://dprdkotatidorekepulauan.com/category/news/" : "Resources"
    }
    
    charset = "utf-8"

    article_data_map ={}  # Mapping date and title with child articles from start URL
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"
        
    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jayapura"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='fl-post-feed']//div[@itemscope='itemscope']"):
            url = article.xpath(".//div[@class='fl-post-text col-md-8']//h2//a/@href").get()
            title = article.xpath(".//div[@class='fl-post-text col-md-8']//h2//a//text()").get()
            date = article.xpath(".//div[@class='fl-post-meta text-muted']//i[@class='fa fa-calendar']/following-sibling::text()[1]").get()
            if url and title and date:
                full_url = url
                title = title.strip()
                clean_date = date.strip()
                self.article_data_map[full_url] = {"title": title, "date": clean_date}
                articles.append(full_url) 
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@id='wrapper-content']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@id='wrapper-content']//img//@src").getall()   
     
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        indonesian_months = {
                "Januari": "January",
                "Februari": "February",
                "Maret": "March",
                "April": "April",
                "Mei": "May",
                "Juni": "June",
                "Juli": "July",
                "Agustus": "August",
                "September": "September",
                "Oktober": "October",
                "November": "November",
                "Desember": "December"
            }
        for indo, eng in indonesian_months.items():
            if indo in date:
                date = date.replace(indo, eng)
                break
        return date
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return None