from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import unquote
import re

class CentralBankOfMalaysiaSpeeches(OCSpider):
    name = "CentralBankOfMalaysiaSpeeches"

    start_urls_names = {
       "https://www.bnm.gov.my/speeches-interviews/-/tag/speeches" : "Speeches" # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
       "https://www.bnm.gov.my/speeches-interviews/-/tag/speeches"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def get_articles(self, response) -> list: 
        mapping = {}
        articles = [] 
        containers = response.xpath('//tbody[@id="myTable"]//tr')
        for box in containers:
            link = box.xpath('./td[2]/p/a/@href').get()
            title = box.xpath('./td[2]/p/a/text()').get()
            date = box.xpath('./td[1]/p/text()').get()
            print(link,title,date)
            if link:
                normalized_link = unquote(link).rstrip('/')
                articles.append(normalized_link)
                mapping[normalized_link] = {
                        'title': title,
                        'pdf': normalized_link,  
                        'date': date
                    }
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ''
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url,{}).get('date')

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
  
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None