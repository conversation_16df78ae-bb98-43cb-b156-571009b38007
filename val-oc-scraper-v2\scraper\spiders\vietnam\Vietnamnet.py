from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import dateparser


class Vietnamnet(OCSpider):
    name = "Vietnamnet"
    
    start_urls_names = {
        "https://vietnamnet.vn/thoi-su": "Press Release",
    }

    # start_urls_with_no_pagination_set = {
    #     "https://vietnamnews.vn/politics-laws"
    # }

    charset = "utf-8"

    country = "Vietnam"
    
    @property
    def language(self): 
        return ""
    
    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        return response.xpath(
            '//div[@class="horizontalPost__main"]//h3//@href'
        ).getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath(
            '//h1[@class="content-detail-title"]//text()'
        ).get()

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath('//div[@class="maincontent main-content content-full-image content-full-image-v1"]//p//text() | //div[@class="maincontent main-content"]//p//text()').getall()
        )

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="maincontent main-content content-full-image content-full-image-v1"]//img//@src | //div[@class="maincontent main-content"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> Optional[str]:
        raw_date = response.xpath('//div[@class="bread-crumb-detail__time"]//text()').get()
        date_obj = dateparser.parse(raw_date, languages=['vi'])
        return date_obj.strftime("%Y-%m-%d")

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return response.xpath('//li[@class="pagination__list-item pagination-next block"]//a//@href').get()