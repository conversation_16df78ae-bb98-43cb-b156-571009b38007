from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from scraper.utils.helper import body_normalization

class PrimeMinisterOfficeSpeechArchive(OCSpider):
    name = "PrimeMinisterOfficeSpeechArchive"

    start_urls_names = {
        "https://www.pmo.gov.my/ucapan/?t={current_year}&b=no&m=s&p=muhyiddin" : "<PERSON><PERSON>"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        current_year = response.meta.get('current_year', 2021)
        hbp = HeadlessBrowserProxy()
        url = hbp.get_proxy(start_url.format(current_year=current_year),timeout=10000)
        yield scrapy.Request(
            url=url,
            callback=self.parse,
            dont_filter=True,
            meta={
                'start_url': start_url,
                'current_year': current_year,
                'url': url
            }
        )

    def get_articles(self, response) -> list:  
        articles=[]
        for id in response.xpath('//td[@class="senarai1"]//a/@href'):
            id = id.get()
            url=f"https://www.pmo.gov.my/ucapan/{id}"
            if url:
                articles.append(url)
        return articles
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('((//tr)[9]//th)[3]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//td[@id="inputText"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//td[@id="inputText"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response) -> str:
        return response.xpath('((//tr)[11]//th)[3]//text()').get()

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return response.xpath('//div[@class="wp-block-file"]//a/@href').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        current_year = response.meta.get('current_year')
        previous_year = str(int(current_year)-1)
        if response.status!=200:
            return None
        else :
            return previous_year
        
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get('start_url')
        previous_year = self.get_next_page(response)
        hbp = HeadlessBrowserProxy()
        if not previous_year:
            return
        proxied_url = hbp.get_proxy(start_url.format(current_year=previous_year),timeout=10000)
        yield scrapy.Request(
            url=proxied_url,
            callback=self.parse,
            dont_filter=True,
            meta={
                'start_url': start_url,
                'current_year': previous_year,
                'proxied_url': proxied_url
            }
        )