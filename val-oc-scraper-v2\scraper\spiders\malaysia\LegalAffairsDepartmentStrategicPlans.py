from scraper.OCSpider import OCSpider 
import re
from urllib.parse import unquote
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class LegalAffairsDepartmentStrategicPlans(OCSpider):
    name = "LegalAffairsDepartmentStrategicPlans"

    charset = "iso-8859-1"

    start_urls_names = {
        'https://www.bheuu.gov.my/en/penerbitan/pelan-strategik': 'publication'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.bheuu.gov.my/en/penerbitan/pelan-strategik'
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request( hbp.get_proxy( response.url , timeout=10000 ), callback=self.parse )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'justice'
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list =[]
        articles = response.xpath('//div[@class="col-sm-6 col-md-3"]')
        for article in articles:
            img = article.xpath('.//img/@src').get()
            title = article.xpath('.//h6//text()').get()
            url = article.xpath('.//a//@href').get()
            date_data = re.search(r"\d{4}", title)
            if date_data and title and url:
                date = date_data.group(0)
                child_article_list.append(url)
                self.article_url_title_date_mapping[url] = [title ,date , img]
        return child_article_list
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[0]

    def get_body(self, response) -> str:
        return ""

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[1]

    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
        return [self.article_url_title_date_mapping.get(unquote(response.url),[])[2]]
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None