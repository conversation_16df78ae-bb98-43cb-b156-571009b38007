from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
import re

class MinistryOfHomeAffairsArchive(OCSpider):
    name = "MinistryOfHomeAffairsArchive"

    start_urls_names = {
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/arkib-test/324-arkib-koleksi-ucapan": "News",
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/arkib-test/325-arkib-berita": "News",
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/arkib-test/324-arkib-koleksi-ucapan":"News",
        "https://www.moha.gov.my/index.php/ms/arkib-keratan-akhbar":"News",        
        "https://www.moha.gov.my/index.php/ms/arkib-2011":"News" , 
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/arkib-test/242-arkib-2016/keratan-akhbar-2016-1":"News",
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/arkib-test/287-arkib-2016/arkib-pencapaian-piagam-pelanggan-2016":"News",
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/arkib-test/254-arkib-2015":"News",
        "https://www.moha.gov.my/index.php/ms/arkib-2010":"News",
        "https://www.moha.gov.my/index.php/ms/arkib-2012":"News",
        "https://www.moha.gov.my/index.php/ms/arkib-2013":"News",
        "https://www.moha.gov.my/index.php/ms/arkib-2014":"News",
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/arkib-test/254-arkib-2015":"News"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       articles= []
       for article in response.xpath('//*[@id="adminForm"]/table/tbody//tr'):
           link = article.xpath(".//td[1]//@href").get()
           title= body_normalization(response.xpath('.//td[1]//a//text()').getall())
           date = article.xpath(".//td[2]//text()").get()
           
           if link:
               articles.append(link)
               self.article_data_map[link]={"date":date,"title" :title}
       return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('/html/body/div/div[5]/div/div/div[2]/div/p//text()  |//div/div/div[3]/div/div/p//text() | /html/body/div/div[5]/div/div/div[2]/div/div//text() | /html/body/div/div[5]/div/div/div[2]/div/table/tbody/tr/td//text()').getall())
        
    def get_images(self, response) -> list:
         return response.xpath("/html/body/div/div[5]/div/div/div[2]/div//img//@src").getall()
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        date =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        
        if date is None:
            title =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
            if not re.search(r"[,:]", title):
                parts = title.split(" ")
                if not parts:
                    return None  

                date = parts[-1]
               
                parsed_date = dateparser.parse(date, languages=['ms','en'])
                return parsed_date.strftime("%Y")
              
            else:
             date, title = title.split(":", 1)
             date = date.strip()
             date1=date.split(",")
             if len(date1)>1:
                date= date1[2]
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return response.xpath("/html/body/div/div[5]/div/div/div[2]/div/ul//a//@href | /html/body/div/div[5]/div/div/div[2]/div/p/a//@href").getall()
       
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
         link = response.xpath('(//a[@class="next"]/@href)').getall()
         if len(link)==1:
            return link[0]
         elif len(link)==2:
            return link[1]
         else:
          return None