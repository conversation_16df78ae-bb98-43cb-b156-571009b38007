from scraper.OCSpider import OCSpider
import dateparser
import re
import datetime

class MinistryofHealthPublications(OCSpider):
    name = "MinistryofHealthPublications"

    start_urls_names = {
        "https://www.moh.gov.my/index.php/pages/view/58?mid=19": "News"
    }

    start_urls_with_no_pagination_set = {
        "https://www.moh.gov.my/index.php/pages/view/58?mid=19"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:  
       articles= []
       titles= []
       for article in response.xpath('//*[@id="container_content"]/div/table/tbody/tr/td//a[contains(translate(@href, "PDF", "pdf"), ".pdf") or contains(@href, "drive.google.com")]'):
        link = article.xpath('./@href').get()
        title1 = (article.xpath('./ancestor::span[1]/text()').get()
          or article.xpath('.//text()').get())
        title = re.sub(r'[^A-Za-z0-9\s-]', '', title1)
        titles.append(title)
        if link:
               articles.append(link)
               self.article_data_map[link]={"title":title,"link":link}
       return articles
    
    def get_href(self, entry) -> str:
        return  entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""
  
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        title =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        title_split = title.split()
        for word in title_split:
            parsed_date = dateparser.parse(word, languages=['ms', 'en'])
            if parsed_date and parsed_date.year >= 1900:
              return parsed_date.strftime("%Y")
        curr_year = datetime.now().year  
        return curr_year.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None