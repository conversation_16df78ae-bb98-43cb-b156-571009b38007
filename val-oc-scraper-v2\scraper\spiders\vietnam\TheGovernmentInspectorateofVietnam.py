from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class TheGovernmentInspectorateofVietnam(OCSpider):
    name = "TheGovernmentInspectorateofVietnam"

    start_urls_names = {
        "https://thanhtra.gov.vn/web/guest/thanh-tra": "INSPECTOR NEWS"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:   
        return response.xpath('//h2[@class="h2-title-xx"]/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="change-size"]/text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='asset-content change-size']//p//text()").getall())

    def get_images(self, response) -> list:
        return [response.urljoin(url) for url in response.xpath('//div[contains(@class,"asset-content")]//img/@src').getall()]

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:  
        return response.xpath('normalize-space(//div[@class="news-meta"]//p[@class="date-post"])').get()
    
    def get_author(self, response) -> str:
        return ""
    
    def get_document_urls(self, response, entry=None) -> list[str]:
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response): 
        return response.xpath('(//ul[@class="pager lfr-pagination-buttons"]//li/a/@href)[3]').get()
