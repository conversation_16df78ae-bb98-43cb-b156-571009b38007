from scraper.OCSpider import <PERSON>CSpider 
from urllib.parse import unquote
from scraper.middlewares import HeadlessBrowserProxy
import re

class LegalAffairsDepartmentNewsClippings(OCSpider):
    name = "LegalAffairsDepartmentNewsClippings"

    start_urls_names = {
        'https://www.bheuu.gov.my/en/media/keratan-akhbar': 'newspaper clip'# pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.bheuu.gov.my/en/media/keratan-akhbar'
    }

    charset = "utf-8"

    HEADLESS_BROWSER_WAIT_TIME = 50000

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		}
	}

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'justice'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        articles = response.xpath('//tbody[@role="rowgroup"]//tr//a/@href').getall()
        full_url = ["https://www.bheuu.gov.my"+article for article in articles]
        return full_url
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title =  response.xpath('//h5[@class="text-uppercase mb-4"]//text()').get()
        return title or ""

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
       date_data = response.xpath('//div[@class="row"]//h6/text()').get()
       date = re.search(r'\d+/\d+/\d+', date_data)
       if date:
           return date.group(0)
       else:
           raise ValueError(f"Date not found for url: {response.url}")

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
       return response.xpath('//div[@class="row"]//img/@src').getall()
    
    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None