from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MinistryOfHomeAffairsSpeeches(OCSpider):
    name = "MinistryOfHomeAffairsSpeeches"

    start_urls_names = {
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/koleksi-ucapan1": "News"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.moha.gov.my/index.php/ms/maklumat-korporat22-4/koleksi-ucapan1"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//*[@id="adminForm"]/table/tbody//tr'):
            link = article.xpath(".//td[1]//@href").get()
            date = article.xpath(".//td[2]//text()").get()
            if link:
                full_link = response.urljoin(link)
                articles.append(full_link)
                self.article_data_map[full_link] = {
                    "date": date,
                }
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("/html/body/div/div[5]/div/div/div[2]/h3//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('/html/body/div/div[5]/div/div/div[2]//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date_str = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if not date_str:
            return ""
        parsed_date = dateparser.parse(date_str, languages=['ms', 'en'])
        if parsed_date:
            return parsed_date.strftime("%Y-%m-%d")
        else:
            return ""

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        relative_urls = response.xpath('/html/body/div/div[5]/div/div/div[2]/div/p//@href').getall()
        return [response.urljoin(url) for url in relative_urls]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None
