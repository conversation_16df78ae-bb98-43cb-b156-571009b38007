from scraper.OCSpider import OCSpider
from datetime import datetime
import re

class LandSurveyorsBoardCirculars(OCSpider):
    name = "LandSurveyorsBoardCirculars"
    
    start_urls_names = {
        "http://www.ljt.org.my/content/circular": "Circular"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "http://www.ljt.org.my/content/circular"
    }

    start_urls_with_no_pagination_set = {
        "http://www.ljt.org.my/content/circular"
    }

    country = "Malaysia"

    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//table[contains(@class, 'table-bordered')]//tr[position()>1]"):
            bil_text = article.xpath("./td[2]/p/text()").get()
            url = article.xpath("./td[3]//a/@href").get()
            title = article.xpath("./td[3]//a/text()").get()
            year = None
            if bil_text:
                match = re.search(r'(\d{4})', bil_text)
                if match:
                    year = match.group(1)
            if url and title and year:
                try:
                    date_obj = datetime.strptime(year, '%Y')
                    timestamp = int(date_obj.timestamp())
                except ValueError:
                    continue
                self.article_data_map[url] = {
                    "title": title.strip(),
                    "year": year,
                }
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("year", "")
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None