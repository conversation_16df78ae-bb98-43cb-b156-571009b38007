from scraper.OCSpider import OCSpider
import re

class MalaccaIslamicReligiousCouncilEnactments(OCSpider):
    name = "MalaccaIslamicReligiousCouncilEnactments"

    start_urls_names = {
        'https://www.maim.gov.my/index.php/ms/info-maim/informasi/enakmen-maim' : 'News Clippings',  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.maim.gov.my/index.php/ms/info-maim/informasi/enakmen-maim'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//tbody/tr[position() > 1]')
        for row in rows:
            title_parts = row.xpath('.//td[not(@rowspan) or position() > 1]//strong/text() | .//td[not(@rowspan) or position() > 1]//li/text()').getall()
            new_title = ' '.join([t.strip() for t in title_parts if t.strip()])
            if new_title:
                title = new_title
                year_match = re.search(r'\d{4}', title)
                year = year_match.group(0) if year_match else None
            doc_links = row.xpath('.//a/@href').getall()
            for link in doc_links:
                full_link = response.urljoin(link.strip())
                link = full_link.split('#')[0]
                if title and full_link and year:
                    self.article_title_pdf_mapping[link] = (title, full_link, year)
                    articles.append(link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url.split('#')[0], ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url.split('#')[0], ("", "", ""))[2]

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None)->list:
        return [self.article_title_pdf_mapping.get(response.url.split('#')[0], ("", "", ""))[1]]

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None
