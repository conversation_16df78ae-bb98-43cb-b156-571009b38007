from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
from urllib.parse import urljoin
from datetime import datetime
from scrapy import Selector

class TheSportsCulturesNewspaper(OCSpider):
    name = "TheSportsCulturesNewspaper"

    start_urls_names = {
        "https://thethaovanhoa.vn/bong-da-viet-nam.htm": "News"
    }

    start_urls_with_no_pagination_set = {}

    api_start_url = {
        "https://thethaovanhoa.vn/bong-da-viet-nam.htm": {
        "url": "https://thethaovanhoa.vn/ajax/zone-news/128-{page}.htm",
        }
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self,response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            self.logger.error(f"No API config found for {start_url}")
            return
        api_url = api_data["url"]
        current_page = response.meta.get("current_page", 1)
        url = api_url.format(page=current_page)
        yield scrapy.Request(
        url=url,
        method="GETS",   
        callback=self.parse,
        meta={
            "start_url": start_url,
            "api_data" : api_data,
            "current_page" : current_page
        }
    )
    
    articles_to_date = {}

    def get_articles(self, response) -> list:  
       return list(set(response.xpath('//li//a/@href').getall()))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@data-role="title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="col640 left"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return response.xpath('//span[@data-role="publishdate"]//text()').re_first("(\d{1,2}/\d{1,2}/\d{4})")

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = (int(response.meta.get("current_page"))+1)
        if response.status !=200:
            return 
        return str(next_page)
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        api_data = response.meta.get("api_data")
        next_page = self.get_next_page(response)
        if next_page:
            api_url = api_data["url"]
            url = api_url.format(page = next_page)
            yield scrapy.Request(
            url=url,
            method="GET",    
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "current_page" : next_page
            }
        )

