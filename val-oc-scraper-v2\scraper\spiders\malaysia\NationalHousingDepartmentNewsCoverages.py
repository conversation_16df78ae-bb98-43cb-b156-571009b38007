from typing import Optional
from scraper.OCSpider import OCSpider
from datetime import datetime
import re

class NationalHousingDepartmentNewsCoverages(OCSpider):
    name = "NationalHousingDepartmentNewsCoverages"

    start_urls_names = {
        "https://ehome.kpkt.gov.my/index.php/pages/view/679": "NATIONAL HOUSING POLICY (NRP)",
    }

    charset = "iso-8859-1"

    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Jakarta"

    def get_articles(self, response):
        if not self._is_html(response):
            return []
        article_mapping = self.extract_titles_and_urls(response)
        self.article_date_map = article_mapping
        articles = []
        for url in article_mapping:
            articles.append(url)
        self.synthetic_articles = [
            {"url": url, "meta": {"row_id": url.split("#")[-1]}}
            for url in article_mapping
        ]
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        for url, meta in self.article_date_map.items():
            if response.url in url:
                return meta.get("title", "")
        return ""

    def get_body(self, response) -> str:
        content_type = response.headers.get('Content-Type', b'').decode('utf-8', errors='ignore').lower()
        if 'application/pdf' in content_type:
            return "[PDF document content not parsed]"
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> Optional[str]:
        for url, meta in self.article_date_map.items():
            if response.url in url:
                raw_date = meta.get("date")
                break
        else:
            return None
        if not raw_date:
            return None
        raw_date = raw_date.split("(")[0].strip()
        raw_date = raw_date.replace("Febuari", "Februari")
        month_map = {
            "Januari": "January", "Februari": "February", "Mac": "March", "April": "April",
            "Mei": "May", "Jun": "June", "Julai": "July", "Ogos": "August", "September": "September",
            "Oktober": "October", "November": "November", "Disember": "December"
        }
        for bm, en in month_map.items():
            if bm in raw_date:
                raw_date = raw_date.replace(bm, en)
                break
        try:
            return datetime.strptime(raw_date, "%d %B %Y").strftime("%Y-%m-%d")
        except Exception:
            return None

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return None

    def extract_titles_and_urls(self, response) -> dict:
        self.article_date_map = {}
        rows = response.xpath('//table//tr')
        if not rows:
            return {}
        valid_date_regex = re.compile(
            r'(\d{1,2}\s*(Januari|Februari|Febuari|Mac|April|Mei|Jun|Julai|Ogos|September|Oktober|November|Disember)\s*\d{4})',
            re.IGNORECASE
        )
        article_map = {}
        for idx, row in enumerate(rows):
            cols = row.xpath('./td')
            if len(cols) < 2:
                continue
            raw_date = cols[0].xpath('string(.)').get(default='').strip()
            title = cols[1].xpath('string(.)').get(default='').strip()
            raw_date = raw_date.replace("Febuari", "Februari")
            if not valid_date_regex.search(raw_date):
                continue
            synthetic_url = f"{response.url}?row_id={idx + 1}"
            article_map[synthetic_url] = {
                "date": raw_date,
                "title": title
            }
        return article_map

    def _is_html(self, response) -> bool:
        content_type = response.headers.get('Content-Type', b'').decode('utf-8', errors='ignore').lower()
        return 'text/html' in content_type or 'application/xhtml+xml' in content_type
