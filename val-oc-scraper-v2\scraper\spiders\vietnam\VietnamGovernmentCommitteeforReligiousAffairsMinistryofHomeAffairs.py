from typing import Optional
import re
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization


class VietnamGovernmentCommitteeforReligiousAffairsMinistryofHomeAffairs(OCSpider):
    name = "VietnamGovernmentCommitteeforReligiousAffairsMinistryofHomeAffairs"
    
    start_urls_names = {
        "https://btgcp.gov.vn/tin-tuc-su-kien": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"
    
    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="media-body"]//span[@class="text-muted"]//a//@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h5[@class="text-semibold mb-5"]//a//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="show_content"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@id="show_content"]//p//img//@src').getall()
    
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_string = response.xpath(
            '//div[@id="tin-hoat-dong-cua-ban-ton-giao-chinh-phu"]//i[@class="text-muted"]/text()').get()
        if date_string:
            # Use regex to ensure only DD/MM/YYYY is extracted
            match = re.search(r'\d{2}/\d{2}/\d{4}', date_string)
            if match:
                return match.group(0)
        return None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return response.xpath('//div[@class="panel panel-flat"]//a[contains(text(), "Trang cuối")]//@href').get()
