from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization 

class MinistryofHealthCirculars(OCSpider):
    name = "MinistryofHealthCirculars"

    start_urls_names = {
        'https://www.moh.gov.my/index.php/database_stores/store_view/10?items=25&page=1': 'ministry'
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
      return response.xpath('//table//td[@class="thin"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//table[@class="dataTableDetail"]//tr/td//text()').get()

    def get_body(self, response) -> str:
        body = response.xpath('//table[@class="dataTableDetail"]//tr//text()').getall()
        body_stripped = body[body.index('No. Pekeliling') : body.index('Tarikh')]
        return body_normalization(body_stripped)
       
    def get_date(self, response) -> str:
        return response.xpath('//table[@class="dataTableDetail"]//tr//td//text()').getall()[-1:][0][:10]

    def date_format(self) -> str:
        return "%d-%m-%Y"

    def get_images(self, response) -> list[str]:
       return []
    
    def get_document_urls(self, response, entry=None):
        urls = response.xpath('//div[@class="attachment"]//li/a/@href').getall()
        if urls:
            return [response.urljoin(url) for url in urls]
        else:
            return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response):
        return response.xpath('//a[text()="Next"]/@href').get()