import scrapy
import json
from datetime import datetime
from typing import Union
from parsel import Selector
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class NewHampshireAttorneyGeneral(OCSpider):
    name = 'NewHampshireAttorneyGeneral'

    country = "US"

    charset = "utf-8"

    HEADLESS_BROWSER_WAIT_TIME = 30000

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 2,
    }

    start_urls_names = {
        'https://www.doj.nh.gov/news/': "News"
    }

    api_start_url = {
        'https://www.doj.nh.gov/news/': {
            'url': 'https://www.doj.nh.gov/content/api/news',
            'headers': {
                'Accept': 'application/json'
            },
            'params': {
                'q': '%40field_press_release_category%3D%7C226',
                'textsearch': '',
                'sort': 'field_date%7Cdesc%7CALLOW_NULLS',
                'view': 'list',
                'page': 0,
                'size': 10
            }
        }
    }

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "US/Eastern"

    @property
    def language(self):
        return "English"

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        current_page = response.meta.get("current_page", 1)
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            return
        params = api_data['params'].copy()
        params['page'] = current_page - 1
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        api_url = f"{api_data['url']}?{query_string}"
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers=api_data['headers'],
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data['url'],
                "current_page": current_page
            },
            dont_filter=True
        )

    # def parse_intermediate(self, response):
    #     start_url = response.meta.get('start_url')
    #     current_page = response.meta.get("current_page", 1)

    #     # ✅ Parse articles from current page
    #     for url in self.get_articles(response):
    #         yield scrapy.Request(
    #             url=url,
    #             callback=self.parse_article,
    #             meta={"start_url": start_url}
    #         )

    #     # ✅ Handle pagination
    #     yield from self.go_to_next_page(response, start_url=start_url, current_page=current_page)


    def get_articles(self, response) -> list:
        try:
            if response.text.strip().startswith("<html>"):
                selector = Selector(response.text)
                json_text = selector.xpath('//pre/text()').get()
            else:
                json_text = response.text
            data = json.loads(json_text)
            results = data.get("data", [])
            valid_articles = []
            for item in results:
                href = Selector(text=item.get("list_content", "")).xpath('//a/@href').get()
                if href:
                    valid_articles.append(f"https://www.doj.nh.gov{href}")
            return valid_articles
        except Exception as e:
            self.logger.error(f"Failed to extract articles: {e}")
            self.logger.debug(f"Response snippet: {response.text[:1000]}")
            return []

    # def get_articles(self, response) -> list:
    #     try:
    #         # Defensive check: is it JSON?
    #         if response.headers.get("Content-Type", b"").decode().startswith("application/json"):
    #             data = json.loads(response.text)
    #         else:
    #             # Try extracting pre > text() content if wrapped
    #             selector = Selector(response.text)
    #             json_text = selector.xpath('//pre/text()').get()
    #             if not json_text:
    #                 raise ValueError("Non-JSON response received from API.")
    #             data = json.loads(json_text)

    #         results = data.get("data", [])
    #         valid_articles = []
    #         for item in results:
    #             href = Selector(text=item.get("list_content", "")).xpath('//a/@href').get()
    #             if href:
    #                 valid_articles.append(f"https://www.doj.nh.gov{href}")
    #         return valid_articles

    #     except Exception as e:
    #         self.logger.error(f"Failed to extract articles: {e}")
    #         self.logger.debug(f"Response snippet: {response.text[:1000]}")
    #         return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title = response.xpath("//h1/text()").get()
        return title.strip() if title else ""

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath("//div[contains(@class,'field--name-body')]//p/text()").getall()
        )

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        raw_date = response.xpath("//div[@class='date']//span[@class='date-label']/following-sibling::text()").get()
        if raw_date:
            cleaned_date = raw_date.strip()
            try:
                return datetime.strptime(cleaned_date, self.date_format()).strftime(self.date_format())
            except ValueError:
                self.logger.warning(f"Invalid date format at {response.url}: '{cleaned_date}'")
        self.logger.warning(f"Date missing at {response.url}")
        return ""

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response, current_page) -> Union[None, int]:
        try:
            if response.text.strip().startswith("<html>"):
                selector = Selector(response.text)
                json_text = selector.xpath('//pre/text()').get()
            else:
                json_text = response.text

            data = json.loads(json_text)
            results = data.get("data", [])
            if len(results) == 10:
                return current_page + 1
        except Exception as e:
            self.logger.warning(f"Pagination detection failed: {e}")
        return None

    def go_to_next_page(self, response, start_url, current_page=1):
        next_page = self.get_next_page(response, current_page)
        if next_page is None:
            return
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            return
        params = api_data['params'].copy()
        params['page'] = next_page - 1
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        api_url = f"{api_data['url']}?{query_string}"
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers=api_data["headers"],
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "current_page": next_page,
                "api_url": api_data['url']
            },
            dont_filter=True
        )
