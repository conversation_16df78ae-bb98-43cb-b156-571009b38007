
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from typing import Optional
from datetime import datetime
import re
import scrapy
import urllib.parse

class CapitalMilitaryDefenseNewspaper(OCSpider):
    name = "CapitalMilitaryDefenseNewspaper"

    start_urls_names = {
        "http://quocphongthudo.vn/thoi-su-chinh-tri": "News",
        }
    

    api_start_url = {
        "http://quocphongthudo.vn/thoi-su-chinh-tri": {
            'url': "https://quocphongthudo.vn/?module=Content.Listing&moduleId=25&cmd=redraw&site=2001606&url_mode=rewrite&submitFormId=25&moduleId=25&page=Article.News.list&site=2001606",
            "payload" : {
                "layout" : "Article.List.list",
                "orderBy":"publishTime DESC",
                "type":"Article.News",
                "pageNo":"1",
                "itemsPerPage":"8",
                "hasInnerActions":"0",
                "hasHeaderInnerActions":"0",
                "service": "Content.Article.selectAll",
                "widgetCode": "Article",
                "parentId": "101734700",
                "detailLayout":"Article.Detail.listType5",
                "_entityTitle": "Tin tức",
                "categoryId" :"101734700",               
                "_entityTitleLCF":"tin tức",
                "imageSizeRatio":"3:2",
                "inheritBlockParentId":"B5ede0fe9b13e20",
                 "page" :"Article.News.list",
                "modulePosition" :"17",
                 "moduleParentId":"14",
                 "_startTime":"1755766302788",
                 "_t" :"1755766317063"
            }
        }
    }

    

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["pageNo"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
        
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//h3[@class="entry-title"]//a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="block-core-a5"]//*[self::p or self::h2 or self::h3 or self::h4 or self::h5 or self::h6]//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//h1//img//@src | //figure//a//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('(//span[@class="post-date left"]//text())[1]').get()
        date_obj = dateparser.parse(date, languages=['vi'])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        
        return int(response.meta.get("current_page")) + 1

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_url.get(start_url)
        if not api_data:
           self.logger.error("API data not found for start_url")
           return

        api_url = api_data["url"]
        next_page = self.get_next_page(response, current_page)
        

        if next_page:
          payload = response.meta.get("payload", {}).copy()
          payload["pageNo"] = str(next_page)

          yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            formdata=payload,
            callback=self.parse_intermediate,
            meta={"current_page": next_page, "start_url": start_url, "payload": payload},
            dont_filter=True,
           )
        else:
         return None
         
