from scraper.OCSpider import OCSpider
import dateparser

class LandPublicTransportAuthorityGuidelines(OCSpider):
    name = "LandPublicTransportAuthorityGuidelines"

    start_urls_names = {
        "https://www.apad.gov.my/sumber-maklumat1/garis-panduan?category[0]=7&category_children=1": "News"
    }

    start_urls_with_no_pagination_set = {}
    
    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    month_map = {
    'Jan': 'Jan', 'Feb': 'Feb', 'Mac': 'Mar', 'Apr': 'Apr', 'Mei': 'May', 'Jun': 'Jun',
    'Jul': 'Jul', 'Ogs': 'Aug', 'Sept': 'Sep', 'Okt': 'Oct', 'Nov': 'Nov', 'Dis': 'Dec'
    }
     
    def get_articles(self, response) -> list:
        articles= []
        for article in response.xpath('//table//tr'):
            link = article.xpath(".//td[6]//a//@href").get()
            date = article.xpath('.//td[4]//time//text()').get()
            title = article.xpath('.//td[2]//span//text()').get()
            if link:
                articles.append(link)
                self.article_data_map[link]={
                        "title":title,"link":link,"date":date
                    }
        return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date_part = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        for malay, eng in self.month_map.items():
         if malay in date_part:
            raw_date = date_part.replace(malay, eng)
            break
        parsed_date = dateparser.parse(raw_date, languages=['en'])
        return parsed_date.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath("//a[contains(text(), '»')]//@href").get() 