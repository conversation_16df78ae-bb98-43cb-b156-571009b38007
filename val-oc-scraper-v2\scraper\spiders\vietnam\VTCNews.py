from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization
import scrapy

class VTCNews(OCSpider):
    name = "VTCNews"

    start_urls_names = {
        'https://vtcnews.vn/thoi-su-28.html': 'news'
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//article//figure//a//@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="font28 bold lh-1-3"]//text()').get() or response.xpath('//h1[@class="mb-title-1 fontz1 "]//text()').get() or  ''
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="edittor-content box-cont mt15 clearfix "]//p//text()').getall() or response.xpath('//div[@class="content-wrapper fontset18 gray-31 lh-1-5"]//p//text()').getall())
        
    def get_date(self, response) -> str:
        date_data = response.xpath('//span[@class="time-update mr10 align-super"]//text()').get() or response.xpath('//span[@class="time-update mr10 "]//text()').get()
        date = re.search(r"\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}", date_data or '')
        if date:
            return date.group(0)
        else:
            ValueError(f"Date not found for url = {response.url}")
       
    def date_format(self) -> str:
        return "%d/%m/%Y %H:%M:%S"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="edittor-content box-cont mt15 clearfix "]//img//@data-src').getall() or response.xpath('//div[@class="content-wrapper fontset18 gray-31 lh-1-5"]//img//@data-src').getall()

    def get_document_urls(self, response, entry=None)->list:
        return []
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        page_no = response.meta.get('page_no' , 1)
        return page_no
    
    def go_to_next_page(self, response, start_url, current_page=None):
        page_no = self.get_next_page(response)
        page_no += 1
        if page_no:
            start_url = response.meta.get('start_url')
            yield scrapy.Request(
                url = f"https://vtcnews.vn/thoi-su-28/trang-{page_no}.html" ,
                method= "GET",
                callback = self.parse, 
                meta= {
                    'page_no' : page_no ,
                    'start_url' : start_url
                }
            )
        else:
            yield None