from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class BaRiaVungTauProvincesPortalOfVietnam(OCSpider):
    name = "BaRiaVungTauProvincesPortalOfVietnam"

    start_urls_names = {
        "https://baria-vungtau.gov.vn/sphere/baria/vungtau/page/tin-tuc.cpx?group=THONGBAO&category=Th%C3%B4ng%20b%C3%A1o%20chung&pageSize=15&pageNumber=0": "News",
        }
    
    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}

    custom_settings = {
           "DOWNLOADER_MIDDLEWARES": {
               'scraper.middlewares.HeadlessBrowserProxy': 3000
           },
           "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
       }
    
    HEADLESS_BROWSER_WAIT_TIME = 3000
     
    def get_articles(self, response) -> list:  
        return response.xpath('//*[@id="main-list-news"]/div/h4/a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h4//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content"]//p//text()').getall())
   
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="content"]//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//span[@class="time"]//text()').get()
        clean_text = date.strip()
        date_obj = dateparser.parse(clean_text, languages=['vi'])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[normalize-space(text())="Tiếp theo"]//@href').get()
