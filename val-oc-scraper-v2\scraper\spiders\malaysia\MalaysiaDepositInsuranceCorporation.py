from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MalaysiaDepositInsuranceCorporation(OCSpider):
    name = "MalaysiaDepositInsuranceCorporation"
    
    start_urls_names = {
        "https://www.pidm.gov.my/my/for-member-institutions/legislation/act": "Legislation Acts",
        "https://www.pidm.gov.my/my/for-member-institutions/legislation/regulations": "Regulations",
        "https://www.pidm.gov.my/my/for-member-institutions/legislation/orders": "Orders",
        "https://www.pidm.gov.my/my/for-member-institutions/legislation/rules": "Rules",
        "https://www.pidm.gov.my/my/for-member-institutions/legislation/guidelines": "Guidelines",
        "https://www.pidm.gov.my/my/for-member-institutions/legislation/directives": "Directives",
        "https://www.pidm.gov.my/my/for-member-institutions/legislation/notes": "Notes",
        "https://www.pidm.gov.my/my/for-member-institutions/consultation-papers": "Consultation-Papers",
        "https://www.pidm.gov.my/my/more/media-release": "Media-Release"
        }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map ={}

    def get_articles(self, response) -> list:
        articles=[]
        if 'media-release' not in response.url:
            for article in response.xpath("//div[@class='listing-content']//div[@class='listing-box']"): 
                url = article.xpath(".//div[@class='listing-right']//a//@href").get()
                title=article.xpath(".//div[@class='listing-title']//text()").get()
                date =article.xpath(".//div[@class='listing-right']//span[@class='postDate']//text()").get()
                if url and title and date:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    self.article_data_map[full_url]={"title": title, "date": date}
                articles.append(full_url)
            return articles
        else:
            return response.xpath("//div[@class='col-6 col-sm-4 col-lg-3']//a[@class='postCard']//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        if 'siaran-media' in response.url or 'media-release' in response.url: 
            return response.xpath("//div[@class='career-heading']//h2//text()").get()
        else:
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str: 
        if 'siaran-media' in response.url or 'media-release' in response.url: 
            return body_normalization(response.xpath("//div[@class='career-detail']//text()").getall())
        else:
            return ""
    
    def get_images(self, response) -> list:
        if 'siaran-media' in response.url or 'media-release' in response.url: 
            return response.xpath("//div[@class='generalBanner']//img//@src").getall()
        else:
            return ""
    
    def date_format(self) -> str:
        return "%b %d %Y"
    
    def get_date(self, response) -> Optional[str]:
        if 'siaran-media' in response.url or 'media-release' in response.url:
            date =response.xpath("//div[@class='postCard-wrapper']//span[@class='postDate']//text()").get()
            date=date.replace(",","")
            parsed_date = dateparser.parse(date, languages=['ms'])
            return parsed_date.strftime("%b %d %Y")  
        else:
            date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
            parsed_date = dateparser.parse(date, languages=['ms'])
            return parsed_date.strftime("%b %d %Y")
    
    def get_document_urls(self, response, entry=None) -> list:
        if '.pdf' in response.url or '.xls' in response.url:
            return [response.url]
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if 'media-release' not in response.url:
            return f'{response.url}?filter=arc'
        return response.xpath("//ul//li[@class='page-item active']/following-sibling::li[1]//a/@href").get()