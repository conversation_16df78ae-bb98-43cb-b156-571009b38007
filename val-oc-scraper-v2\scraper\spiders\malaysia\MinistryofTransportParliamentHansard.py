from scraper.OCSpider import OCSpider
import scrapy
from urllib.parse import urlencode
from bs4 import BeautifulSoup

class MinistryofTransportParliamentHansard(OCSpider):
    name = "MinistryofTransportParliamentHansard"

    start_urls_names = {
        'https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-pertama':'First Meeting',
        'https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-kedua':'Second Meeting',
        'https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-ketiga':'Third Meeting',
        'https://www.mot.gov.my/en/media/parliament/2024/dn-mesy-ketiga':'2024 Third Meeting',
        'https://www.mot.gov.my/my/media/parliament/2023/dr-mesy-ketiga':'Third Meeting',
        'https://www.mot.gov.my/my/media/parliament/2022/dr-mesyuarat-pertama':'First Meeting',
        'https://www.mot.gov.my/my/media/parliament/2021/dr-mesyuarat-pertama':"First Meeting",
        'https://www.mot.gov.my/en/media/parliament/2020/dr-mesyuarat-kedua':'Second Meeting',
        'https://www.mot.gov.my/en/media/parliament/2020/dn-mesyuarat-ketiga':'Third Meeting',
        'https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-pertama':'First Meeting',
        'https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-kedua':'Second Meeting',
        'https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-ketiga':'Third Meeting',
        'https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-pertama':'First Meeting',
        'https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-kedua':'Second Meeting',
        'https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-ketiga':'Third Meeting',
        'https://www.mot.gov.my/en/media/parliament/2018/dr-mesyuarat-pertama':'First Meeting',
        'https://www.mot.gov.my/en/media/parliament/2018/dr-mesyuarat-kedua':'Second Meeting',
        'https://www.mot.gov.my/en/media/parliament/2018/dn-mesyuarat-pertama':'First Meeting',
        'https://www.mot.gov.my/en/media/parliament/2018/dn-mesyuarat-kedua':'Second Meeting',
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    api_start_urls = {
        'https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-pertama' : {
            "params" : {
                "List" : "{8FE2BACB-EAB3-4EC4-BE01-E135ABE251E4}",
                "View" : "{F1E1FA5F-E7F5-4D97-B0E1-C6C14538D970}",
                "ViewCount" : "59",
                "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-pertama",
            },
            "initial_p_ID" : 159,
            "initial_PageFirstRow" : 1
        },
        'https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-kedua' : {
            'params' :{
                "List" : "{8FE2BACB-EAB3-4EC4-BE01-E135ABE251E4}",
                "View" : "{8EAACFF8-A757-481A-ADAF-D18A2E5B5992}",
                "ViewCount" : "2",
                "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-kedua",
            },
            "initial_p_ID" : 5,
            "initial_PageFirstRow" : 1
        },
        'https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-ketiga' : {
            'params' : {
                "List" : "{8FE2BACB-EAB3-4EC4-BE01-E135ABE251E4}",
                "View" : "{3986FB94-78BB-4AD9-892B-A59879C8138D}",
                "ViewCount" : "4",
                "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2024/dr-mesy-ketiga",
                },
            "initial_p_ID" : 227,
            "initial_PageFirstRow" : 1
        },
        "https://www.mot.gov.my/en/media/parliament/2024/dn-mesy-ketiga" : {
            'params' : {
                "List" : "{AF095F8D-A2FB-4D09-8D12-AA117287F868}",
                "View" : "{494C4ED4-0A43-40C5-8B2B-FFE18F0BC5D7}",
                "ViewCount" : 7,
                "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2024/dn-mesy-ketiga",
            },
            "initial_p_ID" : 3,
            "initial_PageFirstRow" : 1
        },
        "https://www.mot.gov.my/my/media/parliament/2023/dr-mesy-ketiga" : {
                'params' : {
                "List" : "{77A72AC1-E864-478F-8AC1-DC2970B33A46}",
                "View" : "{4924EE35-706B-4346-8CFE-A99FE7A9A90A}",
                "ViewCount" : 61,
                "ListViewPageUrl" : "https://www.mot.gov.my/my/media/parliament/2023/dr-mesy-ketiga",
            },
            "initial_p_ID" : 36,
            "initial_PageFirstRow" : 1
        },
        "https://www.mot.gov.my/my/media/parliament/2022/dr-mesyuarat-pertama" : {
            'params' : {
                "List" : "{0D7D28C4-8A0C-4E7F-A7AD-0459B495C160}",
                "View" : "{83FE3EA4-63A0-47C6-A041-9913D79BF9EE}",
                "ViewCount" : 9,
                "ListViewPageUrl" : "https://www.mot.gov.my/my/media/parliament/2022/dr-mesyuarat-pertama",
                "p_Tarikh_x0020_Persidangan" : "20220314 16:00:00",
            },
             "initial_p_ID" : -17,
            "initial_PageFirstRow" : -16
        },
        "https://www.mot.gov.my/my/media/parliament/2021/dr-mesyuarat-pertama" : {
            'params' : {
                "List" : "{F71A3258-C231-4B95-9B19-7785174758C2}",
                "View" : "{8AE76676-03A3-428C-A4F2-24F487111DC6}",
                "ViewCount" : 6,
                "ListViewPageUrl" : "https://www.mot.gov.my/my/media/parliament/2021/dr-mesyuarat-pertama",
            },
             "initial_p_ID" : 3,
            "initial_PageFirstRow" : 1
        },
        "https://www.mot.gov.my/en/media/parliament/2020/dr-mesyuarat-kedua" : {
            'params' : {
                "List" : "{97F7A9F3-CC7A-486D-81AC-AFE5CE04C3AE}",
                "View" : "{A6AD113F-7F01-4645-894B-22AA889D232F}",
                "ViewCount" : 19,
                "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2020/dr-mesyuarat-kedua",
                "p_Tarikh_x0020_persidangan" : "20200824 16:00:00",
            },
             "initial_p_ID" : 192,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2020/dn-mesyuarat-ketiga" : {
            'params' : {
                    "List" : "{820CBB43-6224-4F6E-93C7-3E188C99BD23}",
                    "View" : "{9E54F052-F3C3-498A-92C9-8C21F13ADE56}",
                    "ViewCount" : 86,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2020/dn-mesyuarat-ketiga",
            },
             "initial_p_ID" : 26,
            "initial_PageFirstRow" : 1
        },
        "https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-pertama" : {
            'params' : {
                    "List": "{348EB824-B307-44B3-9C2C-044E7F56A0CF}",
                    "View": "{D0240633-E1A2-4E87-9C3A-D636411D2339}",
                    "ViewCount": 49,
                    "ListViewPageUrl": "https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-pertama",
                    "p_Tarikh_x0020_persidangan": "20190401 16:00:00",
            },
            "initial_p_ID" : 4,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-kedua" : {
            'params' : {
                    "List" : "{348EB824-B307-44B3-9C2C-044E7F56A0CF}",
                    "View" : "{814ED0DA-EE8A-4A0B-97F0-E416562FB729}",
                    "ViewCount" : 18,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-kedua",
                    "p_Tarikh_x0020_persidangan" : "20190709 16:00:00",
            },
            "initial_p_ID" : 47,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-ketiga" : {
            'params' : {
                    "List" : "{348EB824-B307-44B3-9C2C-044E7F56A0CF}",
                    "View" : "{92E6AAEB-1BE3-4C9F-8758-2BA17E3E1D85}",
                    "ViewCount" : 43,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2019/dr-mesyuarat-ketiga",
                    "p_Tarikh_x0020_persidangan" : "20191203 16:00:00",
            },
            "initial_p_ID" : 131,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-pertama" : {
            'params' : {
                    "List" : "{38E8455F-E644-41A4-B941-6C31ECE30192}",
                    "View" : "{38186DEB-E670-4604-9B71-3EE51961C65F}",
                    "ViewCount" : 11,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-pertama",
                    "p_Tarikh_x0020_Persidangan" : "20190505 16:00:00",
            },
            "initial_p_ID": -16,
            "initial_PageFirstRow": -14
        },
        "https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-kedua" : {
            'params' : {
                     "List" : "{38E8455F-E644-41A4-B941-6C31ECE30192}",
                    "View" : "{C2B007F2-9C2B-459F-8420-B84AD404FFBC}",
                    "ViewCount" : 68,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-kedua",
                    "p_Tarikh_x0020_Persidangan" : "20190724 16:00:00",
            },
            "initial_p_ID" : 28,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-ketiga" : {
            'params' : {
                     "List" : "{38E8455F-E644-41A4-B941-6C31ECE30192}",
                    "View" : "{3C962D5A-D69D-4BBC-9B64-EA3536DC3FEB}",
                    "ViewCount" : 18,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2019/dn-mesyuarat-ketiga",
                    "p_Tarikh_x0020_Persidangan" : "20191211 16:00:00",
            },
            "initial_p_ID" : 47,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2018/dr-mesyuarat-pertama" : {
            'params' : {
                    "List" : "{B76B930A-1D8E-4F59-A0FE-595550C3BA7C}",
                    "View" : "{919BC022-9DBF-4067-A6DD-9505E0247B1E}",
                    "ViewCount" : 70,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2018/dr-mesyuarat-pertama",
                    "p_Tarikh_x0020_Persidangan" : "20180815 16:00:00",
            },
            "initial_p_ID" : 40,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2018/dr-mesyuarat-kedua" : {
            'params' : {
                    "List" : "{B76B930A-1D8E-4F59-A0FE-595550C3BA7C}",
                    "View" : "{1AEE0781-C991-446F-B1DC-4045D3E91C78}",
                    "ViewCount" : 30,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2018/dr-mesyuarat-kedua",
                    "p_Tarikh_x0020_Persidangan" : "20181128 16:00:00",
            },
            "initial_p_ID" : 77,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2018/dn-mesyuarat-pertama" : {
            'params' : {
                    "List" : "{1A445EB8-775A-4274-AADB-FB25A987E7F3}",
                    "View" : "{803C5C2C-7295-47FA-A588-36C91F15D6DE}",
                    "ViewCount" : 84,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2018/dn-mesyuarat-pertama",
                    "p_Tarikh_x0020_Persidangan" : "20180903 16:00:00",
            },
            "initial_p_ID" : -25,
            "initial_PageFirstRow" : -14
        },
        "https://www.mot.gov.my/en/media/parliament/2018/dn-mesyuarat-kedua" : {
            'params' : {
                    "List" : "{1A445EB8-775A-4274-AADB-FB25A987E7F3}",
                    "View" : "{7B7E380A-87A9-4B93-8735-B54B8E45696C}",
                    "ViewCount" : 15,
                    "ListViewPageUrl" : "https://www.mot.gov.my/en/media/parliament/2018/dn-mesyuarat-kedua",
                    "p_Tarikh_x0020_Persidangan" : "20181216 16:00:00",
            },
            "initial_p_ID" : 13,
            "initial_PageFirstRow" : -14
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_urls.get(start_url,{})
        if api_data:
            # api_url and some parameters were same for all api calls
            api_url ='https://www.mot.gov.my/my/_layouts/15/inplview.aspx'
            params = api_data["params"]
            params['IsXslView']='TRUE'
            params['IsCSR']='TRUE'
            params['Paged']='TRUE'
            yield scrapy.Request(
                url=f"{api_url}?{urlencode(params)}",
                method="POST",
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "params": params,
                    "p_ID": api_data["initial_p_ID"],
                    "PageFirstRow": api_data["initial_PageFirstRow"]
                }
            )

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    articles_url_date_docUrl_mapping={}

    def get_articles(self, response) -> list:
        articles =[]
        data = response.json()
        for data in data.get("Row",):
            date = data.get('Tarikh_x0020_Persidangan',) or data.get('Tarikh_x0020_persidangan',)
            soup = BeautifulSoup(data.get('Soalan'), "html.parser")
            title = soup.get_text(strip=True, separator=" ")
            url = response.urljoin(data.get("Papar_x0020_Jawapan",))
            if url and title and date:
                articles.append(url)
                self.articles_url_date_docUrl_mapping[url]=[title,date]
        return list(set(articles))

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.articles_url_date_docUrl_mapping.get(response.url)[0]

    def get_body(self, response) -> str:
        return ""       

    def get_date(self, response) -> str: 
        article_date = self.articles_url_date_docUrl_mapping.get(response.url, None)[1]
        if article_date:
            return article_date
        else:
            self.logger.error(f'No date found for URL: {response.url}')
            return None
    
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.url

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        current_p_ID = int(response.meta.get("p_ID", 0))
        current_PageFirstRow = int(response.meta.get("PageFirstRow", 1))
        next_p_ID = str(current_p_ID + 30)
        next_PageFirstRow = str(current_PageFirstRow + 30)
        return next_p_ID, next_PageFirstRow

    
    def go_to_next_page(self, response, start_url, current_page=None):
        try:
            api_data = self.api_start_urls.get(start_url)
            api_url = 'https://www.mot.gov.my/my/_layouts/15/inplview.aspx'
            next_p_ID,next_PageFirstRow = self.get_next_page(response)
            params = response.meta["params"]
            params['p_ID']=next_p_ID
            params['PageFirstRow']= next_PageFirstRow

            yield scrapy.Request(
                url =f"{api_url}?{urlencode(params)}",
                method = "POST",
                callback = self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": 'https://www.mot.gov.my/my/_layouts/15/inplview.aspx',
                    "params": params,
                    "p_ID":int(params["p_ID"]),
                    "PageFirstRow":int(params["PageFirstRow"])
                },
            )
        except Exception as e:
            self.logger.error(f"Pagination failed: {e}", exc_info=True)
            yield None
        
            
