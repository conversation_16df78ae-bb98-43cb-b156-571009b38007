from scraper.OCSpider import OCSpider 
import re
from urllib.parse import unquote

class EducationServiceCommissionAnnouncement(OCSpider):
    name = "EducationServiceCommissionAnnouncement"

    start_urls_names = {
        'https://www.spp.gov.my/sumber/penerbitan/laporan-tahunan': 'ministry'
    }

    start_urls_with_no_pagination_set = {
        'https://www.spp.gov.my/sumber/penerbitan/laporan-tahunan'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list =[]
        articles = response.xpath('//div[@class="g-block size-33 penerbitan"]')
        for article in articles:
            url = article.xpath('.//div[@class="center"]/a/@href').get()
            full_url = response.urljoin(url)
            title = article.xpath('.//div[@class="alert alert-warning center"]/text()').get()
            date_data = article.xpath('.//div[@class="alert alert-warning center"]/text()').get()
            pattern =  r"\b\d{4}\b"
            date = re.search(pattern, date_data).group()
            if date and full_url and title:
                child_article_list.append(full_url)
                self.article_url_title_date_mapping[full_url]=[title, date]
        return child_article_list
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[0]

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[1]

    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.url
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None