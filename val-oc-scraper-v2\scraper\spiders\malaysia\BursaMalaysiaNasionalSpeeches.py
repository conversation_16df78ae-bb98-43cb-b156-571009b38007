from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import urljoin, unquote

class BursaMalaysiaNasionalSpeeches(OCSpider):
    name = "BursaMalaysiaNasionalSpeeches"

    start_urls_names = {
        "https://www.bursamalaysia.com/about_bursa/media_centre/speeches" : "Speeches"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "stock_exchange"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page",1)
        url = f"{start_url}?page={current_page}"
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(url, timeout=10000),
            callback=self.parse,
            meta = {
                "start_url" : start_url,
                "current_page" : current_page
            }
        )

    def get_articles(self, response) -> list: 
        mapping = {}
        base_url = "https://www.bursamalaysia.com"
        articles = [] 
        containers = response.xpath('//table[@class=" table media-releases-table table-striped text-center text-default cardTable dataTable no-footer stacktable small-only"]')
        for box in containers:
            link = box.xpath('.//a/@href').get()
            title = box.xpath('.//a/text()').get().strip()
            date = box.xpath('.//td[contains(@class, "st-val") and contains(@class, "text-center")]/text()').get()
            if link:
                full_url = urljoin(base_url,link)
                normalized_link = unquote(full_url).rstrip('/')
                articles.append(normalized_link)
                mapping[normalized_link] = {
                    'title': title,
                    'pdf': full_url,   
                    'date': date
                }
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title=None
        normalized_url = unquote(response.url).rstrip('/')
        if 'text/html' not in response.headers.get('Content-Type', b'').decode():
            title = self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")
        return title
    
    def get_body(self, response) -> str:
        if 'text/html' not in response.headers.get('Content-Type', b'').decode():
            return ''
        
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> str:
        content_type = response.headers.get('Content-Type', b'').decode()
        date = None
        normalized_url = unquote(response.url).rstrip('/')
        if 'text/html' not in content_type:
            date = self.article_to_pdf_mapping.get(normalized_url, {}).get("date")
        return date.strip() if date else ""

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        content_type = response.headers.get('Content-Type', b'').decode()
        normalized_url = unquote(response.url).rstrip('/')
        pdf=[]
        if 'text/html' not in content_type:
            pdf = self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
            return [pdf]
        else :
            return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        current_page = response.meta.get("current_page")
        next_page = str(int(current_page) + 1)
        if response.status ==200:
            return next_page
        else :
            return None
        
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        next_page = self.get_next_page(response)
        if next_page:
            url = f"{start_url}?page={next_page}"
            hbp = HeadlessBrowserProxy()
            yield scrapy.Request(
                hbp.get_proxy(url, timeout=10000),
                callback=self.parse,
                meta = {
                    "start_url" : start_url,
                    "current_page" : next_page
                }
            )