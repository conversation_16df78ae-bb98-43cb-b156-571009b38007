import json
from urllib.parse import urlencode
from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization
from typing import Optional

class Muyuanfoods(OCSpider):
    name="Muyuanfoods"

    start_urls_names={
        "https://www.muyuanfoods.com/#/companyDynamic" : "学会动态", 
        # "https://www.muyuanfoods.com/#/announcement" : "媒体信息",
    }
    
    charset = "utf-8"

    api_start_urls = {
        "https://www.muyuanfoods.com/#/companyDynamic": {
            "url": "https://www.muyuanfoods.com/api/muyuan-official-website/noToken/introduce/newsList?category=4&current={current}&size=6",
            "payload": {
                "category" : "4",
                "current" : "1",
                "size" : "6"
            }
        },
        "https://www.muyuanfoods.com/#/announcement": {
            "url": "https://www.muyuanfoods.com/api/muyuan-official-website/noToken/bondMatter/noticeList?category=1&current={current}&size=10",
            "payload": {
                "category" : "4",
                "current" : "1",
                "size" : "10"
            }
        },
        }

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        
        if api_data :
            api_url = api_data["url"]
            payload = api_data.get("payload")
            url =api_data.get("url")
            current =payload.get("current")
            current_page = response.meta.get("current_page", current)
            url = api_url.format(current=current_page)
            yield scrapy.Request(
            url = url,
            method = "GET",
            dont_filter=True,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "url": url,
                "current_page": current_page,
                },
            )


    article_to_pdf_urls_mapping={}

    article_to_date_mapping={}

    def get_articles(self, response) -> list:
        start_url =response.meta.get("start_url")
        articles =set()
        mapping = {}
        data = json.loads(response.text)
        elements= data.get("data",{}).get("records", [])
        if start_url == "https://www.muyuanfoods.com/#/companyDynamic":
            for item in elements:
                externalLink = item.get("externalLink")
                publish_time = item.get("publishTime")
                articles.add(externalLink)
                if externalLink:
                    mapping[externalLink] = publish_time
            self.article_to_date_mapping.update(mapping)
        else :
            for item in elements:
                title = item.get("title")
                publish_time = item.get("publishTime")
                file_info=item.get("fileInfo")
                if file_info:
                    articles.add(file_info)
                    mapping[file_info] = {
                        "title": title,
                        "date": publish_time,
                        "pdf_urls": file_info
                    }
            self.article_to_pdf_urls_mapping.update(mapping)
        return list(articles)

    def get_href(self, entry) -> str:
        return entry   

    def get_title(self, response) -> str:
        title = response.xpath('//h1[@class="rich_media_title "]//text()').get()
        if title is None:
            mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
            if mapping_entry:
                title = mapping_entry.get("title")
        return title
    
    def get_body(self, response) -> str:
        body  = body_normalization(response.xpath('//div[@id="js_content"]//section//span/text()').getall())
        if body:
            return body
        return " "

    def get_images(self, response) -> list:
        return response.xpath('scrap').getall()
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
        # try:
        #     # Get the mapping entry using the current response URL
        #     mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
        #     print(mapping_entry)
        #     if mapping_entry:
        #         pdf_urls = mapping_entry.get("pdf_urls")
        #         print(pdf_urls)
        #         if pdf_urls:
        #             return [pdf_urls]
            
        #     return []  # Return an empty list if no PDF URL found
        # except Exception as e:
        #     self.logger.error(f"Error in get_document_urls: {e}")
        #     return []

    
    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        mapping_entry = self.article_to_date_mapping.get(response.url)
        if mapping_entry:
            date = mapping_entry
            if date:
                return date
        mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
        if mapping_entry:
            date= mapping_entry.get("date")
        
        return date
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response):
        current_page=response.meta.get('current_page')
        print(current_page)
        total_pages = int(response.json().get('data', {}).get("pages", 0))
        print(total_pages)
        return str(int(current_page) + 1) if int(current_page) < total_pages else None
       
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=api_url.format(current=next_page),
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            yield None 
        
    