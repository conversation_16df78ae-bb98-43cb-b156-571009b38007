from datetime import datetime
from typing import Optional
import scrapy
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import urllib.parse
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

class ConnecticutDepartmentofLabor(OCSpider):
    name = 'ConnecticutDepartmentofLabor'
    
    country = "US"

    start_urls_names = {
    'https://portal.ct.gov/governor/news/press-releases?language=en_US': 'Press Releases'
    }
    
    api_start_urls = {
        "https://portal.ct.gov/governor/news/press-releases?language=en_US": {
            "url": "https://portal.ct.gov/governor/news/press-releases",
            "payload" : {
                "language": "en_US",
                "Page": 1
            }
        }
    }

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        payload = response.meta.get("payload", api_data["payload"].copy())
        parsed_url = urllib.parse.urlparse(api_data["url"])
        query_params = parse_qs(parsed_url.query)
        query_params.update(payload)  
        clean_query = urlencode(query_params, doseq=True)
        full_api_url = urllib.parse.urlunparse(parsed_url._replace(query=clean_query))     
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["Page"]
            }
        )

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="cg-c-article-list new-cg-c-press-rel"]//a[@class="cg-c-button-link"]/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class="cg-c-article__main-title"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="cg-c-article__content"]//p//text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> Optional[str]:
         return response.xpath('//div[@class="cg-c-article__date"]/text()').get().strip()

    def get_authors(self, response):
        return ""
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        next_page = int(current_page) + 1
        return next_page
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)
        payload["Page"] = next_page
        parsed_url = urllib.parse.urlparse(api_data["url"])
        query_params = urllib.parse.parse_qs(parsed_url.query)
        query_params["Page"] = next_page
        full_api_url = urllib.parse.urlunparse(
            parsed_url._replace(query=urllib.parse.urlencode(query_params, doseq=True))
        )
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": next_page
            },
        )