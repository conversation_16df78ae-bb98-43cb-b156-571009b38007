from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentofSemarangCity(OCSpider):
    name = "ParliamentofSemarangCity"

    start_urls_names = {
        "https://dprd.semarangkota.go.id/page/berita" : "News"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='post-item border']//h2//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='post-item']//h3").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='post-item-description']/p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='post-image']//img//@src").getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='post-item-wrap']//span[@class='post-meta-date']//text()").get().strip()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//ul[(@class= 'pagination')]//li//a[@rel='next']").get()
