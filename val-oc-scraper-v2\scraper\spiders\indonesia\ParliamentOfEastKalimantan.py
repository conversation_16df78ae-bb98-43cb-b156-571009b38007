from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfEastKalimantan(OCSpider):
    name = "ParliamentOfEastKalimantan"
    
    start_urls_names = {
        "https://dprd.kaltimprov.go.id/galeri-foto": "Galeri Foto",
        "https://dprd.kaltimprov.go.id/category/berita-utama":"Berita Utama"
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"
    
    article_data_map = {}  # Mapping title and date with child articles from start URL

    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='grid-item p-2']"):
            url = article.xpath(".//a/@href").get()
            title = article.xpath(".//a/span//text()").get()
            date = article.xpath(".//span//text()").get()
            if url and title and date:
                full_url = url
                title = title.strip()
                clean_date = date.strip()
                self.article_data_map[full_url] = {"title": title, "date": clean_date}
                articles.append(full_url) 
        return articles
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="text-center"]//p//text()').getall())
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%d/%m/%Y'
    
    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//ul[@class='pagination m-0']//a[@rel='next']/@href").get()