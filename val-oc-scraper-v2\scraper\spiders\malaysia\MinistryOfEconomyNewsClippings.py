from typing import List, Union
import scrapy
from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from bs4 import BeautifulSoup
from datetime import datetime

class MinistryOfEconomyNewsClippings(OCSpider):
    name = "MinistryOfEconomyNewsClippings"

    start_urls_names = {
        "https://ekonomi.gov.my/ms/media/keratan-akhbar?page=0": "Keratan Akhbar"
    }

    country = "Malaysia"

    start_urls_with_no_pagination_set = {}
    
    api_start_url = {
        'https://ekonomi.gov.my/ms/media/keratan-akhbar?page=0': {
            'url': 'https://ekonomi.gov.my/ms/media/keratan-akhbar?page=0',
            'payload': {
                "page": "0"
            },
        },   
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        current_page = response.meta.get("current_page", 0)
        api_data["payload"]["page"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="GET",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
        
    article_data_map ={} 

    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath("//tbody/tr")
        for row in rows:
            url = row.xpath(".//td[contains(@class, 'views-field-nothing')]/a/@href").get()
            title = row.xpath(".//td[contains(@class, 'views-field-nothing')]/a//text()").get()
            date = row.xpath(".//td[contains(@class, 'views-field-field-date')]//text()").get()
            source = row.xpath(".//td[contains(@class, 'views-field-field-sumber')]//text()").get()
            if url and title and date:
                full_url = response.urljoin(url)
                title = title.strip()
                clean_date = date.strip()
                pdf = full_url if ".pdf" in full_url.lower() else "None"
                self.article_data_map[full_url] = {
                    "title": title,
                    "date": clean_date,
                    "pdf": pdf,
                    "source": source.strip() if source else ""
                }
                articles.append(full_url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
         
    def get_body(self, response) -> str:
        return ""
        
    def get_images(self, response) -> List[str]:
        return ""
        
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        raw_date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if not raw_date:
            return ""
        month_mapping = {
            "Januari": "January",
            "Februari": "February",
            "Mac": "March",
            "April": "April",
            "Mei": "May",
            "Jun": "June",
            "Julai": "July",
            "Ogos": "August",
            "September": "September",
            "Oktober": "October",
            "November": "November",
            "Disember": "December"
        }
        for local, eng in month_mapping.items():
            if local in raw_date:
                raw_date = raw_date.replace(local, eng)
                break
        try:
            date_obj = datetime.strptime(raw_date.strip(), "%d %B %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError:
            self.logger.error(f"Failed to parse date: {raw_date}")
            return ""

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
        
    def get_next_page(self, response, current_page) -> Union[None, str]:
        data = response.text
        soup = BeautifulSoup(data, 'html.parser')
        hrefs = [a['href'] for a in soup.find_all('a', href=True)]
        if hrefs:
            return current_page + 1
        else:
            return None
        
    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page=0):
        next_page = self.get_next_page(response, current_page)
        if next_page is not None:
            next_url = f"https://ekonomi.gov.my/ms/media/keratan-akhbar?page={next_page}"
            yield scrapy.Request(
                url=next_url,
                callback=self.parse_intermediate,
                meta={
                    'current_page': next_page,
                    'start_url': start_url,
                    'api_url': next_url,
                }
            )