from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentofSarolangunRegency(OCSpider):
    name = "ParliamentofSarolangunRegency"

    start_urls_names = {
        "https://dprd.sarolangunkab.go.id/berita/filter/kategori/berita-dewan": "News",
        "http://jdih.dprd.sarolangunkab.go.id/": "JDIH"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='media-body']//h5[@class='mt-0']//a//@href | //div[@class='cns-content']//a[1]//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='col-xs-11 blog_content']/a[@class='blog_heading']/text() | //h2[@class='mt-3 mb-3 title']//text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='card-body']//p//text() | //div[@class='col-xs-11 blog_content']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='col-sm-8 main_blog']//img//@src | //div[@class='card-body']//img//@src").getall()
    
    def date_format(self) -> str:
        return "%b %d, %Y"
    
    def get_date(self, response) -> str:
        full_date = response.xpath("//small/span[@class='text-muted']/text()").get()
        if full_date:
            return full_date.strip()
        parts = response.xpath("//div[@class='blog_date']/a/text()").getall()
        if len(parts) == 3:
            day, month, year = parts
            return f"{month} {day}, {year}"  
        return None
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//li[@class='page-item']/a[@rel='next']/@href").get()