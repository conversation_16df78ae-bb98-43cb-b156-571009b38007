from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ExternalInformationReviewofVietnam(OCSpider):
    name = "ExternalInformationReviewofVietnam"

    start_urls_names = {
        "https://ttdn.vn/tin-tuc-su-kien": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="viewmore"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//span[@class="story_headline"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="story_teaser clearfix"]//text()').getall() or response.xpath('//table[@class="tbl-image"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//table[@class="tbl-image"]//img/@src').getall()

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return  response.xpath('//div[@class="story_date pt-lg-2"]//text()').re_first(r"(\d{1,2}/\d{1,2}/\d{4})")

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[@class="next"]/@href').get()
