from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import unquote

class MinistryofTourismArtsandCulture(OCSpider):
    name = "MinistryofTourismArtsandCulture"

    start_urls_names = {
        'https://www.motac.gov.my/media2/siaran': 'Media Release'
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    starting_page_number = 0

    articles_url_date_info={}

    def get_articles(self, response) -> list:
        self.article_date_mapping(response)
        return response.xpath('//table//td[@class="list-title"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="uk-article-title"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization( response.xpath('//article//text()[not(ancestor::h1)]').getall())       

    def get_date(self, response) -> str: 
        # some child article urls contains - and ""
        article_url = unquote(response.url)
        article_date = self.articles_url_date_info.get(article_url, None)
        if article_date:
            return article_date
        else:
            self.logger.error(f'No date found for URL: {article_url}')
            return None
    

    def article_date_mapping(self,response):
        urls = response.xpath('//table//td[@class="list-title"]/a/@href').getall()
        dates = response.xpath('//table//td[@class="list-date small"]//text()').getall()
        for url, date in zip(urls, dates):
            self.articles_url_date_info[response.urljoin(url)]= date.strip()
 
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//article//img//@src').getall()

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        start_urls = response.meta['start_url']+'?start='
        self.starting_page_number+=10
        if self.starting_page_number<100:
            return f"{start_urls}?start={self.starting_page_number}"
        else:
            return None