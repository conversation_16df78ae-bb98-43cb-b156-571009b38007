from scraper.OCSpider import OCSpider
import re
from typing import Optional

class MinistryOfInvestmentTradeAndIndustrySpeeches(OCSpider):
    name = "MinistryOfInvestmentTradeAndIndustrySpeeches"
    
    start_urls_names = {
        "https://www.miti.gov.my/index.php/pages/view/5008": "Speeches"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    visited_links = set()

    article_date_map = {}

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return list(self.article_date_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        entry = response.request.meta.get("entry")
        return self.article_date_map.get(entry, {}).get("title", "").strip()

    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        entry = response.request.meta.get("entry")
        return self.article_date_map.get(entry, {}).get("date", "").strip()

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        self.visited_links.add(response.url)
        archive_links = response.xpath('//ul/li/a[contains(text(), "Speeches")]/@href').getall()
        for link in archive_links:
            full_url = response.urljoin(link)
            if full_url not in self.visited_links:
                self.visited_links.add(full_url)
                return full_url
        return None

    def extract_articles_with_dates(self, response):
        li_elements = response.xpath('//div[starts-with(@id, "accordion_")]//li')
        for li in li_elements:
            link = li.xpath('.//a/@href').get()
            title_parts = li.xpath('.//a//text()[normalize-space()]').getall()
            title = ''.join(title_parts).strip()
            all_text = ''.join(li.xpath('.//text()').getall()).strip().replace('\xa0', ' ')
            match = re.search(r'\[(\w+\s+\d{1,2},\s+\d{4})\]', all_text)
            if match:
                date = match.group(1).strip()
            else:
                continue  
            if link and title and date:
                full_url = response.urljoin(link)
                self.article_date_map[full_url] = {
                    "title": title,
                    "date": date
                }
        return self.article_date_map
