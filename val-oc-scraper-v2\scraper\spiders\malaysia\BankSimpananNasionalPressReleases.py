from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class BankSimpananNasionalPressReleases(OCSpider):
    name = "BankSimpananNasionalPressReleases"

    start_urls_names = {
        f"https://www.anm.gov.my/pengumuman/{year}": "News"
        for year in range(2025,2018,-1)
    }
    
    start_urls_with_no_pagination_set = {}
    
    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "Bank"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
     
    def get_articles(self, response) -> list: 
        return response.xpath('////*[@id="adminForm"]/div[2]/table/tbody/tr/td[1]/span/span/a//@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//*[@id="g-mainbody"]/div/div/div/div/div/div/div/div[2]//*[self::h1 or self::h2 or self::h3 or self::h4 or self::h5 or self::h6 or self::p]/text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//*[@id="g-mainbody"]//p/img/@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        dates =  response.xpath('//*[@id="g-mainbody"]//div[1]//dl/dd[1]/time/text()').getall()
        date = dates[1]
        parsed_date = dateparser.parse(date, languages=['ms'])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        pdfs= response.xpath('//*[@id="g-mainbody"]//div[2]//p//*[self::object or self::embed]/@href |//*[@id="g-mainbody"]//div[2]//a/@href |//*[@class="df-more-container"]/a/@href').getall()
        filtered_pdf = []
        final_pdf_list= []
        for i in pdfs:
            if ".pdf" in i:
             filtered_pdf.append(i)
        for j in filtered_pdf:
            if 'http' not in i:
                j= "https://www.anm.gov.my" + j
            else:
                j=j
            final_pdf_list.append(j)    
        return final_pdf_list
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = response.xpath('//a[@aria-label="Go to next page"]//@href').get()
        if  next_page:
            return next_page
        return None 