from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
class ParliamentOfTanggamusRegency(OCSpider):
    name = "ParliamentOfTanggamusRegency"

    start_urls_names = {
        "https://dprd.tanggamus.go.id/berita": "Berita",
      }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }

    day_map = {
        'Senin': 'Monday', 'Selasa': 'Tuesday', 'Rabu': 'Wednesday',
        'Kamis': 'Thursday', 'Jumat': 'Friday', 'Sabtu': 'Saturday', 'Minggu': 'Sunday'
    }
    
    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="description"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="container"]//h3/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="container"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="container"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%A, %d %B %Y"
    
    def get_date(self, response) -> str:
        raw_date = response.xpath('(//div[@class="blog-date"]/ul/li)[2]/text()').get().strip()
        # Translate day and month from Indonesian to English
        for indo_day, eng_day in self.day_map.items():
            raw_date = raw_date.replace(indo_day, eng_day)
        for indo_month, eng_month in self.indonesian_to_english.items():
            raw_date = raw_date.replace(indo_month, eng_month)
        return raw_date

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
       return None