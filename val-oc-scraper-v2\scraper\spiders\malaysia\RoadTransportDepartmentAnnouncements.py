from scraper.OCSpider import <PERSON>CSpider 
from urllib.parse import unquote
import re
from scraper.utils.helper import body_normalization

class RoadTransportDepartmentAnnouncements(OCSpider):
    name = "RoadTransportDepartmentAnnouncements"

    start_urls_names = {
        'https://www.jpj.gov.my/category/pengumuman-terkini/': 'terkini'# pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.jpj.gov.my/category/pengumuman-terkini/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list = []
        articles = response.xpath('//div[@class="content"]//h2[@class="blog-entry-title entry-title"]')
        for article in articles:
            title = article.xpath('.//a/text()').get()
            url = article.xpath('.//a/@href').get()
            if title and url:
                title = title.strip()          
                date = re.search(r'\d{4}' , title)
                if date and url:
                    child_article_list.append(unquote(url))
                    self.article_url_title_date_mapping[unquote(url)]= [title , date.group(0)]

        return child_article_list
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[0]

    def get_body(self, response) -> str:
        return body_normalization([response.xpath('//div[@data-widget_type="text-editor.default"]//p//text()').get()])

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url),[])[1]

    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
       return response.xpath('//div[@class="gallery-icon landscape"]//img/@src').getall()
    
    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None