from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from datetime import datetime

class DepartmentOfFisheriesMalaysiaArchive(OCSpider):
    name = "DepartmentOfFisheriesMalaysiaArchive"

    current_year = datetime.now().year

    start_urls_names = {
        f"https://www.dof.gov.my/en/archives/tenders-quotations-archives/tenders-quotations-{year}/": "Archive" # pagination is not supported
        for year in range (current_year - 1, 2018, -1)
    }

    start_urls_with_no_pagination_set = {
        f"https://www.dof.gov.my/en/archives/tenders-quotations-archives/tenders-quotations-{year}/"
        for year in range (current_year - 1, 2018, -1)
    }

    charset = "iso-8859-1"

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=30000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//div[@class='posts-table-wrapper divi']//tbody//tr"):
                url = article.xpath(".//td[@class='col-mx_perolehan_dokumen']//a//@href").get()
                title = article.xpath(".//td[@class='col-title']//text()").get()
                date = article.xpath(".//td[@class='col-mx_perolehan_tarikh_tutup']//text()").get()
                if url and title:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    self.article_data_map[full_url]={"title": title, "full_url": url, "date": date}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None