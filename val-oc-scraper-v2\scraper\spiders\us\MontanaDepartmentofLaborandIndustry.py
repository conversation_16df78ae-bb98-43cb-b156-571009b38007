from scraper.utils.helper import body_normalization
from scraper.OCSpider import OCSpider
import scrapy
import json

class MontanaDepartmentofLaborandIndustry(OCSpider):
    name = 'MontanaDepartmentofLaborandIndustry'

    country = "US"

    start_urls_names = {
        "https://dli.mt.gov/": "News",
    }

    api_start_url = {
        'https://dli.mt.gov/': {
            'url': 'https://news.dli.mt.gov/News/__source.json',
        }
    } 
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_url.get(start_url)
        url = api_data["url"]
        data = json.loads(response.text)
        print("data1", data)
        all_articles = [item.getall("link") for item in data]
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                method = "GET",
                Headers = {
                "Accept-Encoding":"gzip, deflate, br"
                },
                meta={
                    'start_idx': start_idx, 
                    'articles': all_articles, 
                    'start_url': start_url
                },
                dont_filter=True
            )

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Central"
    
    @property
    def language(self):
        return "English"
    
    def get_articles(self, response) -> list:
        # all_articles = response.meta.get('articles', [])
        # start_idx = response.meta.get('start_idx', 0)
        # end_idx = start_idx + 100
        # return all_articles[start_idx:end_idx]
        # print("Respp",response.text)
        return response.xpath("//div[@class='article-content']//a//@href").getall() 
          
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='col m-auto mb-4 grt shadow mt-5']//h1//text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='col m-auto mb-4 grt shadow mt-5']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %d %Y"
    
    def get_date(self, response) -> str:
        date =  response.xpath("//ul//li[@class='date mb-4']//text()").get()
        date = date.replace("-","")
        return date.strip()
    
    def get_authors(self, response):
        return [response.xpath("//span[@class='small d-block mt-2 w-100']//a//text()").get()]
    
    def get_document_urls(self, response, entry=None):
        return response.xpath("//p//a[contains(@href,'.pdf')]//@href").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return None