from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ParliamentofRrokanhilirregency(OCSpider):
    name = "ParliamentofRrokanhilirregency"

    start_urls_names = {
        "https://dprd.rohilkab.go.id/category/berita-terkini": "News",
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self): 
        return "Indonesian"

    @property
    def timezone(self):
        return "Asia/Jakarta"

    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='content-list']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title = response.xpath("//h1[@class='article-title entry-title']//text()").get()
        return title.strip() if title else ""

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='entry']//p//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='media-single-content']//img//@src").getall()

    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        raw_date = response.xpath("//div[@class='time-article updated']//a//text()").get()
        if raw_date:
            for indo, eng in self.indonesian_to_english.items():
                raw_date = raw_date.replace(indo, eng)
            try:
                return datetime.strptime(raw_date.strip(), "%d %B %Y").strftime("%d %B %Y")
            except Exception as e:
                return
        return None

    def get_authors(self, response):
        return response.xpath("//div[@class='link-author']//a//text()").getall()

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return response.xpath("//div[@class='wp-pagenavi']//span//a[contains(text(),'>')]//@href").get()