from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
import re

class MalaccaStateGovernmentSpeeches(OCSpider):
    name = "MalaccaStateGovernmentSpeeches"
    
    start_urls_names = {
        "https://www.melaka.gov.my/media2/info-media/koleksi-teks-ucapan-audio-dan-visual-program-majlis-kerajaan-negeri-melaka.html": "Media",
        "https://www.melaka.gov.my/media2/info-media/arkib/koleksi-teks-ucapan-audio-dan-visual-program-majlis-kerajaan-negeri-melaka.html": "Media"
    }

    exclude_rules = [r'^https://melakadrive\.melaka\.gov\.my/d/s/zwt32Khvq3f82XuL0KQ9ekzYshWvV85g/.*']

    charset = "utf-8"

    def parse_intermediate(self, response):
        articles = response.xpath("//div[@class='com-content-article__body']//table//tbody//tr//td[4]//a//@href").getall()
        total_articles = len(articles)
        start_url =  list(self.start_urls_names.keys())
        for i in start_url:
            for start_idx in range(0, total_articles, 100):
                yield scrapy.Request(
                        url=i,
                        callback=self.parse,
                        meta={
                            'start_idx': start_idx, 
                            'start_url': i
                        },
                        dont_filter=True
                )

    custom_settings = {
        'HTTPERROR_ALLOW_ALL': True,
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"
        self.article_data_map ={}

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='com-content-article__body']//table//tbody//tr"):
            url = article.xpath(".//td[4]//a//@href").get()
            title = article.xpath(".//td[2]//text()").get()
            date = article.xpath(".//td[3]//text()").get()
            if url and title and date:
                if "/zwt32Khvq3f82XuL0KQ9ekzYshWvV85g/tFeoUsy8rNTyDoElT6UipFGbMPLi4r79-kbIABlZgows" in url:
                    url =""
                self.article_data_map[url] = {"date":date, "title" : title}
                articles.append(url)
        all_articles = list(set(articles))
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d.%m.%Y"
    
    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "") 

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # no next page to crawl
        return None