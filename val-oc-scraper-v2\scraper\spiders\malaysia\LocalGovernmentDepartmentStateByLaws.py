from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class LocalGovernmentDepartmentStateByLaws(OCSpider):
    name = "LocalGovernmentDepartmentStateByLaws"

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        self.start_urls_names = {}
        self.max_year = datetime.now().year
        self.min_year = 2021
        self.start_urls_names[
            f"https://jkt.kpkt.gov.my/en/monthly-news-archive/?selectedYear={self.max_year}"
        ] = "News/Circular"
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"
        self.article_data_map = {}
        self.current_year = self.max_year
        self.current_page = 1

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
    }

    @property
    def source_type(self) -> str:
        return "official_line"

    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def collapse_spaced_title(self, text: str) -> str:
        words = text.split()
        if all(len(word) == 1 for word in words if word.isalpha()):
            return ''.join(words)

        return re.sub(r'((?:[A-Z]\s)+[A-Z])', lambda m: m.group(0).replace(" ", ""), text)

    def get_articles(self, response) -> list:
        articles = []
        blocks = response.xpath('//div[contains(@class, "ct-div-block") and .//a[contains(@href, "/20")]]')
        self.logger.info(f"Matched {len(blocks)} article blocks")

        for block in blocks:
            url = block.xpath('.//a[contains(@href, "/20")]/@href').get()
            title = block.xpath('normalize-space(.//a[contains(@href, "/20")]/text())').get()
            date_element = block.xpath('.//div[contains(@class, "ct-text-block")]//span[contains(text(), "202")]/text()').get()

            date = None
            if date_element:
                parsed_date = dateparser.parse(date_element, languages=["en", "ms"])
                if parsed_date:
                    date = parsed_date.strftime("%d/%m/%Y")

            if url and title and date:
                title = body_normalization(title)
                title = self.collapse_spaced_title(title)
                title = re.sub(r'\s+', ' ', title).strip()
                title = re.sub(r'(\S)\s*\((.*?)\)', r'\1(\2)', title)
                title = re.sub(r'\(\s*(\S)', r'(\1', title)
                title = re.sub(r'(\S)\s*\)', r'\1)', title)

                articles.append(url)
                self.article_data_map[url] = {
                    "title": title,
                    "date": date
                }
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get("entry"), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def get_document_urls(self, response, entry=None) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        raw_date = self.article_data_map.get(response.request.meta.get("entry"), {}).get("date", "")
        if not isinstance(raw_date, str) or not raw_date.strip():
            self.logger.warning(f"No date found for URL: {response.url}")
            return ""
        try:
            dt = datetime.strptime(raw_date, "%d/%m/%Y")
            return dt.strftime(self.date_format())
        except Exception as e:
            self.logger.error(f"Date parsing failed for: {raw_date} at {response.url}: {e}")
            return ""

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response):
        current_year_on_page = response.request.url.split('selectedYear=')[-1].split('&')[0]
        if current_year_on_page.isdigit():
            self.current_year = int(current_year_on_page)
        else:
            self.current_year = response.request.meta.get('selected_year', self.max_year)
        next_page_link = response.xpath('//a[contains(@class, "next page-numbers")]/@href').get()
        if next_page_link:
            self.current_page += 1
            return next_page_link
        else:
            if self.current_year > self.min_year:
                self.current_year -= 1
                self.current_page = 1
                return f"https://jkt.kpkt.gov.my/en/monthly-news-archive/?selectedYear={self.current_year}"
        return None