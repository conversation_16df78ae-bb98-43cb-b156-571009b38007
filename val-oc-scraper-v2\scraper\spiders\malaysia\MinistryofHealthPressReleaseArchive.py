from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import dateparser
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
import re


class MinistryofHealthPressReleaseArchive(OCSpider):
    name = "MinistryofHealthPressReleaseArchive"

    start_urls_names = {
        "https://www.moh.gov.my/index.php/database_stores/store_view/16": "News",
        "https://www.moh.gov.my/index.php/database_stores/store_view/21": "covid",
        "https://www.moh.gov.my/index.php/database_stores/store_view/23":"Health",
        "https://www.moh.gov.my/index.php/database_stores/store_view/17":"Health",
        "https://www.moh.gov.my/index.php/database_stores/store_view/19":"Health",
        "https://www.moh.gov.my/index.php/database_stores/store_view/57":"Health"

        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"   
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    

    custom_settings = {
         "DOWNLOADER_MIDDLEWARES": {
             'scraper.middlewares.HeadlessBrowserProxy': 100
         },
         #"DOWNLOAD_DELAY": 5,
         "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
     }

    HEADLESS_BROWSER_WAIT_TIME = 2000
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       links = response.xpath('//*[@id="DataTables_Table_0"]/tbody/tr/td[4]/a/@href | //*[@id="DataTables_Table_0"]/tbody/tr/td[7]/a/@href').getall()
       print(links)
       new_li = []
       for link in links:
         if link:  # make sure it's not empty
          full_link = 'https://www.moh.gov.my' + link
          new_li.append(full_link)
       return new_li


    def get_href(self, entry) -> str:

        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//table[@class="dataTableDetail"]/tbody/tr[1]/td//text()').get()
   
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//table[@class="dataTableDetail"]/tbody/tr[2]/td//text()').getall())
  
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response) -> str:
       date_str = response.xpath('//table[@class="dataTableDetail"]/tbody/tr[3]/td//text()').get().strip()
       date_part = date_str.split(' ')[0]
       if '--0001' in date_part:
           date_part = date_part.replace('-0001','2023')
       date= date_part[:10]      
       return date
        
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return response.xpath('//table[@class="dataTableDetail"]//ul/li//a//@href').getall()
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        link = response.xpath('//div[contains(@class,"dataTables_paginate")]//a[normalize-space(text())="Next"]/@href').get()
        # print("Extracted relative 'Next' link:", link)
        # pages= link.split('?')[1]
        # page_no= pages.split('&')[1]
        # if page_no=='page=4':
        #     return None
        if link:
            full_url = response.urljoin(link)
            return full_url
        else:
           
            return None