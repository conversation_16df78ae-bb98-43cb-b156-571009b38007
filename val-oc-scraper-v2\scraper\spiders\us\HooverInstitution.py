from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import scrapy
from typing import Union
import json
from datetime import datetime

class HooverInstitution(OCSpider):
    name = "HooverInstitution"

    country = "US"

    start_urls_names = {
        "https://www.hoover.org/news-press": "News & Press",
    }

    api_start_url = {
    'https://www.hoover.org/news-press': {
        'url': 'https://9tyf7rjuhq-1.algolianet.com/1/indexes/*/queries?x-algolia-agent=Algolia%20for%20vanilla%20JavaScript%20(lite)%203.32.0%3Binstantsearch.js%20(4.38.1)%3BJS%20Helper%20(3.7.0)&x-algolia-application-id=9TYF7RJUHQ&x-algolia-api-key=********************************',
        'payload': {
            "requests": [
                    {
                        "indexName": "news_press",  
                        "params": "hitsPerPage=9&filters=hide_from_news_landing:false&highlightPreTag=__ais-highlight__&highlightPostTag=__/ais-highlight__&maxValuesPerFacet=2000&page=0&facets=[\"research_topic\",\"research_team\",\"news_type\",\"date\"]&tagFilters="
                    }
                ]
            },
        'headers': {
            "Content-Type": "application/json"
            }
        }
    }


    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
  
    charset = "utf-8"

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"]
        payload = api_data["payload"].copy()
        headers = api_data["headers"]
        current_page = response.meta.get("current_page", 1)
        page_for_payload = current_page - 1
        original_params = self.api_start_url[start_url]['payload']['requests'][0]['params']
        params_parts = original_params.split('&')
        new_params = "&".join([part if not part.startswith("page=") else f"page={page_for_payload}" for part in params_parts])
        payload["requests"][0]["params"] = new_params
        yield scrapy.Request(
            url=api_url,
            method="POST",
            headers=headers,
            body=json.dumps(payload),
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
            dont_filter=True,
        )

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/Los_Angeles"

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            hits = data.get("results", [])[0].get("hits", [])
            article_urls = [f"https://www.hoover.org{article.get('url')}" for article in hits if article.get("url")]
            return article_urls
        except json.JSONDecodeError:
            return []
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('normalize-space(translate(string(//span[@class="news"]/following-sibling::p[1]), "\u00a0", ""))').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content"]/p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        date_str = response.xpath('normalize-space(substring-after(normalize-space(//span[@class="ellipse"]/preceding-sibling::text()[last()]), ", "))').get()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page) -> Union[None, int]:
        try:
            data = json.loads(response.text)
            total_pages = data["results"][0]["nbPages"]
            if current_page < total_pages:
                return current_page + 1
        except (json.JSONDecodeError, KeyError, TypeError):
            pass
        return None

    def go_to_next_page(self, response, start_url, current_page=1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get("payload", {}).copy()
            original_params = self.api_start_url[start_url]['payload']['requests'][0]['params']
            params_parts = original_params.split('&')
            new_params = "&".join([part if not part.startswith("page=") else f"page={next_page - 1}" for part in params_parts])
            payload["requests"][0]["params"] = new_params
            yield scrapy.Request(
                url=api_url,
                method="POST",
                body=json.dumps(payload),
                headers=self.api_start_url[start_url]["headers"],
                callback=self.parse_intermediate,
                meta={
                    'current_page': next_page,
                    'start_url': start_url,
                    'api_url': api_url,
                    'payload': payload
                },
                dont_filter=True
            )