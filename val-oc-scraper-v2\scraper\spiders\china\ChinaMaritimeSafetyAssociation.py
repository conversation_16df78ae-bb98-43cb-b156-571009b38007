from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from typing import Union
from urllib.parse import urlencode
import scrapy

class ChinaMaritimeSafetyAssociation(OCSpider):
    name = 'ChinaMaritimeSafetyAssociation'
    
    custom_settings = {
        "DOWNLOAD_DELAY": 4,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    start_urls_names = {
        'https://cmsa.com.cn/wtsa/news/1648950693168615426' : '党群活动',
        'https://cmsa.com.cn/wtsa/news/1648950061787418625' : '新闻中心', #only for this start_url pagination and data extraction is done through api for rest start_urls done through normal xpaths
        'https://cmsa.com.cn/wtsa/news/1648949909261553666' : '重要活动',
        'https://cmsa.com.cn/wtsa/news/1648949326169411585' : '通知公告'
    }
    
    api_start_url = {
        'https://cmsa.com.cn/wtsa/news/1648950061787418625' : 'https://cmsa.com.cn/wtsa-prod-api/cmsNoToken/getArticleList?columnId=1648950061787418625&pageSize=10&sort=issueTime&order=desc&startDate=&endDate=&title='
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        current_page = response.meta.get("current_page", 1)
        if(start_url == 'https://cmsa.com.cn/wtsa/news/1648950061787418625'):
            api_data = self.api_start_url[start_url]
            query_params = {'pageNumber': current_page}
            api_url = f"{api_data}&{urlencode(query_params)}"
            yield scrapy.Request(
                url=api_url,
                callback=self.parse,
                meta={
                    "api_url" : api_data,
                    "current_page": current_page,
                    "start_url": start_url,
                },
            )
        else:
            yield scrapy.Request(
                url = start_url,
                callback=self.parse,
                meta={
                    "current_page" : current_page,
                    "start_url" : start_url
                }
            )
        
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response):
        start_url = response.meta.get("start_url")
        if start_url == "https://cmsa.com.cn/wtsa/news/1648950061787418625":
            data = response.json()
            article_list = data.get("result", {}).get("records", [])
            articles = [
                f"https://cmsa.com.cn/wtsa/news/details/{article.get('id')}?columnId={article.get('columnId')}"
                for article in article_list
                if article.get("id") and article.get("columnId")
            ]
            return articles
        else:
            return response.xpath("//div[@class='titlesize']/a/@href").extract()
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath("//article//div[@class='title']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//p//span//text()").getall())
    
    def get_images(self, response) -> list[str]:
        return response.xpath("//p//img/@src").extract()
    
    def get_authors(self, response):
        author = response.xpath("//div[@class='other-info']/p[contains(text(), '作者')]//text()").get()
        return author.split("：")[-1].strip() 
    
    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M'
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='other-info']/p//text()").re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}")  
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page) -> Union[None, str]:
        start_url = response.meta.get("start_url")
        if start_url == "https://cmsa.com.cn/wtsa/news/1648950061787418625":
            data = response.json() 
            total_pages = data["result"]["pages"]
            if current_page < total_pages:
               return current_page + 1
        else:
            return None
    
    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            yield None 