from scraper.OCSpider import OCSpider
from typing import Optional
from scraper.utils.helper import body_normalization
from datetime import datetime
class MinistryOfForeignAffairsMFA(OCSpider):
    name = "MinistryOfForeignAffairsMFA"

    start_urls_names = {
        "https://kemlu.go.id/publikasi/siaran-pers":"Article",
        "https://kemlu.go.id/berita":"Article",
        "https://kemlu.go.id/kebijakan/asean": "Article",
        "https://kemlu.go.id/kebijakan/isu-khusus": "Article",
        "https://kemlu.go.id/kebijakan/kerja-sama-regional":"Article",
        "https://e-ppid.kemlu.go.id/regulasi":"Article",
        "https://kemlu.go.id/publikasi/majalah": "Article",
        "https://kemlu.go.id/kebijakan/kerja-sama-bilateral": "Article",
        "https://kemlu.go.id/kebijakan/kerja-sama-multilateral":"Article",
    }
    
    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 4,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
	}

    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 seconds wait time

    article_data_map = {}  # Mapping Title and date to child articles from start URL

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//section//div[@class='min-w-[100%] md:min-w-[0] md:w-[100%] py-4 rounded-xl max-w-[30vw] hover:cursor-pointer']"):
            url = article.xpath(".//a//@href").get()
            title=article.xpath(".//div[@class='text-[1.18em] font-medium']//text()").get()
            date =article.xpath(".//div[@class='text-neutral-400 text-[0.8em]']//text()").get()
            if url and title and date:
                self.article_data_map[url]={"url":url, "title": title, "date": date}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='flex justify-between items-center md:1/6 mb-10']//text()").getall()) 

    def get_images(self, response) -> list:
        return response.xpath("//img[@class='w-full h-[42vw] object-contain']//@src").getall()

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if date:
            indonesian_months = {
                    'Januari': 'January', 'Februari': 'February', 'Maret': 'March',
                    'April': 'April', 'Mei': 'May', 'Juni': 'June',
                    'Juli': 'July', 'Agustus': 'August', 'September': 'September',
                    'Oktober': 'October', 'November': 'November', 'Desember': 'December'
            }
            for indo_month, eng_month in indonesian_months.items():
                if indo_month in date:
                    date = date.replace(indo_month, eng_month)
                    break
            date_obj = datetime.strptime(date, "%d %B %Y")
            date_text = date_obj.strftime("%B %d, %Y")
            return date_text
         
    def get_authors(self, response):
        return []
    
    def get_page_flag(self, response=None) -> bool:
        return False
     
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return None