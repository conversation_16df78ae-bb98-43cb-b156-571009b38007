from scraper.OCSpider import OCSpider
from datetime import datetime
from scraper.utils.helper import body_normalization

class MalaccaVeterinaryServicesDepartmentNewsClippings(OCSpider):
    name = "MalaccaVeterinaryServicesDepartmentNewsClippings"

    start_urls_names = {
        'https://jpv.melaka.gov.my/keratan-akhbar' : 'News Clippings',  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://jpv.melaka.gov.my/keratan-akhbar"
    }

    custom_settings = {
        "HTTPERROR_ALLOWED_CODES": [403],
    }

    charset = "iso-8859-1"

    MALAY_TO_ENGLISH_MONTHS = {
        "Januari": "January",
        "Februari": "February",
        "Mar": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//tbody/tr')
        for row in rows:
           title = row.xpath('.//td[@class="colortxt"][1]/text()[normalize-space()]').get()
           date = row.xpath('.//td[@class="colortxt"]//p//text()').get()  
           pdf_link = row.xpath('.//td[@class="colortxt"]//a/@href').get()
           if title and date and pdf_link:
                title = title.strip()
                date = date.strip()
                pdf_link = response.urljoin(pdf_link.strip())
                self.article_title_pdf_mapping[pdf_link] = (title, pdf_link, date)
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        original_url = response.meta.get('redirect_urls', [response.url])[0]
        return self.article_title_pdf_mapping.get(original_url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="elementor-element elementor-element-99201e1 elementor-widget elementor-widget-theme-post-content"]//p/text() | //div[@class="dable-content-wrapper"]/p/text()').getall())
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        original_url = response.meta.get('redirect_urls', [response.url])[0]
        date = self.article_title_pdf_mapping.get(original_url, ("", "", ""))[2]
        return self.convert_malay_date(date.strip())

    def get_images(self, response) -> list[str]:
        return [response.xpath('//figure//img/@src').getall()]

    def get_document_urls(self, response, entry=None) -> list:
        original_url = response.meta.get('redirect_urls', [response.url])[0]
        return [self.article_title_pdf_mapping.get(original_url, ("", "", ""))[1]]

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None
    
    def convert_malay_date(self, date_str: str) -> str:
        for malay, english in self.MALAY_TO_ENGLISH_MONTHS.items():
            if malay in date_str:
                date_str = date_str.replace(malay, english)
                break
        try:
            return datetime.strptime(date_str, "%d %B %Y").strftime("%d %B %Y")
        except Exception as e:
            self.logger.error(f"Date conversion failed for '{date_str}': {e}")
            return ""