from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ParliamentofPontianakCity(OCSpider):
    name = "ParliamentofPontianakCity"
    
    start_urls_names = {
        "https://dprd.pontianak.go.id/informasi/berita": "News"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h5[@class='title']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='post-content']//h2//text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='post-content']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='post-thumb']//img//@src").getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"  
    
    def get_date(self, response) -> str:
        raw_date = response.xpath("//div[@class='post-content']//ul[@class='post-meta']//li[2]/text()").get()
        if raw_date:
            raw_date = raw_date.strip()
            try:
                return datetime.strptime(raw_date, self.date_format()).strftime("%Y-%m-%d")
            except ValueError:
                for fmt in ("%d %b %Y", "%d %B %Y"):
                    try:
                        return datetime.strptime(raw_date, fmt).strftime("%Y-%m-%d")
                    except ValueError:
                        continue
        return None
    
    def get_authors(self, response):
        return response.xpath("//div[@class='post-content']//ul[@class='post-meta']//li//a//text()").getall()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//div[@class='container']//ul[@class='pagination']//a[@rel='next']//@href").get()