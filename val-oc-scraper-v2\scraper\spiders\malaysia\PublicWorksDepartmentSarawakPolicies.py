from scraper.OCSpider import OCSpider 
from urllib.parse import urljoin ,  unquote
import re
from scraper.utils.helper import body_normalization


class PublicWorksDepartmentSarawakPolicies(OCSpider):
    name = "PublicWorksDepartmentSarawakPolicies"

    start_urls_names = {
        'https://jkr.sarawak.gov.my/web/subpage/webpage_view/330': 'Policy'# pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://jkr.sarawak.gov.my/web/subpage/webpage_view/330"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list = []
        articles = response.xpath('//div[@class="list-content"]')
        for article in articles:
            title = article.xpath('.//p//span//text()').get()
            img =article.xpath('.//p//img/@src').get()
            date_data = re.search(r'\d{4}' , title)
            if date_data:
                full_url = urljoin("https://jkr.sarawak.gov.my" , img)
                child_article_list.append(full_url)
                self.article_url_title_date_mapping[full_url] = [ title , date_data.group(0)]
        return child_article_list
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url))[0]

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        return self.article_url_title_date_mapping.get(unquote(response.url))[1]


    def date_format(self) -> str:
        return "%Y"

    def get_images(self, response) -> list[str]:
       return [response.url]
    
    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None