from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization
import scrapy

class TuoiTreNewsofVietnam(OCSpider):
    name = "TuoiTreNewsofVietnam"

    start_urls_names = {
        'https://tuoitre.vn/thoi-su/trang-1.htm': 'news'
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return 'unknown'

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    @property
    def language(self):
        return "Vietnamese"

    def get_articles(self, response) -> list:
        articles = response.xpath('//a[@class="box-category-link-title"]//@href').getall() or  response.xpath('//a[@class="box-category-link-with-avatar img-resize"]//@href').getall()
        return articles
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="detail-title article-title"]//text()').get() or ''
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detail-cmain clearfix"]//p//text()').getall())
        
    def get_date(self, response) -> str:
        date_data = response.xpath('//div[@data-role="publishdate"]//text()').get()
        date = re.search(r"\d{2}/\d{2}/\d{4} \d{2}:\d{2}", date_data)
        if date:
            return date.group(0)
        else:
            ValueError(f"Date not found for url = {response.url}")
       
    def date_format(self) -> str:
        return "%d/%m/%Y %H:%M"

    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="detail-cmain clearfix"]//img/@src').getall()

    def get_document_urls(self, response, entry=None)-> list:
        return []
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        page_no = response.meta.get('page_no' , 1)
        return page_no
    
    def go_to_next_page(self, response, start_url, current_page=None):
        page_no = self.get_next_page(response)
        page_no += 1
        if page_no:
            start_url = response.meta.get('start_url')
            yield scrapy.Request(
                url = f"https://tuoitre.vn/timeline/3/trang-{page_no}.htm" ,
                method= "GET",
                callback = self.parse, 
                meta= {
                    'page_no' : page_no ,
                    'start_url' : start_url
                }
            )
        else:
            yield None