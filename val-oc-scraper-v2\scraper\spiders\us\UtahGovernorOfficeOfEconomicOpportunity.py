from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class UtahGovernorOfficeOfEconomicOpportunity(OCSpider):
    name = 'UtahGovernorOfficeOfEconomicOpportunity'
    
    country = "US"

    start_urls_names = {
        'https://business.utah.gov/news/': 'News'
    }
    
    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {'scraper.middlewares.HeadlessBrowserProxy': 350},
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }


    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Mountain"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "x-row x-container max width e71515-e27 m1j6j-f m1j6j-h m1j6j-k m1j6j-t"]/div/a/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class= "entry-title"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content content"]//p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//header[@class="entry-header"]//time[@class="entry-date"]/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return response.xpath('//div[@class="x-paginate-inner"]/a[contains(@class, "prev-next") and span[text()="Next"]]/@href').get()