from scraper.OCSpider import OCSpider
import dateparser
from urllib.parse import urljoin


class CentralDrugsStandardControlOrganizationCENTRALVietnam(OCSpider):
    name = "CentralDrugsStandardControlOrganizationCENTRALVietnam"

    start_urls_names = {
        "https://cdsco.gov.in/opencms/opencms/en/PvPI/": "Media Releases"
    }

    start_urls_with_no_pagination_set = {
        "https://cdsco.gov.in/opencms/opencms/en/PvPI/"
    }

    charset = "utf-8"  # Use utf-8 to avoid garbled characters

    country = "India"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kolkata"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        """Extract articles from the table and store details in article_data_map"""
        rows = response.xpath("//table[@id='example']/tbody/tr")
        articles = []

        for row in rows:
            title = row.xpath("./td[2]//text()").get()
            date_text = row.xpath("./td[3]//text()").get()
            pdf_url = row.xpath("./td[4]/a/@href").get()

            if not pdf_url or not title:
                continue

            # Build full PDF URL
            full_url = urljoin(response.url, pdf_url.strip())

            # Clean up title
            clean_title = title.replace("\xa0", " ").strip()

            # Parse date
            parsed_date = ""
            if date_text:
                date_obj = dateparser.parse(date_text.strip(), languages=['en'])
                if date_obj:
                    parsed_date = date_obj.strftime("%Y-%m-%d")

            # Save data for later use
            self.article_data_map[full_url] = {
                "title": clean_title,
                "date": parsed_date,
                "full_url": full_url,
            }

            articles.append(full_url)

        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        """Fetch title from saved data"""
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        """Fetch date from saved data"""
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        """Return the actual document (PDF) URL"""
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None
