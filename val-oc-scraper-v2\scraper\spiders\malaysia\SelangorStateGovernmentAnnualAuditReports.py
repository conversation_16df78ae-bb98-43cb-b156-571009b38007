from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class SelangorStateGovernmentAnnualAuditReports(OCSpider):
    name = "SelangorStateGovernmentAnnualAuditReports"

    start_urls_names = {
        "https://www.selangor.gov.my/index.php/pages/view/4068?mid=993": "Laporan <PERSON>hunan PSUK"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request 

    def get_articles(self, response) -> list:  
        articles =[]
        for entry in response.xpath('//div[@class="product"]'):
            url= entry.xpath('.//@data-url').get()
            title = entry.xpath('.//@title').get()
            date = title.split(" ")[-1]
            if url and title and date:
                self.article_data_map[url] = {"date":date, "title" : title}
                articles.append(url)
        return articles
        
    def get_href(self, entry) -> str:
        return f'https://www.selangor.gov.my{entry}'
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None