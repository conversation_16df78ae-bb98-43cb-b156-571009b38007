from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
import datetime

class VietnamWomenNewspaper(OCSpider):
    name = "VietnamWomenNewspaper"

    start_urls_names = {
        "https://phunuvietnam.vn/chinh-tri-xa-hoi.htm": "News",# Pagination is not supported
        }
    
    start_urls_with_no_pagination_set = {
        "https://phunuvietnam.vn/chinh-tri-xa-hoi.htm"
    }

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//*[@id="main-content"]/div[2]/div/div/div[1]//h2/a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//*[@id="main-content"]/div[2]/div/div/div[1]/div/h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p[@style="text-align: justify;"]//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//figure//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('(//div[@class="detail-time"]//text())[2]').get()
        #print(date,"date is ================================")
        clean_date = date.strip()
        date_obj = dateparser.parse(clean_date, languages=['vi'])
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None