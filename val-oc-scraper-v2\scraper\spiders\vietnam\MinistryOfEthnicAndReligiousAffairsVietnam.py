from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MinistryOfEthnicAndReligiousAffairsVietnam(OCSpider):
    name = "MinistryOfEthnicAndReligiousAffairsVietnam"

    start_urls_names = {
        "http://www.cema.gov.vn/tin-tuc.htm": "Tin nổi bật",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/thoi-su-chinh-tri.htm":"THỜI SỰ - CHÍNH TRỊ",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/chu-truong-chinh-sach.htm":"CHỦ TRƯƠNG - CHÍNH SÁCH",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/kinh-te-xa-hoi.htm":"KINH TẾ - XÃ HỘI",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/y-te-giao-duc.htm":"Y TẾ - GIÁO DỤC",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/van-hoa-van-nghe-the-thao.htm":"VĂN HÓA - VĂN NGHỆ - THỂ THAO",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/khoa-hoc-cong-nghe-moi-truong.htm":"KHOA HỌC - CÔNG NGHỆ - MÔI TRƯỜNG",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/phap-luat.htm":"PHÁP LUẬT",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/quoc-te.htm":"QUỐC TẾ",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/nghien-cuu-trao-doi.htm":"NGHIÊN CỨU - TRAO ĐỔI",
        "http://www.cema.gov.vn/tin-tuc/tin-tuc-su-kien/guong-dien-hinh-tien-tien.htm":"GƯƠNG ĐIỂN HÌNH TIÊN TIẾN"
    }


    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"


    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    
    def get_articles(self, response) -> list[str]: 
        return response.xpath('//div[@class="news-block-body"]//a[not(contains(@class,"viewmore"))]/@href').getall()



        
    def get_href(self, entry) -> str:
        return entry
    
   
    def get_title(self, response) -> str:
        
        xpath_03 = '//span[@id="BodyContent_ctl00_ctl03_newsTitle"]/text()'
        xpath_02 = '//span[@id="BodyContent_ctl00_ctl02_newsTitle"]/text()'
        xpath_01 = '//span[@id="BodyContent_ctl00_ctl01_newsTitle"]/text()'
        xpath_h3 = '//h3[@class and span[@id="BodyContent_ctl00_ctl00_newsTitle"]]/span/text()'

        title = None

        # Try xpath_03
        title = response.xpath(xpath_03).get()
        print(f"Trying xpath_03 -> {title!r}")

        if not title:
            # Try xpath_02
            title = response.xpath(xpath_02).get()
            print(f"Trying xpath_02 -> {title!r}")

        if not title:
            # Try xpath_01
            title = response.xpath(xpath_01).get()
            print(f"Trying xpath_01 -> {title!r}")

        if not title:
            # Try xpath_h3
            title = response.xpath(xpath_h3).get()
            print(f"Trying xpath_h3 -> {title!r}")

        # Debug prints
        print("\n=== DEBUG get_title ===")
        print(f"URL: {response.url}")
        print(f"Final extracted title: {title!r}")
        print(f"Raw <h1>: {response.xpath('normalize-space(//h1)').get()}")
        print(f"Raw <title>: {response.xpath('normalize-space(//title/text())').get()}")
        print("======================\n")

        return title



    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="divNewsDetails"]//p/text()').getall())

    

    def get_images(self, response) -> list:
        return response.xpath('//div[@id="divDescription"]//img/@src | //div[@id="divNewsDetails"]//img/@src').getall()

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        xpaths = [
            '//span[@id="BodyContent_ctl00_ctl03_lblDate"]/text()',
            '//span[@id="BodyContent_ctl00_ctl00_lblDate"]/text()',
            '//span[@id="BodyContent_ctl00_ctl01_lblDate"]/text()',
            '//span[@id="BodyContent_ctl00_ctl02_lblDate"]/text()',
        ]

        date = None
        for xp in xpaths:
            date = response.xpath(xp).re_first(r'(\d{2}/\d{2}/\d{4})')
            if date:
                break

        return date


    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None
