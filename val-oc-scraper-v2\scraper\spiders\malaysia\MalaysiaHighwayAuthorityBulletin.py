
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class MalaysiaHighwayAuthorityBulletin(OCSpider):
    name = "MalaysiaHighwayAuthorityBulletin"

    start_urls_names = {
    f"https://www.llm.gov.my/publication/bulletin/{i}": "News" for i in range(0, 26, 5) #pagination not supporting 
}   
    start_urls_with_no_pagination_set={
        "https://www.llm.gov.my/publication/bulletin/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
     articles= []
     for article in response.xpath('//div[@class="publication"]//div[@class="panel panel-item"]'):
            img = article.xpath(".//img//@src").get()
            title=article.xpath(".//p//text()").get()
            link= article.xpath(".//a//@href").get()
            if link and img and title:
                articles.append(link)
                self.article_data_map[link]={
                    "img":img,"title":title
                }
     return list(set(articles))

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
     return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("img", "")]
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
       title= self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
       match = re.search(r'\b(20\d{2})\b', title)
       year= match.group(1)
       parsed_date = dateparser.parse(year, languages=['en', 'ms'])
       return parsed_date.strftime("%Y") 
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 