from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class AccountantGeneralDepartmentNewsClippings(OCSpider):
    name = "AccountantGeneralDepartmentNewsClippings"

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        self.start_urls_names = {}
        self.max_year = datetime.now().year
        self.min_year = 2012
        paginate = not self.get_page_flag()
        years_to_run = [self.max_year] if paginate else range(self.min_year, self.max_year + 1)
        for year in years_to_run:
            if year >= 2025:
                url = f"https://www.anm.gov.my/en/newspaper-cutting/tahun-{year}"
            elif year == 2024:
                url = "https://www.anm.gov.my/en/newspaper-cutting/year-2024-en"
            else:
                url = f"https://www.anm.gov.my/en/newspaper-cutting/year-{year}"
            self.start_urls_names[url] = "News/Circular"
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"
        self.article_data_map = {}

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
    }

    @property
    def source_type(self) -> str:
        return "official_line"

    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        articles = []
        for block in response.xpath('//div[contains(@class, "pd-filebox")]'):
            link_el = block.xpath('.//div[contains(@class, "pd-float")]/a')
            url_part = link_el.xpath('./@href').get()
            title = link_el.xpath('normalize-space(./text())').get()
            pdf_preview_url = block.xpath('.//a[contains(@href, ".pdf")]/@href').get()
            if not pdf_preview_url:
                continue
            url = response.urljoin(pdf_preview_url)
            date = None
            year_match = re.search(r'/(\d{4})/', pdf_preview_url)
            if year_match:
                date = f"01/01/{year_match.group(1)}"
            else:
                date_match = re.search(r'(\d{4})', url_part or '')
                if date_match:
                    date = f"01/01/{date_match.group(1)}"
            title = body_normalization(title)
            if re.search(r'(?:[A-Za-z]\s){3,}', title):
                title = title.replace(" ", "")
            title = re.sub(r'\s+', ' ', title).strip()
            if url and title and date:
                articles.append(url)
                self.article_data_map[url] = {
                    "title": title,
                    "date": date
                }
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get("entry"), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def get_document_urls(self, response, entry=None):
        return [entry] if entry and entry.endswith(".pdf") else []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        raw_date = self.article_data_map.get(response.request.meta.get("entry"), {}).get("date", "")
        parsed_date = dateparser.parse(raw_date, languages=["ms"])
        return parsed_date.strftime(self.date_format()) if parsed_date else ""

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        next_page = response.xpath('//li[@class="page-item"]/a[contains(@aria-label, "next")]/@href').get()
        return response.urljoin(next_page) if next_page else None