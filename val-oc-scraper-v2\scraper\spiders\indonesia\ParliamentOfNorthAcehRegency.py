from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import urllib.parse
import scrapy
from scraper.middlewares import HeadlessBrowserProxy

class ParliamentOfNorthAcehRegency(OCSpider):
    name = "ParliamentOfNorthAcehRegency"

    start_urls_names = {
        "https://dprk.acehutara.go.id/berita": "Press Releases"
    }

    api_start_urls = {
        "https://dprk.acehutara.go.id/berita": {
            "url": "https://dprk.acehutara.go.id/json/berita.json?mode=datatable",
            "payload" : {
                "draw": "1",
                "start": "0",
            }
        }
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers={
                "Content-Type": "application/json",
            },
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["draw"]
            }
        )
    
    article_data_map ={}  # Mapping date and title with child articles from start URL

    def get_articles(self, response) -> list:
        data = response.json()
        articles = []
        article_data = data.get("data", [])
        for item in article_data:
            slug = item.get("slug")
            pub_date = item.get("pubDate")
            title = item.get("judul")
            if slug and pub_date and title:
                self.article_data_map[slug] = {"date": pub_date, "title" : title}
                articles.append(slug)
        return articles
        
    def get_href(self, entry) -> str:
        hbp = HeadlessBrowserProxy()
        return hbp.get_proxy(f'https://dprk.acehutara.go.id{entry}', timeout=10000)
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", None)
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='card-body isi-berita']//p//text() | //div[@class='card-body isi-berita']//text()").getall()) 
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='gambar-caption']//img//@src |//div[@class='card-body isi-berita']//img//@src").getall()
    
    def date_format(self) -> str:
        return "%a, %d %b %Y %H:%M:%S %z"
    
    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", None)
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf = response.xpath("//div[@class='card-body isi-berita']//p//a[contains(@href,'.pdf')]//@href").getall()
        pdfs=[]
        for i in pdf:
            i = f'https://dprk.acehutara.go.id{i}'
            pdfs.append(i)
        return pdfs

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return None