from scraper.OCSpider import OCSpider

class SabahStateGovernment(OCSpider):
    name = "SabahStateGovernment"
    
    country = "Malaysia"

    start_urls_names = {   
        "https://sabah.gov.my/announcement?cat_id=2" : "News"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://sabah.gov.my/announcement?cat_id=2"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='table-responsive col']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='views-field views-field-nothing']//h2//text()").get()
        
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='field field--name-field-page-banner-image field--type-image field--label-hidden field__item']//img//@src").getall()
        
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return response.xpath('//div[@class="event-date"]/time/text()').get()

    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//div[@class="views-field views-field-views-conditional-field"]//a//@href').getall()
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None