from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import urllib.parse
import scrapy
from urllib.parse import urlencode

class LandAndSurveyDepartmentNews(OCSpider):
    name = "LandAndSurveyDepartmentNews"

    country = "Malaysia"

    start_urls_names = {
        "https://landsurvey.sarawak.gov.my/web/subpage/news_list/": "Press Releases",
    }
    
    api_start_urls = {
    "https://landsurvey.sarawak.gov.my/web/subpage/news_list/": {
        "url": "https://landsurvey.sarawak.gov.my/web/subpage/news_list_ajax/",
        "payload": {
            "page": "1",
            "sort": "",
            "order_by": "date",
            "s": "",
            "m": "",
            "y": "",
            "category": "",
            "extra_cat_param": ""
        }
    }
}

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        payload = response.meta.get("payload", api_data["payload"].copy())
        current_page = int(payload.get("page", 1))
        api_url = api_data["url"]
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"  
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            }
        )
        yield from self.go_to_next_page(response, start_url, current_page)

    def get_articles(self, response) -> list:
        return  response.xpath('//div[@id="resp-table-body"]//div[@class="table-body-cell table-action"]/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="list-title"]/h1/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="list-content"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d %b %Y"
    
    def get_date(self, response) -> str:
        full_text = response.xpath('//div[@class="list-sub"]/h5/text()').get()
        if full_text:
            return full_text.replace("Posted on ", "").strip()
        return ""
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: int) -> Optional[int]:
        articles = self.get_articles(response)
        if not articles:
            return None
        return current_page + 1
   
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        base_api_url = api_data["url"].split('?')[0]
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)
        if not next_page:
            return
        payload["page"] = next_page
        query_string = urlencode(payload)
        full_api_url = f"{base_api_url}?{query_string}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": base_api_url,
                "payload": payload,
                "current_page": next_page
            },
        )
