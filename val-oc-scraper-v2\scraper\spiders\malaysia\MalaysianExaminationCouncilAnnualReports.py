#MalaysianExaminationCouncilAnnualReports


#MinistryofHomeAffairs(MOHA)Announcements
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MalaysianExaminationCouncilAnnualReports(OCSpider):
    name = "MalaysianExaminationCouncilAnnualReports"

    start_urls_names = {
        "https://www.mpm.edu.my/sumber/arkib/laporan-tahunan": "News"
        }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       articles= []
       for article in response.xpath('//div[contains(@class, "g-block size-33")]'):
           title = article.xpath('.//div[@class="alert alert-warning center"]/text()').get()
           link = article.xpath('.//div[@class="_df_thumb"]/@source').get()
           img = article.xpath('.//div[@class="_df_thumb"]/@thumb').get()
           if link:
               articles.append(link)
               self.article_data_map[link]={
                    "title":title,
                    "img":img,
                    "link":link
                }
       
       return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
   
    def get_images(self, response) -> list:
         images= []
         img= self.article_data_map.get(response.request.meta.get('entry'), {}).get("img", "")
         images.append(img)
         return images
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        title =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        date = title[-4:]
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         pdf= []
         links= self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
         pdf.append(links)
         return pdf
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None