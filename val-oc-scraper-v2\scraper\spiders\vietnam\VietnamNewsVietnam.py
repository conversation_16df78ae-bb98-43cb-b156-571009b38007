from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime


class VietnamNewsVietnam(OCSpider):
    name = "VietnamNewsVietnam"
    
    start_urls_names = {
        "https://vietnamnews.vn/politics-laws": "Press Release",
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"
    
    @property
    def language(self): 
        return "Vietnamese"
    
    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        return response.xpath('//article[@class="story"]//a[@class="story__title"]/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="detail__header"]//h1[@class="headline"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath('//div[@class="sapo"]|//div[@id="abody"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@id="abody"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> Optional[str]:
        raw_date = response.xpath('//div[@class="datetime"]//text()').get()
        if raw_date:
            raw_date = raw_date.strip()
            try:
                parsed_date = datetime.strptime(raw_date, "%B %d, %Y - %H:%M")
                return parsed_date.strftime("%Y-%m-%d")
            except Exception as e:
                self.logger.warning(f"Date parsing failed for: {raw_date} ({e})")
        return None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return response.xpath('//ul[@class="pagination"]//a[@aria-label="Next"]/@href').get()