import json
from scrapy.selector import Selector
import re
from scraper.OCSpider import <PERSON>CSpider
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaAssociationOfRailwayEngineeringConstruction(OCSpider):
    name="ChinaAssociationOfRailwayEngineeringConstruction"

    custom_settings = {
        "DOWNLOAD_DELAY": 2, 
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    start_urls_names={
        "https://www.carec.org.cn/carec/Content?type=%E5%8D%8F%E4%BC%9A%E5%B7%A5%E4%BD%9C&typeId=ff8080817e75244e017e756750700009":"协会工作",
        "https://www.carec.org.cn/carec/Content?type=%E8%A1%8C%E4%B8%9A%E5%8A%A8%E6%80%81&typeId=ff8080817e75244e017e757797b00020":"行业动态",
    }

    api_start_urls = {
        "https://www.carec.org.cn/carec/Content?type=%E5%8D%8F%E4%BC%9A%E5%B7%A5%E4%BD%9C&typeId=ff8080817e75244e017e756750700009": {
            "url": "https://www.carec.org.cn/prod-api/api/selArticleByType?typeId=ff8080817e75244e017e7567d4ab000a&page={current_page}&size=15",
        },
        "https://www.carec.org.cn/carec/Content?type=%E8%A1%8C%E4%B8%9A%E5%8A%A8%E6%80%81&typeId=ff8080817e75244e017e757797b00020": {
            "url": "https://www.carec.org.cn/prod-api/api/selArticleByType?typeId=ff8080817e75244e017e7577c7d80021&page={current_page}&size=15",
        },
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page",0)
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        # current page contion because ocspider has condition if current_page: and it doesn't work for 0 as it consider 0 False
        if current_page==0:
            api_url = api_url.format(current_page=current_page) if api_url else None
        else:
            api_url = api_url.format(current_page=str(int(current_page)-1)) if api_url else None
        yield scrapy.Request(
            url = api_url,
            method = "GET",
            headers={
                "Content-Type": "application/json;",
            },
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "current_page": int(current_page) + 1
            },
        )

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"    
    
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            articles = data.get("data", {}).get("content",[])
            article_urls = [
                self.construct_article_url(article)
                for article in articles
                if article
            ]
            return article_urls
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []
        
    def get_href(self, entry) -> str:
        return entry    
        
    def get_title(self, response) -> str:
        return response.json().get('data', {}).get("title","")
         
    def get_body(self, response) -> str:
        selector = Selector(text=response.json().get('data', {}).get("content",""))
        return body_normalization(selector.xpath('//*//text()').getall())
    
    def get_images(self, response, entry=None) -> List[str]:
        selector = Selector(text=response.json().get('data', {}).get("content",""))
        return [src.strip('\\"') for src in selector.xpath('//img//@src').getall()]
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        selector = Selector(text=response.json().get('data', {}).get("content",""))
        return selector.xpath('//*//a//@href').getall()

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        return re.search(r"\d{4}-\d{2}-\d{2}", response.json().get('data', {}).get("createTime","")).group()
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page):
        data = response.json().get('data', {}).get("content",[])
        return str(int(current_page) + 1) if data != [] else None

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                method='GET',
                headers={
                    "Content-Type": "application/json;",
                },
                callback=self.parse_intermediate,  
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None

    def construct_article_url(self, article):
        id = article.get('id')   
        if id:
            link = f"https://www.carec.org.cn/prod-api/api/getArticle?id={id}" 
            return link  
        return None