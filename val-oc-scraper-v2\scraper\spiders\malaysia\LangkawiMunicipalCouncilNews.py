from scraper.OCSpider import OCSpider
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
from scraper.utils.helper import body_normalization
import re

class LangkawiMunicipalCouncilNews(OCSpider):
    name = "LangkawiMunicipalCouncilNews"

    start_urls_names = {
        'https://pbt.kedah.gov.my/index.php/berita-mplbp-2/' : 'News'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set= {
        'https://pbt.kedah.gov.my/index.php/berita-mplbp-2/'
    }

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(response.url,timeout=20000),
            callback=self.parse, 
            dont_filter=True, 
            meta ={
                "start_url" : response.url
                }
            )

    malay_months = {
        "JANUARI": "January",
        "FEBRUARI": "February",
        "MAC": "March",
        "APRIL": "April",
        "MEI": "May",
        "JUN": "June",
        "JULAI": "July",
        "OGOS": "August",
        "SEPTEMBER": "September",
        "OKTOBER": "October",
        "NOVEMBER": "November",
        "DISEMBER": "December"
    }

    def get_articles(self, response):
        return response.xpath('//h2[@class="entry-title"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@data-aos="zoom-out-up"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="elementor-widget-container"]//p//text()').getall())

    def get_date(self, response) -> str:
        date_part = response.xpath('//p[@class="yiv3101719405MsoNormal"]/text()').get().strip()
        date_part = re.sub(r'[–—-]', '', date_part)
        for malay_month, english_month in self.malay_months.items():
            if malay_month in date_part:
                date_part = date_part.replace(malay_month, english_month)
                break
        return date_part.strip()

    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None