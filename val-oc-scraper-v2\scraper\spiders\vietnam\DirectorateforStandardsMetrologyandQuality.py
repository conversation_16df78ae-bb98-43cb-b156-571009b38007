from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class DirectorateforStandardsMetrologyandQuality(OCSpider):
    name = "DirectorateforStandardsMetrologyandQuality"

    start_urls_names = {
        "https://tcvn.gov.vn/category/tin-tuc/": "NewsArticles",
        }
    
    start_urls_with_no_pagination_set = {}
    
    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       links= response.xpath('//*[@id="main"]/div/div/div/h4/strong/a//@href').getall()
       return links
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//p[@class="post-title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p[@style="text-align: justify;"]//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//p//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//span[@class="highlight"]//text()').get()
        clean_date = date.strip()  
        dt = dateparser.parse(clean_date, languages=['vi'])
        formatted = dt.strftime("%Y-%m-%d")
        return formatted
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          next_page = response.xpath('//a[normalize-space(.)=">"]/@href').get()
          return f"?page={next_page}"
    