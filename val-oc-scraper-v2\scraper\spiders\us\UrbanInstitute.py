from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class UrbanInstitute(OCSpider):
    name = "UrbanInstitute"

    country = "US"

    start_urls_names = {
        "https://www.urban.org/research": "Research Publications",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 seconds wait time

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/New_York"

    def get_articles(self, response) -> list:
        return response.xpath('//ul[contains(@class,"list")]//lbj-lede/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title_xpath = (
            '//lbj-title[@variant="heading-1"]/span/text() | '
            '//lbj-title[@element="h1"]/span/text() | '
            '//lbj-title[@element="h1" and @variant="heading-1" and '
            '@class="mb-4 lg:mb-6 hydrated" and @mode="urban"]/span/text()'
        )
        title_parts = [part.strip() for part in response.xpath(title_xpath).getall() if part.strip()]
        return ''.join(title_parts).strip() if title_parts else " "

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@data-block-plugin-id="urban-blocks-body-or-summary"]//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        date_str = response.xpath('//lbj-title[@variant="date"]//time/text() | //time[@class="date"]/text()').get().strip()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")

    def get_authors(self, response):
        return response.xpath('//lbj-title[@variant="paragraph"]//lbj-link/text()').getall()

    def get_document_urls(self, response, entry=None) -> list:
        relative_urls = response.xpath("//lbj-button[contains(@class, 'file--application-pdf')]/@href").getall()
        base_url = "https://www.urban.org"
        return [base_url + url for url in relative_urls]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[a[@rel="next"]]/a/@href').get()
        if next_page:
            return next_page
        else:
            return None