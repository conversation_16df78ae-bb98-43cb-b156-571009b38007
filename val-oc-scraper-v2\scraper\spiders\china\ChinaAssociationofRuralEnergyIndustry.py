import json
import re
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaAssociationOfRuralEnergyIndustry(OCSpider):
    name = 'ChinaAssociationOfRuralEnergyIndustry'

    start_urls_names = {
        "https://carei.org.cn/zxtg" :"最新通告",                          
        "https://carei.org.cn/xhdt" :"协会动态"
    }
    
    api_start_urls = {
        "https://carei.org.cn/zxtg": {
            "url": "https://carei.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "0",
                "pageSize": "20",
                "selectCategory": "95015",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "NViqETz0Aeyx5xtu4Wl2g6mk14u_TbLAqB8K3CP_24QOOAe2W88566BfNnp0f87_krnOp6_MoH1VQIdSaEJxO0MSvAFNhSYAT7XBWaW8Epg1"
            },
        },
        "https://carei.org.cn/xhdt": {
             "url": "https://carei.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "0",
                "pageSize": "20",
                "selectCategory": "95016",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "NViqETz0Aeyx5xtu4Wl2g6mk14u_TbLAqB8K3CP_24QOOAe2W88566BfNnp0f87_krnOp6_MoH1VQIdSaEJxO0MSvAFNhSYAT7XBWaW8Epg1"
            },
        },
    }

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data=self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return None
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["pageIndex"] = payload.get("pageIndex")
        yield scrapy.FormRequest(
            url = api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;",
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["pageIndex"]
            },
        )
    
    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        data = json.loads(response.text)
        article_urls=[]
        hbp = HeadlessBrowserProxy()
        for item in data.get("Data", []):
            LinkUrl = item.get('LinkUrl')   
            url=hbp.get_proxy(f"https://carei.org.cn{LinkUrl}", timeout=20000)
            article_urls.append(url)
        return article_urls
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="w-detail"]//p/text()').getall())
    
    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='w-title']/text()").get()

    def get_href(self, entry) -> str:
        return entry

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date_text = response.xpath('//span[@class="w-createtime-item w-createtime-date"]//text()').get()
        # Dates were in this format -> "2024年28月1日"
        date_match = re.search(r'(\d{4})年(\d{1,2})月(\d{1,2})日', date_text)
        if date_match:
            year, month, day = date_match.groups()
            formatted_date = f"{year}-{int(month):02d}-{int(day):02d}"
            return formatted_date
        return None
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="w-detail"]//img//@src').getall()
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_authors(self,entry=None) -> list[str]:
        return entry

    def get_next_page(self, response,current_page ):
        max_data = response.json().get('TotalPages',[])
        current_page = int(response.meta.get('current_page'))+1
        return str(current_page) if current_page < max_data else None

    def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return None
        next_page = self.get_next_page(response, current_page)
        if next_page:
            logging.info(f"Fetching next page: {next_page}")
            payload = response.meta.get('payload')
            payload['pageIndex'] = next_page
            yield scrapy.Request(
            url = api_url,
            method = "GET",
            dont_filter=True,
            body = json.dumps(payload),
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["pageIndex"]
            },
        )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")
