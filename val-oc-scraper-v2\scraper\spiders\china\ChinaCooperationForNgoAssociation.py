from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaCooperationForNGOAssociation(OCSpider):
    name = "ChinaCooperationForNgoAssociation"

    start_urls_names = {
        "http://www.cango.org/plus/list.php?tid=129": "最新公告", # From this, One article https://www.cango.org/plus/view.php?aid=543 is getting failed with UnicodeDecodeError: 'gb2312'
        # "http://www.cango.org/plus/list.php?tid=97 ": "工作领域",   # Excluding this start url as this page only contains the list of below start_urls
        "http://www.cango.org/plus/list.php?tid=120": "乡村与社区发展",
        "http://www.cango.org/plus/list.php?tid=121" : "性别平等" ,
        "http://www.cango.org/plus/list.php?tid=124" : "气候变化与环境保护",
        "http://www.cango.org/plus/list.php?tid=123" : "社会组织发展与支持",
        "http://www.cango.org/plus/list.php?tid=125" : "公益研究与倡导",
        "http://www.cango.org/plus/list.php?tid=131" : "中外交流与国际合作"
    }

    article_xpath1 = '//div[@class="bjkk5k"]//ul[@class="inaa"]/li//a'
    article_xpath2 = '//div[@class="bjkk5k"]//ul[@class="inaa"]//td[@align="left"]//a'

    website_xpath = {
        "http://www.cango.org/plus/list.php?tid=129" : article_xpath2,
        "http://www.cango.org/plus/list.php?tid=120" : article_xpath1,
        "http://www.cango.org/plus/list.php?tid=121" : article_xpath1,
        "http://www.cango.org/plus/list.php?tid=124" : article_xpath1,
        "http://www.cango.org/plus/list.php?tid=123" : article_xpath1,
        "http://www.cango.org/plus/list.php?tid=125" : article_xpath1,
        "http://www.cango.org/plus/list.php?tid=131" : article_xpath1
    }

    charset = "gb2312"
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = response.xpath(self.website_xpath.get(response.meta.get('start_url')))
        return articles
    
    def get_href(self, entry) -> str:
        return entry.attrib['href']
    
    def get_title(self, response) -> str:
        return "".join(response.xpath('//td[@class="zzbtzz"]/text()').extract()).strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//span[@class="zzhui zz14zz zzlh"]//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//td[@class="zz12zz huizztt"]//text()').re_first(r"\d{4}-\d{2}-\d{2}") 
    
    def get_images(self, response) -> list:
       return response.xpath('//span[@class="zzhui zz14zz zzlh"]//img/@src').getall()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    # No pagination in any landing page
    def get_next_page(self, response) -> str:
        return None
