from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy   
from datetime import datetime
from scraper.utils.helper import body_normalization

class CentralBankOfMalaysiaNoticesAnnouncements(OCSpider):
    name = "CentralBankOfMalaysiaNoticesAnnouncements"

    start_urls_names = {
        # The url gives 202 status code sometimes dont fetch the content
       "https://www.bnm.gov.my/notices-announcements/-/tag/notices" : "News"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_date_mapping = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        current_year =response.meta.get('current_year',datetime.now().year)
        url =f"{start_url}-{current_year}"
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(url, timeout=30000), callback=self.parse,dont_filter=True
        )
        request.meta['start_url'] = start_url
        request.meta['current_year'] = current_year
        yield request

    def get_articles(self, response):
        articles = []
        mapping = {}
        rows = response.xpath('//tbody[@id="myTable"]/tr')
        print(rows)
        for row in rows:
            date = row.xpath('./td[1]//p//text()').get().strip()
            url = row.xpath('.//a/@href').get()
            title = row.xpath('./td[2]//a/text()').get().strip()
            print(url, date,title)
            if url and date:
                articles.append(url)
                mapping[url] = {
                    'title': title,
                    'date': date
                }
        self.article_to_date_mapping.update(mapping)
        print("[INFO] Extracted articles:", mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_to_date_mapping.get(response.url,{}).get('title')
    
    def get_body(self, response) -> str:
        # Body content cant be fetched due to 403 Forbidden error served by AWS CloudFront
        return body_normalization(response.xpath('//div[@class="article-content-cs"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> str:
        return self.article_to_date_mapping.get(response.url,{}).get('date')

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return []
  
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) :
        current_year = response.meta.get('current_year')
        previous_year = str(int(current_year)-1)
        if response.status == 200:
            return previous_year
        else:
            return

    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get('start_url')
        hbp=HeadlessBrowserProxy()
        next_year = self.get_next_page(response)
        url = f"{start_url}-{next_year}"
        print(next_year)
        yield scrapy.Request(
            hbp.get_proxy(url,timeout=30000),
            callback = self.parse_intermediate,
            dont_filter= True,
            meta = {
                "start_url" : start_url,
                "current_year" : next_year
            }
        ) 