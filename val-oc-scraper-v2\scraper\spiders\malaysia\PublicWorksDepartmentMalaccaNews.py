from scraper.OCSpider import OCSpider 
import re
from scraper.utils.helper import body_normalization

class PublicWorksDepartmentMalaccaNews(OCSpider):
    name = "PublicWorksDepartmentMalaccaNews"

    start_urls_names = {
        'https://www.jkrmlk.gov.my/1/aktiviti.php': 'activiti'
    }

    start_urls_with_no_pagination_set = {
        "https://www.jkrmlk.gov.my/1/aktiviti.php"
    }

    charset = "utf-8"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_url_title_date_mapping ={}

    def get_articles(self, response) -> list:
        child_article_list = []
        articles = response.xpath('//div[@class="col-md-4"]//div[@class="media-body"]//a/@href').getall()
        for article in articles:
            url_year_data = re.search(r'\d{4}', article)
            if url_year_data:
                full_url = f"https://www.jkrmlk.gov.my/1/news_content.php?id={url_year_data.group(0)}"
                child_article_list.append(full_url)
        return child_article_list
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@align="left"]//h4//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//td[@class="content_text"]//p/text()').getall())

    def get_date(self, response) -> str:
        malay_to_english = {
        "Jan": "January", "Januari": "January",
        "Feb": "February", "Februari": "February",
        "Mac": "March", 
        "Apr": "April", "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Jul": "July",
        "Julai": "July",
        "Ogos": "August",
        "Sep": "September", "Sept": "September", "September": "September",
        "Okt": "October", "Oktober": "October",
        "Nov": "November", "November": "November",
        }
        date_data = response.xpath('//div[@align="left"]//div//text()').get()
        date_parts = re.search('(\d+) (\w+) (\d+)', date_data)
        if date_parts:
            eng_month = malay_to_english.get(date_parts.group(2))
            date = f"{date_parts.group(1)} {eng_month} {date_parts.group(3)}"
            return date

    def date_format(self) -> str:
        return "%d %B %Y"

    def get_images(self, response) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        return []
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None