
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class MalaysiaHighwayAuthorityGuidelines(OCSpider):
    name = "MalaysiaHighwayAuthorityGuidelines"

    start_urls_names = {
      f"https://www.llm.gov.my/publication/guidelines/{i}": "News clipping"  #pagination not supported 
      for i in range(0, 77, 5)   
    
 }

    start_urls_with_no_pagination_set={
       "https://www.llm.gov.my/publication/guidelines/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
     articles= []
     for panel in response.xpath('//div[@class="panel panel-item"]'):
      title = panel.xpath('normalize-space(.//h5[@class="g-title"]/text())').get()
      image_url = panel.xpath('.//div[@class="panel-left"]/img/@src').get()
      raw_date = panel.xpath('normalize-space(.//p[@class="date"]/text())').get()
      body = panel.xpath('normalize-space(.//p[@class="description"]/following-sibling::p[1]/text())').get()
      articles.append(image_url)
      self.article_data_map[image_url]={ "date":raw_date,"title":title,"imgsrc":image_url,"body":body}
     return articles


    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
     return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("body", "")

    def get_images(self, response) -> list:
        return [response.url]
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
       date= self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
       date_str = " ".join(date.split()[:3])  
       date_obj = datetime.strptime(date_str, "%d %m %Y")
       formatted_date = date_obj.strftime("%Y-%m-%d")
       return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 