from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import unquote
from datetime import datetime
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse
import re

class CentralBankofMalaysiaRatesStatistics(OCSpider):
    name = "CentralBankofMalaysiaRatesStatistics"

    start_urls_names = {
       "https://www.bnm.gov.my/bnmstatement/-/tag/bnmstats" : "Speeches",
       "https://www.bnm.gov.my/publications/mhs" : "",
       "https://www.bnm.gov.my/web/guest/international-reserves-and-foreign-currency-liquidity" : ""
    }

    start_urls_with_no_pagination_set = {
        "https://www.bnm.gov.my/bnmstatement/-/tag/bnmstats"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        next_page_url = response.meta.get("next_page_url", response.url)
        hbp = HeadlessBrowserProxy()
        proxy_url = hbp.get_proxy(next_page_url, timeout=20000)
        yield scrapy.Request(
            url = proxy_url,
            callback=self.parse,
            meta = {
                "start_url" : start_url,
                "proxy_url" : next_page_url
            } 
        )

    def get_articles(self, response) -> list: 
        mapping = {}
        articles = [] 
        rows = []  
        if "guest" in response.url:
            rows = response.xpath('//tbody/tr[td]')
            for row in rows:
                url = row.xpath('.//a/@href').get()
                title = ''.join(row.xpath('.//a//node()').getall()).strip()
                date_match = re.search(r'\((\d{1,2} \w+ \d{4})\)', title or "")
                date = date_match.group(1) if date_match else None
                if url and title and date:
                    normalized_link = unquote(url).rstrip('/')
                    articles.append(normalized_link)
                    mapping[normalized_link] = {
                        'title': title.strip(),
                        'pdf': normalized_link,
                        'date': date
                    }
        elif "mhs" in response.url:
            rows = response.xpath('//table[@id="result"]/tbody/tr[td]')
        elif "bnmstat" in response.url:
            rows = response.xpath('//tbody/tr[td]')
        if rows and ("guest" not in response.url):
            for row in rows:
                date = row.xpath('./td[1]//text()').re_first(r'\d{1,2}\s+\w+\s+\d{4}')
                title = row.xpath('./td[2]//a/text()').get() 
                url = row.xpath('./td[2]//a/@href').get() 
                if url and title and date:
                    normalized_link = unquote(url).rstrip('/')
                    articles.append(normalized_link)
                    mapping[normalized_link] = {
                        'title': title.strip(),
                        'pdf': normalized_link,
                        'date': date
                    }
        self.article_to_pdf_mapping.update(mapping)
        return articles
 
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ''
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> str:
        start_url = response.meta.get("start_url")
        normalized_url = unquote(response.url).rstrip('/')
        date = None
        if "guest" in start_url:
            date_text = self.article_to_pdf_mapping.get(normalized_url,{}).get('date')
            date_obj = datetime.strptime(date_text, "%d %B %Y")
            return date_obj.strftime("%d %b %Y") 
        else:
            return self.article_to_pdf_mapping.get(normalized_url,{}).get('date')

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
  
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        start_url = response.meta.get("start_url")
        if "bnmstats" in start_url :
            return None
        else :
           return response.xpath("//a[normalize-space(text())='Next']/@href").get()

    def go_to_next_page(self, response, start_url, current_page=None):
        next_page_url = self.get_next_page(response)
        if next_page_url:
            self.logger.info(f"Following next page: {next_page_url}")
            yield scrapy.Request(
                url=next_page_url,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "proxy_url" : next_page_url
                },
                dont_filter=True
            )