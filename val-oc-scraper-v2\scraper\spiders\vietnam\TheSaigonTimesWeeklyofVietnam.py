from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class TheSaigonTimesWeeklyofVietnam(OCSpider):
    name = "TheSaigonTimesWeeklyofVietnam"

    start_urls_names = {
        "https://thesaigontimes.vn/noi-bat-2/": "News"
    }

    start_urls_with_no_pagination_set = {
         "https://thesaigontimes.vn/noi-bat-2/"  # Pagination is not suported
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    articles_to_date = {}

    def get_articles(self, response) -> list:  
        return list(set(response.xpath('//h3[@class="entry-title td-module-title"]//a//@href').getall()))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return  response.xpath('//h1[@class="tdb-title-text"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="tdb-block-inner td-fix-index"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d-%m-%Y"

    def get_date(self, response) -> str:
        date= response.xpath('//div[@class="tdb-block-inner td-fix-index"]//time[@class="entry-date updated td-module-date"]//text()').get()
        dt = datetime.strptime(date.strip(), "%H:%M %d/%m/%Y")
        return  dt.strftime("%d-%m-%Y")

    def get_authors(self, response):
        return  ""
    
    def get_document_urls(self, response, entry=None)->list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None