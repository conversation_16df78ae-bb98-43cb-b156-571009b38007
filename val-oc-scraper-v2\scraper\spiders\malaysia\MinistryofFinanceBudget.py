
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

import scrapy

class MinistryofFinanceBudget(OCSpider):
    name = "MinistryofFinanceBudget"
    year = 1995 
    start_urls_names = {
        f"https://www.mof.gov.my/portal/arkib/belanjawan/ub{year}.html": "News"
        for year in range(2022,2009,-1)
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:
        articles= []
        for article in response.xpath('//div[@class="furniture-middle"]//p'):
           link = article.xpath(".//a/@href").get()
           title = article.xpath(".//a//text()").get()
           #date = article.xpath(".//td[2]//text()").get()
           articles.append(link)
           self.article_data_map[link]={"title":title,"link":link}
        print(list(set(articles)))
        print(self.article_data_map)
        return (list(set(articles)))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
       # return  body_normalization(response.xpath('//div[@class="entry entryfull px-1"]//p//text()').getall())
         return ""
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        #get_date = list(self.start_urls_names.key())[0]
        #print(get_date)
        #date=get_date.split(".")[0]
        #year=date[-4:0]

        parsed_date = dateparser.parse(str(self.year), languages=['ms','en'])
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
        #//*[@id="core"]/div/div[2]/p[2]/a//@href
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 
