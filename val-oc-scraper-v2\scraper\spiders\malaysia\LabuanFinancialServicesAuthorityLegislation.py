from scraper.OCSpider import OCSpider
import re
from urllib.parse import unquote,quote
import json
import ast

class LabuanFinancialServicesAuthorityLegislation(OCSpider):
    name = "LabuanFinancialServicesAuthorityLegislation"

    start_urls_names = {
        'https://www.labuanfsa.gov.my/legislation-guidelines/legislation/legislation' : 'Legislation'  
    }

    start_urls_with_no_pagination_set = {
        'https://www.labuanfsa.gov.my/legislation-guidelines/legislation/legislation' # Pagination is not supported
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'industry_association'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        # articles = []
        # try:
        #     script_text = response.xpath('//script[contains(text(), "$X.PPG")]/text()').get()
        #     if not script_text:
        #         self.logger.warning("Could not find $X.PPG script in response")
        #         return articles
        #     # Capture JSON.parse content across multiple lines
        #     match = re.search(r'\$X\.PPG\s*=\s*JSON\.parse\("([\s\S]+?)"\);', script_text)
        #     if not match:
        #         self.logger.warning("Could not find JSON.parse pattern in script")
        #         return articles
        #     json_str = match.group(1)
        #     # Convert JavaScript-style escaped string into proper Python string
        #     try:
        #         # Safely evaluate the escaped string into a normal Python str
        #         json_str = ast.literal_eval(f'"{json_str}"')
        #     except Exception as e:
        #         self.logger.error(f"Failed to literal_eval JSON string: {e}")
        #         return articles
        #     try:
        #         ppg_data = json.loads(json_str)
        #     except Exception as e:
        #         self.logger.error(f"Still invalid JSON: {e}\nPreview: {json_str[:200]}")
        #         return articles
        #     for block in ppg_data.values():
        #         if not isinstance(block, dict):
        #             continue
        #         for page_items in block.get("pages", {}).values():
        #             if not isinstance(page_items, list):
        #                 continue
        #             for item in page_items:
        #                 title = item.get("Article Name") or item.get("Page Name")
        #                 pdf_link = item.get("Article URL") or item.get("Page Address")
        #                 if not title or not pdf_link:
        #                     continue
        #                 year_match = re.search(r'(19|20)\d{2}', title)
        #                 if not year_match:
        #                     continue
        #                 year = year_match.group(0)
        #                 pdf_link = response.urljoin(unquote(pdf_link.strip()))
        #                 self.article_title_pdf_mapping[pdf_link] = (title.strip(), pdf_link, year)
        #                 articles.append(pdf_link)
        # except Exception as e:
        #     self.logger.error(f"Error parsing articles: {e}")
        # self.logger.info(f"Found {len(articles)} articles")
        # return articles
        articles = []
        script_text = response.xpath('//script[contains(text(), "$X.PPG")]/text()').get()
        match = re.search(r'\$X\.PPG\s*=\s*JSON\.parse\("(.+?)"\);', script_text or "", re.DOTALL)
        json_str = match.group(1).encode().decode('unicode_escape')
        ppg_data = json.loads(json_str)
        for block in ppg_data.values():
            pages = block.get("pages", {})
            for page_items in pages.values():
                for item in page_items:
                    title = item.get("Article Name") or item.get("Page Name")
                    pdf_link = item.get("Article URL") or item.get("Page Address")
                    year_match = re.search(r'(19|20)\d{2}', title or "")
                    if title and pdf_link and year_match:
                        year = year_match.group(0)
                        title = title.strip()
                        pdf_link = response.urljoin(quote(pdf_link.strip(), safe=':/'))
                        self.article_title_pdf_mapping[pdf_link] = (title, pdf_link, year)
                        articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[2]

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None) -> list[str]:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[1]

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None