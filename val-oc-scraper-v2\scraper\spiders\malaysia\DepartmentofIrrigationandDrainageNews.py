from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import unquote
from urllib.parse import urljoin

class DepartmentOfIrrigationAndDrainageNews(OCSpider):
    name = "DepartmentOfIrrigationAndDrainageNews"

    start_urls_names = {
       "https://www.water.gov.my/index.php/database_stores/store_view/4" : "News" 
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = start_url
        yield request

    def get_articles(self, response) -> list: 
        mapping = {}
        articles = [] 
        rows = response.xpath('//div[@id="DataTables_Table_0_wrapper"]//tr')
        for row in rows:
            title = row.xpath('./td[2]/text()').get()
            date = row.xpath('./td[3]/text()').get()
            relative_url = row.xpath('./td[last()]/a/@href').get()

            if title and date and relative_url:
                full_url = urljoin(response.url, relative_url.strip())
                if full_url.startswith("http"):
                    articles.append(full_url)
                    mapping[full_url.rstrip('/')] = {
                        'title': title.strip(),
                        'date': date.strip()
                        # don't store 'pdf' here unless it is explicitly a direct link to a .pdf
                    }
                else:
                    self.logger.warning(f"Skipped invalid URL: {full_url}")
        
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ''
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url,{}).get('date')

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        pdf_url = response.xpath('//a[contains(@href, ".pdf")]/@href').get()
        if pdf_url:
            return urljoin(response.url, pdf_url.strip())
        return None
  
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[(contains(@class,"paginate_button") and contains(text(),"Next"))]/@href').get()
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        next_page =self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                next_page,
                callback=self.parse_intermediate,
                meta ={
                 "start_url": start_url 
                })
        else:
            self.logger.info("No next page found.") 