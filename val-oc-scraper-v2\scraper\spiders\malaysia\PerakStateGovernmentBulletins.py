from scraper.OCSpider import OCSpider
import re

class PerakStateGovernmentBulletins(OCSpider):
    name = "PerakStateGovernmentBulletins"
    
    start_urls_names = {
        "https://www.perak.gov.my/index.php/suk-perak/penerbitan/buletin": "<PERSON>raj<PERSON>"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//div[@class="uk-width-1-1@m"]//table//tbody//tr//td'):
            url = article.xpath(".//a/@href").get()
            title = article.xpath(".//a//text()").get()
            date = article.xpath(".//a//text()").get()
            if url and title and date:
                full_url = url
                title = title.strip()
                clean_date = date.strip() 
                match = re.search(r'\b(\d{1,2}/\d{4})\b', clean_date)
                if match:
                    date_str = match.group(1)
                    # print(date_str)  # Output: 2/2024  
                self.article_data_map[full_url] = {"title": title, "date": date_str, "pdf": full_url}
                articles.append(full_url) 
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m/%Y"

    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None