from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re 

class MississippiDepartmentOfEmploymentSecurity(OCSpider):
    name = "MississippiDepartmentOfEmploymentSecurity"
    
    country = "US"
    
    start_urls_names = {
        "https://mdes.ms.gov/news/": "News Releases",
    }

    charset = "utf-8"
      
    visited_links = set()  # Keep track of visited URLs to avoid reprocessing

    @property
    def language(self): 
        return "English"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@id="page_content"]//h2[@class="newstitle"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@id="page_content"]/h1[@class="newstitle"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="page_content"]//p[not(contains(text(), "For Immediate Release")) and not(contains(text(), "Date:")) and not(contains(text(), "# # #"))]').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> Optional[str]:
        url = response.url 
        match = re.search(r'/news/(\d{4})/(\d{2})/(\d{2})/', url)
        year, month, day = match.groups()
        date_obj = datetime.strptime(f"{year}-{month}-{day}", "%Y-%m-%d")
        return date_obj.strftime(self.date_format()) 
          
    def get_authors(self, response):
        author = response.xpath('//div[@id="page_content"]/p[contains(text(), "–")]/text()').get()
        if author:
            author = author.strip().replace("–", "").strip()
            return [author]
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        current_page_text = response.xpath('//div[@class="pagination"]/a[@class="pagelink currentresult"]/text()').get()
        try:
            current_page = int(current_page_text)
        except:
            current_page = 1
        next_page_number = str(current_page + 1)
        next_page_url = response.xpath(f'//div[@class="pagination"]/a[text()="{next_page_number}"]/@href').get()
        if next_page_url:
            return response.urljoin(next_page_url)
        return None  