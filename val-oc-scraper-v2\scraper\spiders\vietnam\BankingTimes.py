from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy

class BankingTimes(OCSpider):
    name = "BankingTimes"

    start_urls_names = {
        "https://thoibaonganhang.vn/ngan-hang": "News" # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://thoibaonganhang.vn/ngan-hang"
    } 
    
    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:  
        return response.xpath('//h3[@class="article-title f0"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="article-detail-title f0 clearfix"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="bx-cat __MASTERCMS_CONTENT fw clearfix"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return response.xpath('//span[@class="format_date"]//text()').re_first("\d{1,2}/\d{1,2}/\d{4}")

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
       return None