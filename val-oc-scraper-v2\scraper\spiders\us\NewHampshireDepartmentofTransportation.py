import scrapy
import json
from datetime import datetime
from typing import Union
from parsel import Selector
from scrapy.exceptions import IgnoreRequest
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class NewHampshireDepartmentOfTransportation(OCSpider):
    name = 'NewHampshireDepartmentOfTransportation'

    country = "US"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 5,
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000

    start_urls_names = {
        'https://www.dot.nh.gov/news-and-media': "News"
    }

    charset = "utf-8"

    api_start_url = {
        'https://www.dot.nh.gov/news-and-media': {
            'url': 'https://www.dot.nh.gov/content/api/news',
            'headers': {
                'Accept': 'application/json'
            },
            'params': {
                'q': '',
                'textsearch': '',
                'sort': 'field_date%7Cdesc%7CALLOW_NULLS',
                'view': 'list',
                'page': 0,
                'size': 10
            }
        }
    }

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        current_page = response.meta.get("current_page", 1)
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            return
        params = api_data['params'].copy()
        params['page'] = current_page - 1
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        api_url = f"{api_data['url']}?{query_string}"
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers=api_data['headers'],
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data['url'],
                "current_page": current_page
            },
            dont_filter=True
        )

    def get_articles(self, response) -> list:
        try:
            if response.text.strip().startswith("<html>"):
                selector = Selector(response.text)
                json_text = selector.xpath('//pre/text()').get()
            else:
                json_text = response.text
            data = json.loads(json_text)
            results = data.get("data", [])
            articles = []
            for item in results:
                if item.get("list_content"):
                    href = Selector(text=item['list_content']).xpath('//a/@href').get()
                    if href:
                        # Ensure full URL
                        if href.startswith('/'):
                            href = f"https://www.dot.nh.gov{href}"
                        articles.append(href)
            return articles
        except Exception as e:
            self.logger.error(f"Failed to extract articles: {e}")
            self.logger.debug(f"Response snippet: {response.text[:1000]}")
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title_selectors = [
            "//h1//text()",
            "//h1/text()",
            "//title/text()",
            "//meta[@property='og:title']/@content"
        ]
        for selector in title_selectors:
            title = response.xpath(selector).get()
            if title:
                # Clean up title - remove site name if present
                title = title.strip()
                if " | Department of Transportation" in title:
                    title = title.replace(" | Department of Transportation", "")
                return title
        self.logger.warning(f"No title found for {response.url}")
        return ""

    def get_body(self, response) -> str:
        body_selectors = [
            "//div[contains(@class,'field--name-body')]//p//text()",
            "//div[contains(@class,'field--name-body')]//text()",
            "//div[contains(@class,'content')]//p//text()",
            "//main//p//text()",
            "//article//p//text()"
        ]
        for selector in body_selectors:
            body_texts = response.xpath(selector).getall()
            if body_texts:
                return body_normalization(body_texts)
        fallback_text = response.xpath("//main//text() | //article//text()").getall()
        if fallback_text:
            meaningful_text = [
                text.strip() for text in fallback_text 
                if text.strip() and len(text.strip()) > 10 and 
                not any(skip in text.lower() for skip in ['javascript', 'css', 'menu', 'navigation'])
            ]
            return body_normalization(meaningful_text[:5])
        
        self.logger.warning(f"No body content found for {response.url}")
        return ""

    def get_images(self, response) -> list:
        image_selectors = [
            "//div[contains(@class,'field--name-body')]//img/@src",
            "//main//img/@src",
            "//article//img/@src"
        ]
        images = []
        for selector in image_selectors:
            img_urls = response.xpath(selector).getall()
            for img_url in img_urls:
                if img_url.startswith('/'):
                    img_url = f"https://www.dot.nh.gov{img_url}"
                images.append(img_url)
        return list(set(images))

    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        date_selectors = [
            "//div[@class='date']//span/following::text()[1]",
            "//div[@class='date']//text()",
            "//div[contains(@class,'date')]//text()",
            "//time/@datetime",
            "//time//text()",
            "//meta[@property='article:published_time']/@content",
            "//span[contains(@class,'date')]//text()",
            "//div[contains(@class,'meta')]//text()[contains(., '20')]",
            "//div[contains(@class,'info')]//text()[contains(., '20')]"
        ]
        for selector in date_selectors:
            try:
                date_candidates = response.xpath(selector).getall()
                if not date_candidates:
                    date_candidates = [response.xpath(selector).get()]
                for raw_date in date_candidates:
                    if not raw_date:
                        continue
                    cleaned_date = raw_date.strip()
                    if not cleaned_date or len(cleaned_date) < 8:
                        continue
                    date_formats = [
                        '%B %d, %Y',
                        '%m/%d/%Y',
                        '%Y-%m-%d',
                        '%d %B %Y',
                        '%B %d %Y',
                        '%m-%d-%Y',
                    ]
                    for date_format in date_formats:
                        try:
                            parsed = datetime.strptime(cleaned_date, date_format)
                            return parsed.strftime(self.date_format())
                        except ValueError:
                            continue
                    import re
                    date_pattern = r'(\w+ \d{1,2}, \d{4})'
                    match = re.search(date_pattern, cleaned_date)
                    if match:
                        try:
                            parsed = datetime.strptime(match.group(1), self.date_format())
                            return parsed.strftime(self.date_format())
                        except ValueError:
                            continue
                            
            except Exception as e:
                self.logger.debug(f"Date extraction attempt failed: {e}")
                continue
        try:
            import re
            page_text = ' '.join(response.xpath("//text()").getall())
            year_matches = re.findall(r'20\d{2}', page_text)
            if year_matches:
                current_year = year_matches[0]
                fallback_date = f"January 1, {current_year}"
                self.logger.warning(f"Using fallback date {fallback_date} for {response.url}")
                return fallback_date
        except Exception:
            pass
        current_date = datetime.now().strftime(self.date_format())
        self.logger.warning(f"No date found, using current date {current_date} for {response.url}")
        return current_date

    def get_authors(self, response):
        author_selectors = [
            "//div[@class='office-info']//span[@itemprop='contactPoint']/text()",
            "//div[contains(@class,'author')]//text()",
            "//span[contains(@class,'author')]//text()",
            "//div[contains(@class,'byline')]//text()",
            "//meta[@name='author']/@content"
        ]
        authors = []
        for selector in author_selectors:
            author_list = response.xpath(selector).getall()
            authors.extend([author.strip() for author in author_list if author.strip()])
        seen = set()
        unique_authors = []
        for author in authors:
            if author not in seen:
                seen.add(author)
                unique_authors.append(author)
        return unique_authors

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page) -> Union[None, int]:
        try:
            if response.text.strip().startswith("<html>"):
                selector = Selector(response.text)
                json_text = selector.xpath('//pre/text()').get()
            else:
                json_text = response.text
            data = json.loads(json_text)
            results = data.get("data", [])
            if len(results) >= 10:
                return current_page + 1
        except Exception as e:
            self.logger.warning(f"Pagination detection failed: {e}")
        return None

    def go_to_next_page(self, response, start_url, current_page=1):
        next_page = self.get_next_page(response, current_page)
        if next_page is None:
            return
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            return
        params = api_data['params'].copy()
        params['page'] = next_page - 1
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        api_url = f"{api_data['url']}?{query_string}"
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers=api_data["headers"],
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "current_page": next_page,
                "api_url": api_data['url']
            },
            dont_filter=True
        )