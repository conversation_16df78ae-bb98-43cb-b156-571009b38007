from typing import List, Union
import scrapy
from scraper.OCSpider import OCSpider
import json
from datetime import datetime

class MinistryOfJusticeVietnam(OCSpider):
    name = "MinistryOfJusticeVietnam"
    
    start_urls_names = {
        "https://qlvb.moj.gov.vn/cddh/Pages/chidaodieuhanh.aspx": "",
    }

    start_urls_with_no_pagination_set = {}

    country = "Vietnam"
    
    charset = "iso-8859-1"

    article_data_map = {}
    
    api_start_url = {
        'https://qlvb.moj.gov.vn/cddh/Pages/chidaodieuhanh.aspx': {
            'url': 'https://qlvb.moj.gov.vn/Vbden/api/VanBanCDDH/CDDHREQUEST?do=QUERYDATASOLR&Urlsite=/botp',
            'payload': {
                "draw": "1",
                "columns[0][data]": "0",
                "columns[0][name]": "",
                "columns[0][searchable]": "true",
                "columns[0][orderable]": "false",
                "columns[0][search][value]": "",
                "columns[0][search][regex]": "false",
                "columns[1][data]": "function",
                "columns[1][name]": "vbdiSoKyHieu",
                "columns[1][searchable]": "true",
                "columns[1][orderable]": "true",
                "columns[1][search][value]": "",
                "columns[1][search][regex]": "false",
                "columns[2][data]": "function",
                "columns[2][name]": "vbdiNgayBanHanh",
                "columns[2][searchable]": "true",
                "columns[2][orderable]": "true",
                "columns[2][search][value]": "",
                "columns[2][search][regex]": "false",
                "columns[3][data]": "function",
                "columns[3][name]": "vbdiCoQuanBanHanh",
                "columns[3][searchable]": "true",
                "columns[3][orderable]": "true",
                "columns[3][search][value]": "",
                "columns[3][search][regex]": "false",
                "columns[4][data]": "function",
                "columns[4][name]": "vbdiTrichYeu",
                "columns[4][searchable]": "true",
                "columns[4][orderable]": "true",
                "columns[4][search][value]": "",
                "columns[4][search][regex]": "false",
                "columns[5][data]": "function",
                "columns[5][name]": "Title",
                "columns[5][searchable]": "true",
                "columns[5][orderable]": "true",
                "columns[5][search][value]": "",
                "columns[5][search][regex]": "false",
                "order[0][column]": "2",
                "order[0][dir]": "desc",
                "start": "0",
                "length": "20",
                "search[value]": "",
                "search[regex]": "false",
                "vbdiLinhVuc": "0",
                "vbdiLoaiVanBan": "0",
                "lstvbdiCoQuanBanHanh": "",
                "vbdiCoQuanBanHanh": "0",
                "lstIDNot": "",
                "currentUserID": "0",
                "SearchInSimple": "FullText",
                "isPublic": "1",
                "isTrangChu": "1"
            },
        },
    }
    
    def parse_intermediate(self, response):
        content_type = (response.headers.get(b"Content-Type") or b"").decode("utf-8", "ignore").lower()
        text_stripped = response.text.strip()
        looks_like_json_payload = text_stripped.startswith("{") and ("\"Data\"" in text_stripped or "\"aaData\"" in text_stripped)
        is_json_response = content_type.startswith("application/json") or (content_type.startswith("text/html") and looks_like_json_payload)
        if is_json_response:
            try:
                hrefs = self.get_articles(response) or []
            except Exception:
                hrefs = []
            if hrefs:
                self.crawler.stats.inc_value("count_summary__articles_crawled", len(hrefs))
            start_url = response.meta.get("start_url", "")
            for href in hrefs:
                yield response.follow(
                    href,
                    callback=self.parse_article,
                    meta={
                        "entry": href,
                        "start_url": start_url,
                    },
                )
            if self.get_page_flag() and hrefs:
                current_start = int((response.meta.get("payload") or {}).get("start", "0"))
                yield from self.go_to_next_page(response, start_url=start_url, current_page=current_start)
        else:
            start_url = response.meta.get("start_url") or response.url
            api_data = self.api_start_url.get(start_url)
            if not api_data:
                return
            api_url = api_data["url"]
            current_draw = int(api_data["payload"].get("draw", "1"))
            api_data["payload"]["draw"] = str(current_draw)
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "X-Requested-With": "XMLHttpRequest",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Referer": start_url,
            }
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers=headers,
                dont_filter=True,
                formdata=api_data["payload"],
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": api_data["payload"].copy(),
                },
            )

    article_data_map ={}

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            rows = data.get("Data", []) or data.get("data", []) or data.get("aaData", [])
            articles = []
            for i, row in enumerate(rows):
                doc_id = row.get("ID")
                if not doc_id:
                    continue
                url = f"/cddh/Pages/chidaodieuhanh.aspx?idvb={doc_id}"
                full_url = response.urljoin(url)
                def safe_extract(field_value):
                    if isinstance(field_value, str):
                        return field_value.strip()
                    elif isinstance(field_value, dict):
                        return field_value.get("Title", "").strip() if field_value.get("Title") else ""
                    elif field_value is None:
                        return ""
                    else:
                        return str(field_value).strip()
                title = safe_extract(row.get("vbdiTrichYeu"))
                date = safe_extract(row.get("vbdiNgayBanHanh"))
                docno = safe_extract(row.get("vbdiSoKyHieu"))
                agency = safe_extract(row.get("vbdiCoQuanBanHanh"))
                smid = safe_extract(row.get("SMID"))
                file_attachments = row.get("ListFileAttach", []) or []
                file_urls = []
                if file_attachments:
                    for attachment in file_attachments:
                        if isinstance(attachment, dict) and attachment.get("Url"):
                            file_urls.append(response.urljoin(attachment["Url"]))
                self.article_data_map[full_url] = {
                    "title": title,
                    "date": date,
                    "docno": docno,
                    "agency": agency,
                    "file_urls": file_urls,
                    "smid": smid,
                    "doc_id": doc_id,
                }
                articles.append(full_url)
            if articles:
                return articles
        except json.JSONDecodeError:
            pass
        except Exception:
            pass
        rows = response.xpath("//table[@id='grid_ThuTuc']//tbody/tr")
        if rows:
            articles = []
            for row in rows:
                detail_href = row.xpath(".//td[2]//a/@href").get() or row.xpath(".//td[5]//a/@href").get()
                if not detail_href:
                    continue
                url = response.urljoin(detail_href)
                title = row.xpath("normalize-space(.//td[5]//a)").get()
                date = row.xpath("normalize-space(.//td[contains(@class,'sorting_1')])").get() or row.xpath("normalize-space(.//td[3])").get()
                docno = row.xpath("normalize-space(.//td[2]//a)").get()
                agency = row.xpath("normalize-space(.//td[4])").get()
                if title and date:
                    self.article_data_map[url] = {
                        "title": title, "date": date, "docno": docno, "agency": agency,
                        "allfiles_api": row.xpath(".//td[6]//a[contains(@class,'get-allfile')]/@data-url").get()
                    }
                    articles.append(url)
            if articles:
                return articles
        hrefs = response.xpath('//ul[@id="table0"]//li/a/@href').getall()
        if hrefs:
            return [response.urljoin(u) for u in hrefs]
        return []

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        entry_url = response.request.meta.get('entry') or response.url
        title = self.article_data_map.get(entry_url, {}).get("title", "")
        return title
         
    def get_body(self, response) -> str:
        entry_url = response.request.meta.get('entry') or response.url
        article_data = self.article_data_map.get(entry_url, {})
        parts = []
        if article_data.get("docno"):
            parts.append(f"Document Number: {article_data['docno']}")
        if article_data.get("agency"):
            parts.append(f"Issuing Agency: {article_data['agency']}")
        if article_data.get("date"):
            parts.append(f"Issue Date: {article_data['date']}")
        body = "\n".join(parts)
        return body
    
    def get_images(self, response) -> List[str]:
        return ""
    
    def date_format(self) -> str:
        return '%d-%m-%Y'

    def get_date(self, response) -> str:
        raw_date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if not raw_date:
            return ""
        try:
            dt = datetime.fromisoformat(raw_date)
            return dt.strftime("%d-%m-%Y")
        except Exception:
            return raw_date 
        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        entry_url = response.request.meta.get('entry') or response.url
        article_data = self.article_data_map.get(entry_url, {})
        file_urls = article_data.get("file_urls", [])
        if file_urls:
            return file_urls
        else:
            return [response.url]
    
    def get_next_page(self, response, current_page) -> Union[None, int]:
        try:
            data = json.loads(response.text)
            total = int(data.get("recordsTotal") or data.get("iTotalRecords") or 0)
        except Exception:
            return None
        payload = response.meta.get("payload", {}) or {}
        cur_start = int(payload.get("start", str(current_page or 0)))
        length = int(payload.get("length", "20"))
        next_start = cur_start + length
        if total and next_start < total:
            return next_start
        else:
            return None
            
    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page=0):
        api_url = response.meta.get("api_url")
        payload = (response.meta.get("payload") or {}).copy()
        if not api_url or not payload:
            return
        next_start = self.get_next_page(response, current_page=current_page)
        if next_start is None:
            return
        payload["start"] = str(next_start)
        payload["draw"] = str(int(payload.get("draw", "1")) + 1)
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "X-Requested-With": "XMLHttpRequest",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Referer": start_url,
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            formdata=payload,
            dont_filter=True,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_start,
            },
        )