from scraper.OCSpider import OCSpider
import scrapy

class MinistryOfEconomyDeputyMinistersSpeeches (OCSpider):
    name = "MinistryOfEconomyDeputyMinistersSpeeches"

    start_urls_names = {
        "https://ekonomi.gov.my/ms/media/koleksi-teks-ucapan/ucapan-timbalan-menteri": "Speeches" # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://ekonomi.gov.my/ms/media/koleksi-teks-ucapan/ucapan-timbalan-menteri"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//div//table//tbody"):
            url = article.xpath(".//tr//a//@href").get()
            title = article.xpath(".//tr//a//text()").get()
            date = article.xpath(".//td[@class='views-field views-field-field-date']//text()").get()
            author = article.xpath("//td[@class='views-field views-field-field-penyampai']//text()").get()
            if url:
                full_url = url
                title= title.strip()
                date= date.strip()
                author = author.strip()
                self.article_data_map[full_url]={"full_url": url, "date": date, "title": title, "author": author}
            articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if date:
            month_map = {
                    'Jan': 'January',
                    'Feb': 'February',
                    'Mac': 'March',
                    'Apr': 'April',
                    'Mei': 'May',
                    'Jun': 'June',
                    'Jul': 'July',
                    'Ogos': 'August',
                    'Sep': 'September',
                    'Okt': 'October',
                    'Nov': 'November',
                    'Dis': 'December'
                }
            for malay, eng in month_map.items():
                if malay in date:
                    date = date.replace(malay, eng)
                    break
            return date
        return date

    def get_authors(self, response):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("author", "")
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        # No next page to scrape
        return None