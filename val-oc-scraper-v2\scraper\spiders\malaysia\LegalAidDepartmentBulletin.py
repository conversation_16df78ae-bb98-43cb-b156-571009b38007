from scraper.OCSpider import OCSpider
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
from urllib.parse import unquote, urljoin
import re
import requests

class LegalAidDepartmentBulletin(OCSpider):
    name = "LegalAidDepartmentBulletin"

    start_urls_names = {
        'https://www.jbg.gov.my/index.php/ms-my/icons/buletin': 'News'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.jbg.gov.my/index.php/ms-my/icons/buletin'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'justice'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_to_date_map = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        yield scrapy.Request(
            hbp.get_proxy(response.url, timeout=30000),
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": response.url
            }
        )

    def get_articles(self, response):
        base_url = "https://www.jbg.gov.my/"
        articles = []
        mapping = {}
        for element in response.xpath('//div[@itemprop="articleBody"]//tr//a'):
            url = element.xpath('./@href').get()
            title = element.xpath('./text()').get()
            date = None
            if title:
                match = re.search(r'\b\d{1}/\d{4}\b', title)
                if match:
                    date = match.group(0)
            if title and url and date:
                full_url = urljoin(base_url, url)
                # Only process if URL is valid
                if full_url.lower().startswith("http") and self.is_url_ok(full_url):
                    normalized_url = unquote(full_url)
                    articles.append(normalized_url)
                    mapping[normalized_url] = {
                        'title': title.strip(),
                        'date': date.strip(),
                        'pdf': normalized_url if normalized_url.lower().endswith(".pdf") else None
                    }
        self.article_to_date_map.update(mapping)
        return list(set(articles))

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url, {}).get('title', '')

    def get_body(self, response) -> str:
        return ''

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url)
        return self.article_to_date_map.get(normalized_url, {}).get('date', '')

    def date_format(self) -> str:
        return "%m/%Y"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        normalized_url = unquote(response.url)
        pdf_url = self.article_to_date_map.get(normalized_url, {}).get('pdf')
        if pdf_url and pdf_url.lower().startswith("http"):
            return [pdf_url]
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None
    
    def is_url_ok(self, url):
        try:
            r = requests.head(url, allow_redirects=True, timeout=10)
            return r.status_code == 200
        except requests.RequestException:
            return False