# from typing import List
# from scraper.OCSpider import OCSpider
# import scrapy

# class GEMDALE(OCSpider):
#     name = "GEMDALE"

#     start_urls_names = {
#         "https://www.gemdale.com/investor.html#slide3": "金地集团",
#     }

#     article_data_map ={}

#     HEADLESS_BROWSER_WAIT_TIME = 100 
    
#     custom_settings = {
# 		"DOWNLOADER_MIDDLEWARES" : {
# 			'scraper.middlewares.HeadlessBrowserProxy': 350,
# 		},

# 	}
#     api_start_urls = {
#         'https://www.gemdale.com/investor.html#slide3':{
#             'url' : 'https://www.hundsun.com/v1api/index.php/news/gsxwpage',
#             'payload': {
#                 "method":"GetInvestor1",
#                 "coid":"116",
#                 "typeId":"1",
#                 "pageIndex":"1",
#             },
#             'headers':{
#                 "User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
#                 "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
#                 "Cookie" : "acw_tc=ac11000117459409087912588e005b45dba36184b7582dc77153916da999da; acw_sc__v2=6810f1afad344ceffbf766cd3f6c90facbd70a3f; SERVERID=b3f13cc6355af729adf1e8dffe65b441|1745941082|1745940908",
#             }
#         },
#     }

#     def parse_intermediate(self, response):
#         start_url = response.request.meta['start_url']
#         api_data = self.api_start_urls.get(start_url)
#         if not api_data:
#             self.logger.error(f"No API configuration found for start_url: {start_url}")
#             return
#         headers= api_data["headers"]
#         payload = api_data["payload"]
#         current_page=payload["pageIndex"]
#         yield scrapy.FormRequest(
#             url=api_data["url"],
#             method="POST",
#             headers=headers,
#             formdata=payload,
#             callback=self.parse,
#             dont_filter=True,
#             meta={
#                 "start_url": start_url,
#                 "api_url": api_data["url"],
#                 "payload": payload,
#                 "current_page": current_page,
#             },
#         )

#     charset = "iso-8859-1"

#     @property
#     def source_type(self) -> str:
#         return "private_enterprise"
    
#     @property
#     def timezone(self) -> str:
#         return "Asia/Shanghai"
    
#     def get_articles(self, response) -> list:
#         try:
#             articles = response.xpath("//div[@class='list']")
#             article_urls = []
#             for article in articles:
#                 url = article.xpath(".//div[@class='li']//a/@href").get()
#                 title = article.xpath(".//h3//text()").get()
#                 date = article.xpath(".//div[@class='time']//text()").get() 
#                 print("aaa",url,title,date)
#                 if url and title and date:
#                     full_url = response.urljoin(url.strip())
#                     clean_date = date.strip().replace("\xa0", " ") 
#                     self.article_data_map[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
#                     article_urls.append(full_url)
#             print("aaaa",article_urls)
#             return article_urls
#         except Exception as e:
#             return []

#     def get_href(self, entry) -> str:
#         return entry
    
#     def get_title(self, response, entry=None) -> str:
#         return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
#     def get_body(self, response, entry=None) -> str:
#         return ""
    
#     def date_format(self) -> str:
#         return "%Y.%m.%d"
    
#     def get_date(self, response, entry=None) -> int:
#         return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
    
#     def get_authors(self, response, entry=None) -> List[str]:
#         return []
    
#     def get_document_urls(self, response, entry=None) -> list:
#         return self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")
    
#     def get_images(self, response, entry=None) -> List[str]:
#         return []
    
#     def get_page_flag(self) -> bool:
#         return True
    
#     def get_next_page(self, response) -> List[str]:
#         current_page= response.meta.get("current_page")
#         next_page = int(current_page)+1
#         return str(next_page)   
    
#     def go_to_next_page(self, response, start_url=None, current_page=None):
#         start_url = response.meta.get("start_url")
#         payload = response.meta.get("payload")
#         next_page= self.get_next_page(response)
#         payload["pageIndex"]=next_page
#         if next_page:
#             yield scrapy.FormRequest(
#                 url=response.meta.get("api_url"),
#                 method="POST",
#                 headers={"Content-Type": "application/x-www-form-urlencoded"},
#                 formdata=payload,
#                 callback=self.parse,
#                 meta={
#                     "start_url": start_url,
#                     "api_url": response.meta.get("api_url"),
#                     "payload": payload,
#                     "current_page": next_page
#                 },
#                 dont_filter=True
#             )
            
#     # def extract_articles_with_dates(self, response):
#     #     # Function to extract dates of respective articles from start URL
#     #     mapping = {}
#     #     for article in response.xpath("//div[@class='list']"):
#     #         url = article.xpath(".//a//@href").get()
#     #         title = article.xpath(".//h3//text()").get()
#     #         date = article.xpath(".//div[@class='time']//text()").get()
#     #         if url and title and date:
#     #             full_url = response.urljoin(url.strip())
#     #             clean_date=date.strip()
#     #             mapping[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
#     #         self.article_data_map.update(mapping)
#     #     return self.article_data_map
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
class WustecCninfoMinimal(OCSpider):
    name = "WustecCninfoMinimal"  # Changed to lowercase to match helper.py
    country = "CN"  # Added country code
    start_urls_names = {
        # "http://www.wustec.com/news.php": "Wustec News",
        "https://irm.cninfo.com.cn/ircs/company/companyDetail?stockcode=002463&orgId=9900013929": "CNINFO Detail",
    }
    HEADLESS_BROWSER_WAIT_TIME = 100 
    
    proxy_country = "cn"

    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
            'scraper.middlewares.GeoProxyMiddleware': 350,
		},
    }
    # custom_settings = {
    #     "DOWNLOAD_DELAY": 3,
    #     "USER_AGENT": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    #     "RETRY_TIMES": 2,
    #     "HTTPCACHE_ENABLED": True
    # }
    charset = "utf-8"  # Added charset
    @property
    def source_type(self) -> str:
        return "Corporate"
    @property
    def language(self) -> str:  # Added language property
        return "Chinese"
    @property
    def timezone(self):
        return "Asia/Shanghai"
    def get_page_flag(self) -> bool:  # Added get_page_flag method
        return True
    def get_articles(self, response) -> list:
        # # Improved article detection with precise XPath selectors
        # if "wustec.com" in response.url:
        #     # For Wustec website - these XPaths are verified on the actual site
        #     links = response.xpath('//table/tbody/tr/td/a/@href').getall()
        # else:
        #     # For CNINFO website
        #     links = response.xpath('//div[@class="f14 overhide"]//a//@href').getall()
        # return [response.urljoin(link) for link in (links or []) if link and not link.startswith('#')]
        return response.xpath('//table/tbody/tr/td/a/@href | //div[@class="content"]//a//@href').getall()
    
    def get_href(self, entry) -> str:  # Added get_href method
        return entry
    
    def get_title(self, response, entry=None) -> str:
        # Improved title extraction with site-specific selectors
        if "wustec.com" in response.url:
            title = response.xpath('//div[@class="news_title"]/text()|//div[@class="detail-header"]/h1/text()|//title/text()').get()
        else:
            title = response.xpath('//div[@class="queDetailContent"]').get()
        return title.strip() if title else ""
    def get_body(self, response, entry=None) -> str:
        # Improved body extraction with site-specific selectors
        if "wustec.com" in response.url:
            body_parts = response.xpath('//div[@class="con"]//text()').getall()
        else:
            body_parts = response.xpath('//div[@class="questContent"]//text').getall()
        return body_normalization(body_parts or [])
    def date_format(self) -> str:
        return "%Y-%m-%d"
    def get_date(self, response, entry=None) -> str:
        # Improved date extraction with site-specific selectors
        if "wustec.com" in response.url:
            date_str = response.xpath('//h4//text()').get()
        else:
            date_str = response.xpath('//div[@class="cont-foot"]').get()
        # Return current date if no date found
        return datetime.now().strftime(self.date_format())
    def get_next_page(self, response) -> list:
        # Improved pagination detection with site-specific selectors
        if "wustec.com" in response.url:
            next_page = response.xpath('//a[contains(text(), "下一页")]/@href').get()
        else:
            next_page = response.xpath('//a[contains(@class, "next-page")]/@href').get()
        return [response.urljoin(next_page)] if next_page else []
    def get_images(self, response, entry=None) -> list:
        # Improved image extraction with site-specific selectors
        if "wustec.com" in response.url:
            img_urls = response.xpath('//div[@class="news_content"]//img/@src').getall()
        else:
            img_urls = response.xpath('//div[@class="detail-content"]//img/@src').getall()
        if not img_urls:
            img_urls = response.xpath('//div[contains(@class, "content")]//img/@src').getall()
        return [urljoin(response.url, img) for img in (img_urls or [])]
    def get_document_urls(self, response, entry=None) -> list:
        # Improved document URL extraction
        doc_urls = response.xpath('//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [urljoin(response.url, doc) for doc in (doc_urls or [])]
    def get_authors(self, response, entry=None) -> list:  # Added get_authors method
        return ["Unknown"]
    def get_meta(self, response, entry=None) -> list:  # Added get_meta method
        return []
    def get_pdf(self, response, entry=None):  # Added get_pdf method
        return None