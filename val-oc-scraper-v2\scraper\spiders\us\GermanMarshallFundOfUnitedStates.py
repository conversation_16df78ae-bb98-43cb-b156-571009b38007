from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin 

class GermanMarshallFundOfUnitedStates(OCSpider):
    name = "GermanMarshallFundOfUnitedStates"
    
    country = "US"

    start_urls_names = {
        "https://www.gmfus.org/press" : "In the News"
    }
    
    charset = "utf-8"

    include_rules = [r'^https://www\.gmfus\.org/.*']

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        a = response.xpath('//div[@class="card--title"]//h3//a[contains(@href,"news")]//@href').getall()
        absolute_urls = [urljoin(response.url, url) for url in a]
        return absolute_urls

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="pageheader__title"]//text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="p__inner"]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath('//span[@class="caas-img-wrapper"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="pageheader__date"]//text()').get().strip()
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath('(//a[@title="Go to next page"])[2]/@href').get()