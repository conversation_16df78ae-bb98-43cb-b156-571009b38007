from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy

class ParliamentOfSouthKalimantan(OCSpider):
    name = "ParliamentOfSouthKalimantan"

    start_urls_names = {
        "https://dprdkalselprov.id/category/berita-dewan/?_gl=1*r8270y*_ga*NzY5NTYwODUxLjE3MDY2OTYyMTY.*_ga_E4FBBJ16R7*MTcwNjY5NjIxNS4xLjAuMTcwNjY5NjIxNS4wLjAuMA.." : "BERITA DEWAN",
        "https://dprdkalselprov.id/" : "Latest"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def parse_intermediate(self,response):
        start_url=response.meta.get("start_url")
        if "berita-dewan" in start_url:    
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={'start_url': start_url},
                dont_filter=True
            )
        else:
            current_year = response.meta.get('current_year', datetime.now().year) 
            current_month = response.meta.get('current_month', datetime.now().month) 
            url = f"https://dprdkalselprov.id/{current_year}/{current_month:02d}/"
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                dont_filter=True,
                meta={
                    'start_url': start_url,  
                    'current_year': current_year,
                    'current_month': current_month,
                    'url': url
                }
            )
            
    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }
    
    def get_articles(self, response) -> list:  
        return response.xpath('//h2[@class="cm-entry-title"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="cm-entry-title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="cm-content"]//p//text()').getall()) 
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        raw_date = response.xpath('//time//text()').get()
        for indo,eng in self.indonesian_to_english.items():
            raw_date = raw_date.replace(indo, eng)
        return raw_date.strip()
       
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        start_url = response.meta.get("start_url")
        if "berita-dewan" in start_url:
            return None 
        elif start_url == "https://dprdkalselprov.id/":
            current_year = response.meta.get("current_year")
            current_month = response.meta.get("current_month")
            if current_month == 1:
                next_year = current_year - 1
                next_month = 12
            else:
                next_year = current_year
                next_month = current_month - 1
            if response.status == 200:
                return next_year,next_month
            else:
                return None
        else :
            return None
            
    def go_to_next_page(self, response, start_url, current_page=None):
        next_year,next_month=self.get_next_page(response)
        if next_year and next_month:
            next_url = f"https://dprdkalselprov.id/{next_year}/{next_month:02d}/"
            yield scrapy.Request(
                url=next_url,
                callback=self.parse_intermediate,
                dont_filter=True,
                meta={
                    'start_url': start_url,  
                    'current_year': next_year,
                    'current_month': next_month,
                    'url': next_url
                }
            )
