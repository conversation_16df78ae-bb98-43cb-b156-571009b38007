from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import dateparser
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
from datetime import datetime

class UrbanPeopleMagazine(OCSpider):
    name = "UrbanPeopleMagazine"

    start_urls_names = {
        "https://nguoidothi.net.vn/do-thi": "News",
        }

    charset = "iso-8859-1"

    country = "Vietnam"

    custom_settings = {
         "DOWNLOADER_MIDDLEWARES": {
             'scraper.middlewares.HeadlessBrowserProxy': 100
         },
         #"DOWNLOAD_DELAY": 5,
         "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
     }
    
    HEADLESS_BROWSER_WAIT_TIME = 700

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       urls= response.xpath('//*[@id="dvZoneNews"]/div/div[2]/div[2]/a//@href').getall()
       print("URL IS +++++++++++++++++++++++++++++++",urls)
       return urls
     
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('/html/body/div[2]/div[1]/div[1]//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//p/a/img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('/html/body/div[2]/div[1]/div[1]/div[1]/div/span[1]//text()').get()
        print(date,"Date is ================================")
        clean_text = date.replace("\xa0", " ").strip()
        date_part = clean_text.split("|")[-1].strip()
        date_obj = dateparser.parse(date_part, languages=['vi'])
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) :
          return response.xpath('//a[normalize-space(text())=">"]/@href').get()