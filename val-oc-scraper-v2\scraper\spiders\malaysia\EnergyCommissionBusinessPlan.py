from scraper.OCSpider import OCSpider

class EnergyCommissionBusinessPlan(OCSpider):
    name = "EnergyCommissionBusinessPlan"

    start_urls_names = {
        'https://www.st.gov.my/ms/my/web/download/listing/137' : 'Business Plan'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.st.gov.my/ms/my/web/download/listing/137"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//tr[td and .//a[contains(@href, "download")]]')
        for row in rows:
           title = row.xpath('./td[2]/text()').get()
           date = row.xpath('./td[3]/text()').get()
           pdf_link = row.xpath('./td[4]/a/@href').get()
           if title and date and pdf_link:
                title = title.strip()
                date = date.strip()
                pdf_link = response.urljoin(pdf_link.strip())
                self.article_title_pdf_mapping[pdf_link] = (title, pdf_link, date)
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        original_url = response.meta.get('redirect_urls', [response.url])[0]
        return self.article_title_pdf_mapping.get(original_url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        original_url = response.meta.get('redirect_urls', [response.url])[0]
        date = self.article_title_pdf_mapping.get(original_url, ("", "", ""))[2]
        return date

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None)->list:
        original_url = response.meta.get('redirect_urls', [response.url])[0]
        return [self.article_title_pdf_mapping.get(original_url, ("", "", ""))[1]]

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None