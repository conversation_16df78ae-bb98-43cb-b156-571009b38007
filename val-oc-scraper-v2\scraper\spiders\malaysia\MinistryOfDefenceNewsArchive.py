from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from typing import Optional
import re
from urllib.parse import urljoin
from urllib.parse import unquote

class MinistryOfDefenceNewsArchive(OCSpider):
    name = "MinistryOfDefenceNewsArchive"

    start_urls_names = {
        "https://www.mod.gov.my/index.php/en/media3/news-archive": "News Archive"
    }

    start_urls_with_no_pagination_set = {
        "https://www.mod.gov.my/index.php/en/media3/news-archive"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        return list(self.extract_articles_with_dates(response).keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        url = unquote(response.url)
        for data in self.article_data_map.values():
            if url in data.get("pdf", []):
                return data.get("title", "")
        return ""

    def get_body(self, response) -> str: 
        return ""

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        url = unquote(response.url)
        for data in self.article_data_map.values():
            if url in data.get("pdf", []):
                return data.get("year", "")
        return ""

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        url = unquote(response.url)
        for data in self.article_data_map.values():
            if url in data.get("pdf", []):
                return data.get("pdf", [])
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None
   
    def extract_articles_with_dates(self, response):
        mapping = {}
        base_url = response.url
        cards =  response.xpath("//div[contains(@class, 'uk-card-body')]")
        for index, card in enumerate(cards, 1):
            title = card.xpath(".//h6//a/text()").get()
            pdf_relative_url = card.xpath(".//a[contains(@href, '.pdf')]/@href").get()
            year = None
            if title:
                match = re.search(r"\b(20\d{2})\b", title)
                if match:
                    year = match.group(1)
            if pdf_relative_url and title and year:
                pdf_url = urljoin(base_url, pdf_relative_url.strip())
                mapping[pdf_url] = {
                    "title": title.strip(),
                    "year": year,
                    "pdf": [pdf_url]
                }
        if not hasattr(self, "article_data_map"):
            self.article_data_map = {}
        self.article_data_map.update(mapping)
        return self.article_data_map