
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
from scraper.middlewares import HeadlessBrowserProxy
import re

class MalaysiaHighwayAuthorityPressReleases(OCSpider):
    name = "MalaysiaHighwayAuthorityPressReleases"

    start_urls_names = {
    f"https://www.llm.gov.my/announcement/press_release/{i}": "News" for i in range(0, 62, 10) # not pagination support
}

    start_urls_with_no_pagination_set={
        "https://www.llm.gov.my/announcement/press_release/"
    }

    charset = "iso-8859-1"


    custom_settings = {
         "DOWNLOADER_MIDDLEWARES": {
             'scraper.middlewares.HeadlessBrowserProxy': 100
         },
         #"DOWNLOAD_DELAY": 5,
         "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
     }

    HEADLESS_BROWSER_WAIT_TIME = 700
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
     articles= []
     for article in response.xpath('//table//tr'):
            link = article.xpath(".//a//@href").get()
            title=article.xpath(".//a//text()").get()
            date= article.xpath(".//td[3]//text()").get()
            if link and date and title:
                articles.append(link)
                self.article_data_map[link]={
                    "date":date,"title":title,"link":link
                }
     return list(set(articles))

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
     return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response) -> str:
       date= self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
       clean_date_str = date.strip() 
       date_obj = datetime.strptime(clean_date_str, "%d/%m/%y")  # Parse the cleaned string
       formatted_date = date_obj.strftime("%d-%m-%Y")
       return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 