from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class TexasEconomicDevelopmentCorporation(OCSpider):
    name = 'TexasEconomicDevelopmentCorporation'
    
    country = "US"

    start_urls_names = {
        'https://businessintexas.com/news/': 'News'
    }
     
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "post-content"]/div[@class= "content overview-content bposts"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        title = response.xpath('//h1[@class= "overview-content-title"]/text()').get()
        return title.strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "content overview-content"]//div//p/text()').getall())    
            
    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class= "content overview-content"]//div//img/@src').getall()

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//span[@class= "overview-label"]/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="nav-links"]/a[contains(@class, "next")]/@href').get()
        return next_page