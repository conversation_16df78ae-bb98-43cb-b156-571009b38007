from scraper.OCSpider import OCSpider
import dateparser
import requests

class HigherEducationDepartmentPublications(OCSpider):
    name = "HigherEducationDepartmentPublications"

    start_urls_names = {
        "https://jpt.mohe.gov.my/portal/index.php/ms/penerbitan": "News"
    }
    
    start_urls_with_no_pagination_set = {}

    charset = "utf-8"
   
    country = "Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def get_articles(self, response) -> list:  
        curr_url = response.xpath('//*[@id="adminForm"]/table/tbody/tr/td[1]/a//@href').getall()
        for i in range(1,len(curr_url)+1):
            curr_date =response.xpath(f'//*[@id="adminForm"]/table/tbody/tr[{i}]/td[2]//text()').get()
            curr_url1 = response.xpath(f'//*[@id="adminForm"]/table/tbody/tr[{i}]/td[1]/a//@href').get()
            self.article_data_map[curr_url1] = {"curr_date":curr_date}
        return curr_url
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='uk-margin-medium-top']//img//@src").getall()
         
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        curr_date1=  self.article_data_map.get(response.request.meta.get('entry'), {}).get("curr_date", "")
        parsed_date = dateparser.parse(curr_date1, languages=['en', 'ms']) 
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        pdf_urls = response.xpath('//div[@class="uk-margin-medium-top"]//a//@href').getall()
        valid_pdfs = []
        for url in pdf_urls:
            if not url.startswith("http"):
                url = "https://jpt.mohe.gov.my" + url
            # Collect URLs without checking SSL here
            valid_pdfs.append(url)
        return valid_pdfs  


    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = response.xpath("(//a[contains(@class, 'next')])[last()]/@href").get()
        if next_page:
            return next_page
        return None 