from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

class BusinessandTradeReview(OCSpider):
    name = "BusinessandTradeReview"

    start_urls_names = {
        'https://doanhnghiepvathuongmai.vn/danh-muc/thoi-su-57.phtml': 'Bussiness and Trade'
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="post-cat-small" or @class="post-cat-big" or @class="category-item"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//header[@class="header-single"]//h2/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//article[@class="entry-content"]//p//span/text() | //div[@class="header-excerpt"]//span/text()').getall())

    def get_date(self, response) -> str:
        date = response.xpath('(//div[@class="col-12 col-md-6 col-xl-6 col-lg-6"]//text())[1]').get()
        match = re.search(r"\b\d{2}/\d{2}/\d{4}\b", date)
        return match.group(0) if match else ""

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//article[@class="entry-content"]//span//img/@src').getall()      
    
    def get_document_urls(self, response, entry=None) -> list[str]:
        return []

    def get_authors(self, response) -> str :
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        url = response.url
        parsed = urlparse(url)
        qs = parse_qs(parsed.query)
        current = int(qs.get('page', ['1'])[0])
        pages = response.xpath('//ul[contains(@class,"pagination")]//a/text()').re(r'\d+')
        if pages and current >= int(max(pages)):
            return None
        qs['page'] = [str(current + 1)]
        return urlunparse(parsed._replace(query=urlencode(qs, doseq=True)))