from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from typing import List
import scrapy

class NorthCarolinaDepartmentOfTransportation(OCSpider):
    name = 'NorthCarolinaDepartmentOfTransportation'

    country = "US"

    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,
	}
    
    start_urls_names = {
        'https://www.ncdot.gov/news/press-releases/Pages/default.aspx?startdate=201801010000&enddate=202503120000': "News"
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        all_articles = list(set(response.xpath("//h3//a//@href").getall()))
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx, 
                    'articles': all_articles, 
                    'start_url': start_url
                },
                dont_filter=True
            )

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)  # More than 100 articles are there on Start URL
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='ms-rtestate-field']//p//text() | //div[@class='ms-rtestate-field']//ul//li//text() | //div[@class='ms-rtestate-field']//div//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='ms-rtestate-field']//p//img//@src").getall()
    
    def date_format(self) -> str:
        return '%m/%d/%Y'

    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='sidebar__section']//h6//following::text()[1]").get(default="").strip()
        return date
    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return None