from datetime import datetime
from scraper.OCSpider import OCSpider
import scrapy
from typing import List
from scraper.utils.helper import body_normalization
from typing import Optional

class yuyuantm(OCSpider):
    name = "yuyuantm"

    start_urls_names = {
        "https://www.yuyuantm.com.cn/news/latest.html": "豫园股份", 
        "https://www.yuyuantm.com.cn/IR/regularly.html": "豫园股份", 
        # "https://www.yuyuantm.com.cn/IR/profile.html": "豫园股份",  # No articles on this start URL
        "https://www.yuyuantm.com.cn/IR/temporary.html": "豫园股份" 
    }

    charset = "iso-8859-1"

    article_data_map = {}  # Mapping child article with title, date and PDF from start URL
  
    api_start_urls = {
        "https://www.yuyuantm.com.cn/news/latest.html": {
            "url": "https://www.yuyuantm.com.cn/data/pages2.aspx?createId=0&typeName=news&type=1&dataType=GET",
            "payload": {
                "createId": "0",
                "typeName": "news",
                "type": "1",
                "dataType": "GET",
                "pageid": "1", 
                "pagecount": "6",  
                "isCount": "true"
                }
            },

        "https://www.yuyuantm.com.cn/IR/regularly.html":{
            "url":"https://www.yuyuantm.com.cn/data/pages2.aspx?createId=0&typeName=report&typeid=2&dataType=GET",
            "payload":{
                "createId": "0",
                "typeName": "report",
                "typeid": "2",
                "dataType": "GET",
                "pageid": "1", 
                "pagecount": "6",  
                "isCount": "true"
               }
            },
        "https://www.yuyuantm.com.cn/IR/temporary.html":{
            "url":"https://www.yuyuantm.com.cn/data/pages2.aspx?createId=0&typeName=report&typeid=1&dataType=GET",
            "payload":{
                "createId": "0",
                "typeName": "report",
                "typeid": "1",
                "dataType": "GET",
                "pageid": "1", 
                "pagecount": "6",  
                "isCount": "true"
               }
            },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["pageIndex"] = payload.get("pageid")
        yield scrapy.FormRequest(
            url = api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["pageid"],
            },
        )    

    @property
    def source_type(self) -> str:
        return 'private_enterprise'

    @property
    def timezone(self):
        return "Asia/Shanghai"
           
    def get_articles(self, response) -> list:
        start_url = response.meta.get("start_url")
        if start_url == "https://www.yuyuantm.com.cn/news/latest.html":
            try:
                data = response.json()
                article_urls = []
                for item in data.get("data", []):
                    article_id = item.get("id")
                    title = item.get("title")
                    date = item.get("addtime")
                    if article_id and title and date:
                        full_url = f"https://www.yuyuantm.com.cn/news/detail-{article_id}.html"
                        self.article_data_map[full_url] = {
                            "title": title,
                            "date": date,
                            "pdf": [full_url]
                        }
                        article_urls.append(full_url)
                return article_urls
            except Exception as e:
                    return []
        else:
            try:
                data = response.json()
                article_urls = []
                for item in data.get("data", []):
                    title = item.get("name")
                    date = item.get("addtime")
                    pdf_url = item.get("PDF")  
                    if title and date and pdf_url:
                        full_pdf_url = f"https://www.yuyuantm.com.cn{pdf_url}" 
                        self.article_data_map[full_pdf_url] = {
                            "title": title,
                            "date": date,
                            "pdf": [full_pdf_url]
                        }
                        article_urls.append(full_pdf_url)
                return article_urls
            except Exception as e:
                return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")
       
    def get_body(self, response) -> str:
        if not response.headers.get('Content-Type', b'').startswith(b'text/html'):
            return " " 
        try:
            body = response.xpath('//p//text()').getall()
            body = [t.strip() for t in body if t.strip()]
            return body_normalization(body) if body else " "
        except Exception as e:
            return " "

    def get_images(self, response, entry=None) -> List[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None):
        entry = entry or response.meta.get('entry')
        if entry and entry.lower().endswith('.pdf'):
            return [entry]
        return self.article_data_map.get(entry, {}).get("pdf", [])

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        addtime_str = self.article_data_map[response.request.meta.get('entry')].get("date")
        if addtime_str:
            try:
                addtime = datetime.strptime(addtime_str, "%Y-%m-%dT%H:%M:%S")
                return addtime.strftime(self.date_format())
            except ValueError:
                return ""
        else:
            return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        total_pages = int(response.json().get('PageCount', 0))
        return str(int(current_page) + 1) if int(current_page) < total_pages else None

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['pageid'] = next_page
            yield scrapy.http.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                },
                callback=self.parse_intermediate, 
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                        'payload': payload,
                    }
            )
        else:
            yield None