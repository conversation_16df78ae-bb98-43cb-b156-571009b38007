import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
import json
import logging
from typing import List, Union

class AircraftOwnersAndPilotsAssociationOfChina(OCSpider):
    name = 'AircraftOwnersAndPilotsAssociationOfChina'
    
    custom_settings = {
        "DOWNLOAD_DELAY": 4,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    start_urls_names = {
        'https://www.caacpilots.org.cn/chalpa/articleList?id=13&name=%E6%96%B0%E9%97%BB%E4%B8%AD%E5%BF%83':'党建文化',
        'https://www.caacpilots.org.cn/chalpa/articleList?id=17&name=%E5%85%9A%E5%BB%BA%E6%96%87%E5%8C%96':'新闻中心'
    }
    
    api_start_url = {
        'https://www.caacpilots.org.cn/chalpa/articleList?id=13&name=%E6%96%B0%E9%97%BB%E4%B8%AD%E5%BF%83':{
           'url' : 'https://www.caacpilots.org.cn/server/api/portal/content/website/public/getContentList',
           'payload': {
               'columnOneId' : '13',
                'currentPage': '1',
                'pageSize' : '10'
            }
        },
        'https://www.caacpilots.org.cn/chalpa/articleList?id=17&name=%E5%85%9A%E5%BB%BA%E6%96%87%E5%8C%96':{
           'url' : 'https://www.caacpilots.org.cn/server/api/portal/content/website/public/getContentList',
           'payload': {
               'columnOneId' : '17',
                'currentPage': '1',
                'pageSize' : '10'
            }
        }
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            current_page = response.meta.get("current_page", 1)
            api_data["payload"]["currentPage"] = str(current_page)
            payload = api_data["payload"]
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers=headers,
                dont_filter=True,
                formdata=payload,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "current_page": current_page
                },
            )
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self): 
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            data = response.json() 
            article_urls = []
            hbp = HeadlessBrowserProxy() 
            article_list = data.get("content", {}).get("list", [])  
            ids = [article["id"] for article in article_list]  
            for article_id in ids:
                url = f"https://www.caacpilots.org.cn/chalpa/details?id={article_id}"
                proxy_url = hbp.get_proxy(url, timeout=30000)  
                if proxy_url:  
                    article_urls.append(proxy_url)
            return article_urls 
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON: {e}")
            return []
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self,response) -> str:
        return response.xpath("//p[@class='titb']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='contenttext']//p//text()").getall())
    
    def get_images(self, response) -> List[str]:
        return response.xpath("//div[@class='imgdiv']//img/@src").extract() 
    
    def get_authors(self, response):
        return []
        
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return response.xpath("//p[@class='fbtit']//span//text()").re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page) -> Union[None, str]:
        data = response.json() 
        total_pages = data["content"]["totalPage"]
        if current_page < total_pages:
            return current_page + 1
        else:
            return None

    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            logging.info("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None 