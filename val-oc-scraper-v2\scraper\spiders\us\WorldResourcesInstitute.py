from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class WorldResourcesInstitute(OCSpider):
    name = "WorldResourcesInstitute"
    
    country = "US"

    start_urls_names = {
        "https://www.wri.org/resources/type/research-65?query=&sort_by=created" : "Resources",
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//h3[@class="h3"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="h1"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="layout__region layout__region--main_content main-content margin-bottom-lg"]//p/text()').getall() or response.xpath('//div[@class="layout__region layout__region--main_content main-content margin-bottom-lg"]//li/text()'.getall())) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//span[@class="article-date category-wrapper"]/text()').get().strip()
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath('//a[@title="Go to next page"]/@href').get()