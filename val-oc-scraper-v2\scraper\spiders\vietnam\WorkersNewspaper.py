from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class WorkersNewspaper(OCSpider):
    name = "WorkersNewspaper"

    start_urls_names = {
        f"https://nld.com.vn/timelinelist/1961002/{i}.htm": "News"  for i in range(1,1400,1)   
        # Here are more than 1399 pages if you want than go for it but i take only 1400 pages it go upto 
        #https://nld.com.vn/thoi-su.htm  main url in the network tab i get this URL 
        }
    
    start_urls_with_no_pagination_set = {
        "https://nld.com.vn/timelinelist/1961002/1.htm"
       
    }

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('/html/body/div/div/h3/a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[2]/div[2]/div[1]/div/div/p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//div/div/div/div/div/img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//div[@data-role="publishdate"]/text()').get().strip()
        date_obj = dateparser.parse(date_str, languages=['vi'])
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None