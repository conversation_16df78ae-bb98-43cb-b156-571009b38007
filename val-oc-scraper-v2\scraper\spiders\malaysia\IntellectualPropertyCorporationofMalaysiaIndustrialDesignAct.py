from scraper.OCSpider import OCSpider
import re

class IntellectualPropertyCorporationOfMalaysiaIndustrialDesignAct(OCSpider):
    name = "IntellectualPropertyCorporationOfMalaysiaIndustrialDesignAct"

    start_urls_names = {
        "https://www.myipo.gov.my/industrial-design-act/": "INDUSTRIAL DESIGNS ACT 1996"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.myipo.gov.my/industrial-design-act/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//table//td'):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//a/text()").get()
            if url and title:
                article_date = None
                match = re.search(r'/(\d{4})/(\d{2})/', url)
                if match:
                    year, month = match.groups()
                    article_date = f"{year}-{month}" if year and month else None
            if url and title and article_date:
                self.article_data_map[url] = {"title": title.strip(), "date": article_date, "pdf": url}
                articles.append(url)
        return list(set(articles))  
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m"
    
    def get_date(self, response): 
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")    
        return date

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None