from scraper.OCSpider import OCSpider

class CybersecurityMalaysiaPressReleases(OCSpider):
    name = "CybersecurityMalaysiaPressReleases"

    start_urls_names = {
        "https://www.cybersecurity.my/portal-main/document-list/media-releases?frontendpage=media-releases&params=&menuslug=&page=1": "Media Releases"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles=[]
        mapping={}
        for article in response.xpath("//div[@class='col-lg-9']//tbody//tr"):
                url = article.xpath(".//a//@href").get()
                title = article.xpath(".//div[@style='color:#ccc;']/following-sibling::text()[1]").get()
                date = article.xpath(".//td[2]//div//text()").get()
                if url and title and date:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    mapping[full_url]={"title": title, "date": date, "full_url": url}
                articles.append(url)
        self.article_data_map.update(mapping)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return response.xpath("//ul//li[@class='next']//@href").get()