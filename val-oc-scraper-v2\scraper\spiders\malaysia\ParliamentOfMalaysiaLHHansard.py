from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re
import dateparser

class ParliamentOfMalaysiaLHHansard(OCSpider):
    name = "ParliamentOfMalaysiaLHHansard"
    
    start_urls_names = {
        "https://www.parlimen.gov.my/hansard-dewan-rakyat.html?uweb=dr&arkib=yes": "News"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}  # Mapping date and title to child articles from start URL

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='boxAktivitiContentText']"):
            url = article.xpath(".//a//@href").get()
            date = article.xpath(".//a//text()").get()
            match = re.search(r"loadResult\('([^']+)'", url)
            full_url = match.group(1)
            if full_url and date:
                title = body_normalization(response.xpath("//div[@class='boxTopTabA']//strong//text()").getall())
                self.article_data_map[full_url] = {"date":date,"title" : title.strip()}
                articles.append(full_url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response): 
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(date, languages=['ms']) 
        return parsed_date.strftime("%Y-%m-%d")

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to crawl
        return None