from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import dateparser
import scrapy
from urllib.parse import urlencode

class PublicSecurityNewsVietnam(OCSpider):
    name = "PublicSecurityNewsVietnam"

    country = "Malaysia"

    start_urls_names = {
        "https://cand.com.vn/thoi-su/": "Press Releases",
    }
    
    api_start_urls = {
     "https://cand.com.vn/thoi-su/": {
         "url": "https://cand.com.vn/article/Paging",
         "payload": {
             "categoryId":"1415",
             "pageIndex":"1",
             "pageSize":"20",
             "fromDate":"",
             "toDate":""
         }
     }
 }

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    custom_settings = {
        "DEFAULT_REQUEST_HEADERS": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                          "AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/110.0.0.0 Safari/537.36",
            "X-Requested-With": "XMLHttpRequest",
            "Accept": "text/html, */*; q=0.01",
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url") or response.url
        api_data = self.api_start_urls.get(start_url)
        if "/article/Paging" in response.url:
            yield from OCSpider.parse(self, response)
            if not self.get_page_flag():
                return
            payload = response.meta.get("payload") or (api_data and api_data["payload"].copy()) or {}
            current_page = int(response.meta.get("current_page") or payload.get("pageIndex", 1))
            next_page = self.get_next_page(response, current_page)
            if not next_page:
                return
            payload["pageIndex"] = str(next_page)
            api_url = (response.meta.get("api_url") or api_data["url"]).split("?")[0]
            full_api_url = f"{api_url}?{urlencode(payload)}"
            yield scrapy.Request(
                url=full_api_url,
                method="GET",
                headers={
                    **self.custom_settings["DEFAULT_REQUEST_HEADERS"],
                    "Referer": start_url,
                },
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page,
                },
            )
            return
        if api_data:
            payload = api_data["payload"].copy()
            api_url = api_data["url"].split("?")[0]
            full_api_url = f"{api_url}?{urlencode(payload)}"
            yield scrapy.Request(
                url=full_api_url,
                method="GET",
                headers={
                    **self.custom_settings["DEFAULT_REQUEST_HEADERS"],
                    "Referer": start_url,
                },
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": int(payload.get("pageIndex", "1")),
                },
            )
            return

    def get_articles(self, response) -> list:
        hrefs = response.xpath('//div[@class="boxlist-list"]//div[@class="box-title"]//a/@href').getall()
        return [response.urljoin(h) for h in hrefs]

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detail-content-body"]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        full_date = response.xpath('//div[@class="box-date"]//text()').get()
        date_obj = dateparser.parse(full_date, languages=['vi'])
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: int) -> Optional[int]:
         articles = self.get_articles(response)
         if not articles:
             return None
         return current_page + 1

    def go_to_next_page(self, response, start_url, current_page):
     api_data = self.api_start_urls.get(start_url)
     if not api_data:
        return
     base_api_url = api_data["url"].split('?')[0]
     payload = response.meta.get("payload") or api_data.get("payload", {}).copy()
     next_page = self.get_next_page(response, current_page)
     if not next_page:
        return
     payload["pageIndex"] = str(next_page)
     query_string = urlencode(payload)
     full_api_url = f"{base_api_url}?{query_string}"
     yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers={
                **self.custom_settings["DEFAULT_REQUEST_HEADERS"],
                "Referer": start_url
            },
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": base_api_url,
                "payload": payload,
                "current_page": next_page
            },
        )
