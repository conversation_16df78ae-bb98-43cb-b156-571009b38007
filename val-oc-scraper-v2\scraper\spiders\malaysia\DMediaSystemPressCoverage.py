from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from typing import Optional
from urllib.parse import urljoin
from dateutil import parser

class DMediaSystemPressCoverage(OCSpider):
    name = "DMediaSystemPressCoverage"
    
    start_urls_names = {
        "https://dmedia.penerangan.gov.my/sm": "SIARAN MEDIA",
    }

    start_urls_with_no_pagination_set = {
        "https://dmedia.penerangan.gov.my/sm"
    }

    article_data_map = {}

    country = "Malaysia"

    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_date_map = {}
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return list(self.article_data_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        url = response.url
        article_info = self.article_data_map.get(url)
        if article_info and 'title' in article_info:
            return article_info['title'].strip()
        return "No title found"

    def get_body(self, response) -> str:
        if response.url.lower().endswith(".pdf") or ".pdf" in response.url.lower():
            return ""

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d-%m-%Y"
    
    def get_date(self, response) -> Optional[str]:
        article_url = response.url
        article_info = self.article_data_map.get(article_url)   
        if not article_info:
            return None   
        raw_date = article_info.get("date")
        if not raw_date:
            return None
        try:
            date_obj = parser.parse(raw_date.strip(), dayfirst=True)
            return date_obj.strftime(self.date_format())
        except (ValueError, TypeError):
            return None

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        article_info = self.article_data_map.get(response.url)
        if article_info and "pdf" in article_info:
            return article_info["pdf"]
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None

    def extract_articles_with_dates(self, response):
        mapping = {}
        base_url = response.url
        rows = response.xpath('//table[@id="dataTableHover"]/tbody/tr')
        for row in rows:
            date = row.xpath('./td[2]/text()').get()
            anchor = row.xpath('./td[3]/a')
            title = anchor.xpath('normalize-space(text())').get()
            pdf_relative_url = anchor.xpath('./@href').get()
            if pdf_relative_url and title and date:
                pdf_url = urljoin(base_url, pdf_relative_url.strip())
                mapping[pdf_url] = {
                    "title": title.strip(),
                    "date": date.strip(),
                    "pdf": [pdf_url]
                }
        self.article_data_map.update(mapping)
        return self.article_data_map