from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import urllib.parse
import scrapy
from datetime import datetime

class ManhattanInstitute(OCSpider):
    name = "ManhattanInstitute"

    country = "US"

    handle_httpstatus_list = [301, 302]

    start_urls_names = {
        "https://manhattan.institute/search?top=true&limit=10&page-number=1&search=&types%5B%5D=article&article_types%5B%5D=24868029&dates=&sort=date": "Press Releases",
    }

    api_start_urls = {
        "https://manhattan.institute/search?top=true&limit=10&page-number=1&search=&types%5B%5D=article&article_types%5B%5D=24868029&dates=&sort=date": {
            "url": "https://search.manhattan.institute/api/mi/search/search.json",
            "payload": {
                "top": "true",
                "limit": 10,
                "page": 1,
                "search": "",
                "types[]": "article",
                "article_types[]": "24868029",
                "dates": "",
                "exclude_article_types[]": "press-release",
            }
        }
    }

    article_date_map = {}  # Mapping child articles with date from start URL

    article_map = {}  # Mapping child article with title from start URL

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    custom_settings = {
        "REDIRECT_ENABLED": True
    }

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/New_York"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        if response.url.startswith("https://search.manhattan.institute/api"):
            try:
                data = response.json()
                items = data.get("items", [])
                self.article_date_map.clear()
                self.article_map.clear()
                for item in items:
                    meta = item.get("meta", {})
                    permalink = meta.get("permalink", "")
                    pub_date = meta.get("date_formatted", "")
                    if not pub_date:
                        raw_date = meta.get("date_raw") or meta.get("date_unix")
                        if raw_date:
                            try:
                                pub_date = datetime.utcfromtimestamp(
                                    int(raw_date)
                                ).strftime("%b %d %Y")
                            except Exception:
                                pub_date = None
                    if permalink:
                        if permalink.startswith("/cj/article/"):
                            full_url = f"https://www.city-journal.org/article/{permalink.split('/')[-1]}"
                        else:
                            full_url = f"https://manhattan-institute.org{permalink}"
                        if full_url and pub_date:
                            self.article_date_map[full_url] = pub_date.strip()
                        self.article_map[full_url] = item
            except Exception:
                return
            yield from super().parse(response)
        else:
            api_data = self.api_start_urls.get(start_url)
            if not api_data:
                return
            payload = response.meta.get("payload", api_data["payload"].copy())
            api_url = api_data["url"]
            full_api_url = f"{api_url}?{urllib.parse.urlencode(payload, doseq=True)}"
            yield scrapy.Request(
                url=full_api_url,
                method="GET",
                headers=self.headers,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": payload["page"]
                }
            )

    def get_articles(self, response):
        return list(self.article_map.values())

    def get_href(self, entry) -> str:
        permalink = entry.get("meta", {}).get("permalink", "")
        if not permalink:
            return ""
        if permalink.startswith("/cj/article/"):
            slug = permalink.split("/cj/article/")[-1]
            return f"https://www.city-journal.org/article/{slug}"
        else:
            return f"https://manhattan-institute.org{permalink}"

    def get_title(self, response) -> str:
        title = response.xpath('//h1/text()').get()
        return title.strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="cg-c-article__content"]//text() | //td[@class="column column-1"]//span//text()| //td[@class="column column-1"]//p//text() | //table[@class="nl-container"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response):
        raw_date = self.article_date_map.get(response.url, "").strip()
        if raw_date:
            try:
                date_obj = datetime.strptime(raw_date, "%b %d %Y")
                return date_obj.strftime(self.date_format())
            except ValueError:
                return None
        else:
            if "manhattan.institute" in response.url:
                html_date = response.xpath("//div[contains(@class, 'article-date')]/text()").get()
                if html_date:
                    cleaned_date = html_date.strip()
                    try:
                        date_obj = datetime.strptime(cleaned_date, "%B %d, %Y")
                        return date_obj.strftime(self.date_format())
                    except ValueError:
                        return None
                else:
                    return None          
            elif "city-journal.org" in response.url:
                try:
                    slug = response.url.split("/")[-1]
                    date_parts = slug.rsplit("-", 3)[-3:]  # ['mar', '5', '2025']
                    raw_url_date = " ".join(date_parts).title()  # → 'Mar 5 2025'
                    date_obj = datetime.strptime(raw_url_date, "%b %d %Y")
                    return date_obj.strftime(self.date_format())
                except Exception:
                    return None
            return None

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        next_page = int(current_page) + 1
        return next_page

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        api_url = api_data["url"]
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)
        payload["page"] = next_page
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload, doseq=True)}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            }
        )

    def extract_articles_with_dates(self, response):
        for article in response.xpath("//div[contains(@class, 'm_article-card')]"):
            url = article.xpath(".//a[contains(@class, 'title')]/@href").get()
            date = article.xpath(".//span[contains(@class, 'date')]/text()").get()
            if url and date:
                full_url = response.urljoin(url.strip())
                self.article_date_map[full_url] = date.strip()