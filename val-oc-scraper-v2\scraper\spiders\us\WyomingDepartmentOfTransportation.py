import scrapy
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime

class WyomingDepartmentOfTransportation(OCSpider):
    name = 'WyomingDepartmentOfTransportation'
    
    country = "US"
    
    start_urls_names = {
        'https://www.dot.state.wy.us/home/<USER>/news_releases.html': 'News Releases',
        'https://www.dot.state.wy.us/home/<USER>/news_releases/2024-releases.html': 'News Releases'
    }
    
    def parse_intermediate(self, response):
        start_url=response.meta.get("start_url")
        if start_url == 'https://www.dot.state.wy.us/home/<USER>/news_releases.html':
            yield scrapy.Request(
                url = start_url,
                callback = self.parse,
                dont_filter = True, 
                meta = {
                    "start_url" : start_url,
                }
            )
        else:
            current_year=response.meta.get("current_year",(datetime.now().year - 1))
            url = f"https://www.dot.state.wy.us/home/<USER>/news_releases/{current_year}-releases.html"
            yield scrapy.Request(
                url = url,
                callback = self.parse,
                dont_filter = True, 
                meta = {
                    "start_url" : start_url,
                    "current_year": current_year
                }
            )
            
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Mountain"
    
    def get_articles(self, response) -> list:
        return response.xpath('//h2[@class= "news-heading"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1/text()').get() 
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "news-text-big"]//p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y" 
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="news-date"]/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
     
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath("//a[contains(@class, 'nextLink')][1]/@href").get()
        return response.urljoin(next_page) if next_page else None
                
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url", response.url)  # Keep original start URL
        current_year = response.meta.get("current_year", datetime.now().year)  # Fetch current year
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=next_page,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "current_year": current_year
                }
            )
        else:
            previous_year = current_year - 1
            if previous_year >= 2019:
                url = f"https://www.dot.state.wy.us/home/<USER>/news_releases/{previous_year}-releases.html"
                yield scrapy.Request(
                    url=url,
                    callback=self.parse_intermediate,
                    meta={
                        "start_url": start_url,
                        "current_year": previous_year
                    }
                )