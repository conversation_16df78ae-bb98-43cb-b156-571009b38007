from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
from scraper.middlewares import HeadlessBrowserProxy

class MinistryOfCommunicationsAnnualReports(OCSpider):
    name = "MinistryOfCommunicationsAnnualReports"
    
    start_urls_names = {
        "https://www.komunikasi.gov.my/media-menu/penerbitan/laporan-tahunan": "News"
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request 

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}  # Mapping date and title to child articles from start URL

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//table[2]//tbody//tr"):
            url = article.xpath(".//td[4]//a[1]//@href").get()
            title = article.xpath(".//td[3]//a//span//text()").get()
            date = title.split(" ")[-1]
            if url and title and date:
                self.article_data_map[url] = {"date":date, "title" : title}
                articles.append(url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to crawl
        return None