from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaMunicipalEngineeringAssociation(OCSpider):
    name = "ChinaMunicipalEngineeringAssociation"

    start_urls_names = {
        "https://www.zgsz.org.cn/h-col-122.html": "行业动态",
        "https://www.zgsz.org.cn/h-col-114.html":"协会动态",
        "https://www.zgsz.org.cn/h-col-143.html":"党建专栏",
        "https://www.zgsz.org.cn/h-about.html":"通知通告"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//*[@id="containerForms"]//div[@class="news_title"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div/h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="jz_fix_ue_img"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//*[@id="module12"]//img/@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d %H:%M"
    
    def get_date(self, response) -> str:
        return response.xpath('//div/span[@class="newsInfo"]//text()').re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page=response.xpath('//span[@class="pageNext"]/a//@href').get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:
                    return None
                else:
                    self.logger.info("No next page found.")
                    return next_page_url
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None