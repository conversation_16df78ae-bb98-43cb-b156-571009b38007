from scraper.OCSpider import OCSpider
from datetime import datetime
from scraper.utils.helper import body_normalization

class MalaccaIslamicReligiousCouncilNews(OCSpider):
    name = "MalaccaIslamicReligiousCouncilNews"

    start_urls_names = {
        'https://www.maim.gov.my/index.php/ms/lagi-berita' : 'News'
    }

    start_urls_with_no_pagination_set ={}

    charset = "iso-8859-1"

    MALAY_TO_ENGLISH_MONTHS = {
        "Januari": "January",
        "Februari": "February",
        "Mac": "March",
        "April": "April",
        "Mei": "May",
        "Jun": "June",
        "Julai": "July",
        "Ogos": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Disember": "December"
    }

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//tbody/tr')
        for row in rows:
           title = row.xpath('.//td[@class="list-title"]//a/text()').get()
           date = row.xpath('.//td[@class="list-date small"]//text()').get()
           pdf_link = row.xpath('.//td[@class="list-title"]//a/@href').get()
           if title and date and pdf_link:
                title = title.strip()
                date = date.strip()
                pdf_link = response.urljoin(pdf_link.strip())
                self.article_title_pdf_mapping[pdf_link] = (title, pdf_link, date)
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article-details "]//p/text()').getall())
    
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_date(self, response) -> str:
        date_str = self.article_title_pdf_mapping.get(response.url, ("", "", ""))[2]
        for malay, english in self.MALAY_TO_ENGLISH_MONTHS.items():
            if malay in date_str:
                date_str = date_str.replace(malay, english)
                break
        try:
            return datetime.strptime(date_str, "%d %B %Y").strftime("%d %B %Y")
        except Exception as e:
            self.logger.error(f"Date conversion failed for '{date_str}': {e}")
            return ""

    def get_images(self, response) -> list[str]:
        return [response.xpath('//div[@class="article-details "]//p/img/@src').getall()]

    def get_document_urls(self, response, entry=None)->list:
        return [self.article_title_pdf_mapping.get(response.url, ("", "", ""))[1]]

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        current = int(response.xpath('//li[@class="active page-item"]/span/text()').get().strip())
        next_href = response.xpath(f'//li[@class="page-item"]/a[normalize-space(text())="{current + 1}"]/@href').get()
        return response.urljoin(next_href) if next_href else None