from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
from scraper.utils.helper import body_normalization
from urllib.parse import unquote
import scrapy

class DepartmentOfIrrigationAndDrainageAnnouncementsArchive(OCSpider):
    name = "DepartmentOfIrrigationAndDrainageAnnouncementsArchive"

    start_urls_names = {
        "https://www.water.gov.my/index.php/pages/archives/28": "News"
        # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.water.gov.my/index.php/pages/archives/28"
    }

    charset = "utf-8"
    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(
            hbp.get_proxy(response.url, timeout=10000),
            callback=self.parse
        )
        request.meta['start_url'] = response.request.meta.get('start_url', response.url)
        yield request

    def get_articles(self, response) -> list: 
        mapping = {}
        articles = []
        rows = response.xpath('//div[@class="group_tag_search_result_item"]')
        for row in rows:
            title = row.xpath('.//h3/text()').get()
            date = row.xpath('.//span[@class="date"]//text()').get()
            article_url = row.xpath('./a/@href').get()
            pdf_url = row.xpath('.//a[contains(@href, ".pdf")]/@href').get()
            # Clean up
            if title:
                title = title.strip()
            if date:
                date = date.replace('-', '').strip()
            if article_url:
                normalized_link = unquote(article_url).rstrip('/')
                articles.append(normalized_link)
                mapping_entry = {
                    'title': title if title else "",
                    'pdf': response.urljoin(pdf_url) if pdf_url else None,
                    'date': date if date else ""
                }
                mapping[normalized_link] = mapping_entry
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="page_segment_0"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get('date')

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        normalized_url = unquote(response.url).rstrip('/')
        pdf_url = self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
        if pdf_url:
            return [pdf_url]
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None