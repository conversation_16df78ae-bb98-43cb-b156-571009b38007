from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import re

class VietnamTimes(OCSpider):
    name = "VietnamTimes"

    start_urls_names = {
        'https://thoidai.com.vn/viet-nam-hom-nay': 'Veitnam Times'
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'unknown'
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="_BX_LISTING fw clearfix"]//div[@class="article"]//h3[@class="article-title f0"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="article-detail-title f0"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath( '//div[@class="article-detail-content fw"]//p//text()').getall())

    def get_date(self, response) -> str:
        date = response.xpath('//span[@class="article-detail-date rt"]/text()').get()
        match = re.search(r"\d{2}/\d{2}/\d{4}", date)
        return match.group(0) if match else ""

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_images(self, response) -> list[str]:
        return response.xpath('//table[@class="MASTERCMS_TPL_TABLE"]//img/@src').getall()        
    
    def get_document_urls(self, response, entry=None) -> list[str]:
        return []

    def get_authors(self, response) -> str :
        return response.xpath('//h2[@class="author-title"]/a[@class="author-link f0"]/text()').get()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//div[@class="btn-viewmore"]/a[contains(text(), "Sau")]/@href').get()