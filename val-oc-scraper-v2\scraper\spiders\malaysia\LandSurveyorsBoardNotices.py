from scraper.OCSpider import OCSpider

class LandSurveyorsBoardNotices(OCSpider):
    name = "LandSurveyorsBoardNotices"
    
    start_urls_names = {
        "http://www.ljt.org.my/article/notice": "News",  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "http://www.ljt.org.my/article/notice"
    }

    country = "Malaysia"
    
    charset = "iso-8859-1"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map ={}

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//table[contains(@class, 'views-table')]//tbody//tr"):
            url = article.xpath(".//td[contains(@class, 'views-field-nothing-1')]//a//@href").get()
            title = article.xpath(".//td[contains(@class, 'views-field-nothing-1')]//a//text()").get()
            date = article.xpath(".//td[contains(@class, 'views-field-created')]//text()").get()
            if date:
                date = date.strip()
            if url and title and date:
                self.article_data_map[url] = {"title": title, "date": date}
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response): 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")    
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None