#MinistryofHomeAffairs(MOHA)Announcements
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MinistryofHomeAffairsAnnouncements(OCSpider):
    name = "MinistryofHomeAffairsAnnouncements"

    start_urls_names = {
        "https://www.moha.gov.my/index.php/ms/terkini": "News"
        }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       articles= []
       for article in response.xpath('//*[@id="adminForm"]/table/tbody//tr'):
           link = article.xpath(".//td[1]//@href").get()
           date = article.xpath(".//td[2]//text()").get()
           if link:
               articles.append(link)
               self.article_data_map[link]={
                    "date":date,
                }
       return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("/html/body/div/div[5]/div/div/div[2]/h3//text()").get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('/html/body/div/div[5]/div/div/div[2]//text()').getall())
   
    def get_images(self, response) -> list:
        return response.xpath('/html/body/div/div[5]/div/div/div[2]/div[2]/p/img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return response.xpath('/html/body/div/div[5]/div/div/div[2]/div[2]/p/a//@href').getall()
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        link = response.xpath('(//a[@class="next"]/@href)').getall()
       
        if len(link)==1:
            return link[0]
        elif len(link)==2:
            return link[1]
        else:
          return None