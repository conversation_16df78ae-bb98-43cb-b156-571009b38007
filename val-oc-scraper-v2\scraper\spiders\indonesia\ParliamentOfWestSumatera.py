from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re

class ParliamentOfWestSumatera(OCSpider):
    name = "ParliamentOfWestSumatera"
    
    start_urls_names = {
         "https://dprd.sumutprov.go.id/berita-terkini" : "Berita Lainnya",
        #  "https://dprd.sumutprov.go.id/agenda-kegiatan" : "Agenda Kegiatan", ##no hrefs for article present
         "https://dprd.sumbarprov.go.id/home/<USER>/11" : "File 11",
         "https://ppid.sumbarprov.go.id/home/<USER>" : "PPID" 
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    HEADLESS_BROWSER_WAIT_TIME = 100
    
    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,            
	}

    indonesian_to_english = {
        "Januari": "January",
        "Februari": "February",
        "Maret": "March",
        "April": "April",
        "Mei": "May",
        "Juni": "June",
        "Juli": "July",
        "Agustus": "August",
        "September": "September",
        "Oktober": "October",
        "November": "November",
        "Desember": "December",
    }

    indonesian_months = {
        1: "Januari",
        2: "Februari",
        3: "Maret",
        4: "April",
        5: "Mei",
        6: "Juni",
        7: "Juli",
        8: "Agustus",
        9: "September",
        10: "Oktober",
        11: "November",
        12: "December"
    }
    
    def get_articles(self, response) -> list:  
        start_url=response.meta.get('start_url')
        articles =list()
        if "berita-terkini" in start_url:
            for url in response.xpath('//div[@class="group relative"]//a/@href').getall():
                url = response.urljoin(url) 
                url=url.replace('https://proxy.scrapeops.io','https://dprd.sumutprov.go.id')
                full_url=response.urljoin(url)
                articles.append(full_url)
        elif "home/file" in start_url:
            articles =  response.xpath('//div[@class="news-post article-post"]//h2/a/@href').getall()
        elif "home/index-berita" in start_url:
            articles = response.xpath('//h6[@class="blog_title"]//a/@href').getall()
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = (response.xpath('//h1[@class="mt-2 mb-5 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"]//text()').get() 
                 or response.xpath('//div[@class="title-post"]/h1/text()').get() or response.xpath('//div[@class="blog_text"]/h2/text()').get())
        return title
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="mx-auto max-w-7xl px-6 text-base leading-7 text-gray-700"]//p//text()').getall() or response.xpath('//div[@class="post-content"]//p/text()').getall() or response.xpath('//div[@class="blog_text"]//p/text()').getall()) 
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        start_url = response.meta.get('start_url')
        if "berita-terkini" in start_url:
            raw_date = response.xpath('//div[@class="flex mb-5"]//p[2]//text()').get()
            for indo,eng in self.indonesian_to_english.items():
                raw_date = raw_date.replace(indo, eng)
            date_part = raw_date.split(', ')[-1]
            return date_part.strip()
        elif "home/file" in start_url:
            raw_date=response.xpath('//div[@class="title-post"]/h1/text()').get()
            pattern = r"(\d{1,2}\s+\w+\s+\d{4})"
            match = re.search(pattern, raw_date)
            raw_date =match.group(1)
            for indo, eng in self.indonesian_to_english.items():
                raw_date = raw_date.replace(indo, eng)
            return raw_date
        elif "home/index-berita" in start_url:
            date_str = response.xpath('(//a[@href="#"])[7]//text()').get().strip()
            final_date = self.convert_to_indonesian_date(date_str)
            for indo, eng in self.indonesian_to_english.items():
                if indo in date_str:
                    final_date= final_date.replace(indo, eng)
                    break 
            return final_date

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return response.xpath('//blockquote//p//a/@href').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
      return response.xpath('//a[span[text()="Next »"]]/@href').get() or response.xpath('//a[@aria-label="Next"]/@href').get() or response.xpath('//a[@rel="next"]/@href').get()

    def convert_to_indonesian_date(self,date_str):
        dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        return f"{dt.day:02d} {self.indonesian_months[dt.month]} {dt.year}"