from scraper.OCSpider import OCSpider
import dateparser

class HigherEducationDepartmentAnnouncements(OCSpider):
    name = "HigherEducationDepartmentAnnouncements"

    start_urls_names = {
        "https://jpt.mohe.gov.my/portal/index.php/ms/pengumuman": "News"
    }
    
    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {} 

    def get_articles(self, response) -> list:
        curr_url = response.xpath('//*[@id="adminForm"]/table/tbody/tr/td[1]/a//@href').getall()
        for i in range(1,len(curr_url)+1):
            curr_date =response.xpath(f'//*[@id="adminForm"]/table/tbody/tr[{i}]/td[2]//text()').get()
            curr_url1 = response.xpath(f'//*[@id="adminForm"]/table/tbody/tr[{i}]/td[1]/a//@href').get()
            self.article_data_map[curr_url1] = {"curr_date":curr_date}
        return curr_url
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return response.xpath('//*[@class="uk-margin-medium-top"]/p//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        curr_date1=  self.article_data_map.get(response.request.meta.get('entry'), {}).get("curr_date", "")
        parsed_date = dateparser.parse(curr_date1, languages=['en', 'ms']) 
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        pdf =  response.xpath('//div[@class="uk-margin-medium-top"]//a[not(contains(@href, ".jpg")) and not(contains(@href, ".png"))]/@href | /html/body/div/div[5]/div/div/div/div/div/iframe//@src').getall()
        pdfs = []
        for i in pdf:
            if "http" not in i :
                i= "https://jpt.mohe.gov.my" + i
            else:
                i = i
            pdfs.append(i)
        return pdfs
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = response.xpath('//*[@class="next"]//@href').get()
        if  next_page:
            return next_page
        return None 