from scraper.OCSpider import OCSpider
import scrapy

class DepartmentOfVeterinaryServicesNewsClippings(OCSpider):
    name = "DepartmentOfVeterinaryServicesNewsClippings"
    
    start_urls_names = {
        "https://www.dvs.gov.my/index.php/pages/view/5060": "News Clippings", # Pagination is not supported 
        "https://www.dvs.gov.my/index.php/pages/view/5065": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5070": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5071": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5074": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/4390": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/4421": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/4657": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/4671": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/4677": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/4920": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5036": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5037": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5038": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5039": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5043": "News Clippings", # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/5044": "News Clippings", # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.dvs.gov.my/index.php/pages/view/5060",
        "https://www.dvs.gov.my/index.php/pages/view/5065",
        "https://www.dvs.gov.my/index.php/pages/view/5070",
        "https://www.dvs.gov.my/index.php/pages/view/5071",
        "https://www.dvs.gov.my/index.php/pages/view/5074",
        "https://www.dvs.gov.my/index.php/pages/view/4390",
        "https://www.dvs.gov.my/index.php/pages/view/4421",
        "https://www.dvs.gov.my/index.php/pages/view/4657",
        "https://www.dvs.gov.my/index.php/pages/view/4671",
        "https://www.dvs.gov.my/index.php/pages/view/4677",
        "https://www.dvs.gov.my/index.php/pages/view/4920",
        "https://www.dvs.gov.my/index.php/pages/view/5036",
        "https://www.dvs.gov.my/index.php/pages/view/5037",
        "https://www.dvs.gov.my/index.php/pages/view/5038",
        "https://www.dvs.gov.my/index.php/pages/view/5039",
        "https://www.dvs.gov.my/index.php/pages/view/5043",
        "https://www.dvs.gov.my/index.php/pages/view/5044"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//div[@class='page-content']//table//tbody//tr//ul//li"): 
                url = article.xpath(".//a//@href").get()
                title=article.xpath("//div[@class='page-title']//h1//text()").get()
                date = article.xpath("//div[@class='page-title']//h1//text()").get()
                if url and title and date:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    self.article_data_map[full_url]={"title": title, "date": date}
                    articles.append(full_url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %Y"
    
    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date_str = date.split()
        date_f = " ".join(date_str[-2:])
        month_map = {
                    'Januari': 'January',
                    'Februari': 'February',
                    'Mac': 'March',
                    'April': 'April',
                    'Mei': 'May',
                    'Jun': 'June',
                    'Julai': 'July',
                    'Ogos': 'August',
                    'September': 'September',
                    'Oktober': 'October',
                    'November': 'November',
                    'Disember': 'December',
                    'Jan': 'January',
                    'Feb': 'February',
                    'Mac': 'March',
                    'Apr': 'April',
                    'Mei': 'May',
                    'Jun': 'June',
                    'Jul': 'July',
                    'Ogos': 'August',
                    'Sep': 'September',
                    'Okt': 'October',
                    'Nov': 'November',
                    'Dis': 'December'
                }
        for malay, eng in month_map.items():
            if malay in date_f:
               date_f = date_f.replace(malay, eng)
               break
        return date_f
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None