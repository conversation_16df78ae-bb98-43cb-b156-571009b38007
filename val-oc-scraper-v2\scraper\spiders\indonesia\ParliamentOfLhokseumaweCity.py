from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import urllib.parse
import scrapy
import re
from datetime import datetime

class ParliamentOfLhokseumaweCity(OCSpider):
    name = "ParliamentOfLhokseumaweCity"

    start_urls_names = {
        "https://dprk-lhokseumawekota.go.id/category/berita-dprk/": "Press Releases",
        "https://dprk-lhokseumawekota.go.id/category/pengumuman/": "Press Releases",
        "https://jdih-dprk.lhokseumawekota.go.id/dih": "Press Releases",
    }

    api_start_urls = {
        "https://jdih-dprk.lhokseumawekota.go.id/dih": {
            "url": "https://jdih-dprk.lhokseumawekota.go.id/dih/jsondata?",
            "payload" : {
                "draw": "1",
                "start": "0",
                "length":"100"
            }
        }
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        if "jdih" not in start_url:
            url = start_url
            api_url = start_url
            yield scrapy.Request(
                url = url,
                headers ={
                "Content-Type":"text/html; charset=UTF-8",
            },
            callback = self.parse,
            meta={
                    "start_url": start_url,
                    "api_url": api_url,
                })
        else:
            api_data = self.api_start_urls.get(start_url)
            if not api_data:
                return
            payload = response.meta.get("payload", api_data["payload"].copy())
            api_url = api_data["url"]
            full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
            yield scrapy.Request(
                url=full_api_url,
                method="GET",
                headers={
                    "Content-Type": "application/json",
                },
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": payload["draw"]
                }
            )
    
    article_data_map ={}  # Mapping date and title with child articles from start URL

    def get_articles(self, response) -> list:
        start_url = response.meta.get("start_url")
        articles = []
        if "dprk-lhokseumawekota.go.id"  in start_url:
            for article in response.xpath("//div[@class='content']//article[@class='item-list']"):
                url = article.xpath(".//h2//a//@href").get()
                title = article.xpath(".//h2//a//text()").get()
                date = article.xpath(".//span[@class='tie-date']//text()").get()
                print(url)
                if url and title and date:
                    self.article_data_map[url] = {"date":date, "title" : title}
                    articles.append(url)
            return articles
        else:
            data = response.json()
            article_data=data.get("data",[])
            for item in article_data:
                slug= item[0]
                match = re.search(r'href="([^"]+)"', slug)
                if match:
                    url = match.group(1)
                    articles.append(url)
            return articles
        
    def get_href(self, entry) -> str:
        return entry
        
    def get_title(self, response) -> str:
        title =self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        if not title :
            title = response.xpath("//h1//text()").get()  
        return title
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='container']//div[@class='col-lg-6']//div[@class='row']//text() | //div[@class='entry']//p[@style='text-align: justify;']//text()").getall()) 
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='entry']//p//img//@src").getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        indonesian_months = {
                "Januari": "January",
                "Februari": "February",
                "Maret": "March",
                "April": "April",
                "Mei": "May",
                "Juni": "June",
                "Juli": "July",
                "Agustus": "August",
                "September": "September",
                "Oktober": "October",
                "November": "November",
                "Desember": "December"
            }
        for indo, eng in indonesian_months.items():
            if indo in date:
                date = date.replace(indo, eng)
                break
        try:
            dt = datetime.strptime(date.strip(), "%B %d, %Y")
            return dt.strftime("%Y-%m-%d")
        except Exception:
            return datetime.now().strftime(self.date_format())

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        if "jdih-dprk" not in response.url:
            pdf = response.xpath("//div[@class='card-body isi-berita']//p//a[contains(@href,'.pdf')]//@href").getall()
            pdfs=[]
            for i in pdf:
                i = f'https://dprk.lhokseumawekota.go.id{i}'
                pdfs.append(i)
            return pdfs
        else:
            return response.xpath("//div[@class='pull-right']//a[contains(@href,'download')]//@href").getall()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return response.xpath("//span[@id='tie-next-page']//a//@href").get()