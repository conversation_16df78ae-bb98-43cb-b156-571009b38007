from typing import Optional
from scraper.OCSpider import OCSpider
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
import dateparser

class MalaysiaPetroleumResourcesCorporationPublications(OCSpider):
    name = "MalaysiaPetroleumResourcesCorporationPublications"
    
    start_urls_names = {
        "https://mprc.gov.my/resources/publications/": " News"
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request 
    
    charset = "iso-8859-1"

    country = "Malaysia"
    
    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
   
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='container w-full py-10 mx-auto']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h2[@class='mt-10 mb-4 font-bold tracking-wider text-mprc-blackText']//text()|//div[@class='flex flex-col items-start justify-between px-4 py-3 gap-y-6']//p[@class='pb-2 text-xs font-semibold text-start']//text()").get().strip()
    
    def get_body(self, response) -> str:
        texts = response.xpath("//div[@class='flex flex-col justify-start gap-6 mx-6 md:mx-20']//p//text()").getall()
        return " ".join(text.strip() for text in texts if text.strip())
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> Optional[str]: 
        date_text = response.xpath("//div[@class='flex-col hidden mx-6 mb-6 md:mx-20 sm:flex']//p[@class='text-base font-semibold text-mprc-blackText']//text()").get()
        if date_text:
            parsed_date = dateparser.parse(date_text, languages=['en', 'ms'])
            if parsed_date:
                return parsed_date.strftime("%B %d, %Y")
        return None
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return True
        
    def get_next_page(self, response):
        return None