from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import unquote

class CentralBankOfMalaysiaPressReleases(OCSpider):
    name = "CentralBankOfMalaysiaPressReleases"

    start_urls_names = {
       "https://www.bnm.gov.my/press-release" : "Press Releases"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        # current_year = response.meta.get("current_year", datetime.now().year)
        current_year = response.meta.get("current_year",2025)
        url = f"{start_url}-{current_year}"
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(url, timeout=10000), callback=self.parse, meta = {'current_year' : current_year}
        )
        request.meta['start_url'] = response.request.meta['start_url']
        # request.meta['current_year'] = response.request.meta['current_year']
        yield request

    def get_articles(self, response) -> list: 
        mapping = {}
        articles = [] 
        containers = response.xpath('//tbody[@id="myTable"]//tr')
        # print(containers)
        for box in containers:
            link = box.xpath('.//a/@href').get()
            title = box.xpath('.//a/text()').get()
            date = box.xpath('.//p/text()').re_first(r'\d{1,2} \w{3} \d{4}')
            # print(link,title,date)
            if link:
                normalized_link = unquote(link).rstrip('/')
                articles.append(normalized_link)
                mapping[normalized_link] = {
                        'title': title,
                        'pdf': normalized_link,    # Keeping relative path
                        'date': date
                    }
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ''
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url,{}).get('date')

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
  
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) :
        current_year = response.meta.get("current_year")
        print(current_year," is current year")
        next_year = str(int(current_year)-1)
        if response.status == 200:
            return next_year
        else :
            return 
        
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        next_year = self.get_next_page(response)
        if next_year:
            url = f"{start_url}-{next_year}"
            hbp = HeadlessBrowserProxy()
            yield scrapy.Request(
                hbp.get_proxy(url, timeout=20000),
                callback=self.parse,
                meta = {
                    "start_url" : start_url,
                    "current_year": next_year
                }
            )