from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfCentralSulawesi(OCSpider):
    name = "ParliamentOfCentralSulawesi"

    start_urls_names = {
        #The latest section of both url is same 
        "https://dprd.sultengprov.go.id/": "Latest", #Alternate url
        # "https://dprd.sultengprov.go.id/albums/" : "Latest", # 404 - Page Not Found
        # "https://dprd.sultengprov.go.id/halaman/detail/informasi-publik" : "Latest",# 404 - Page Not Found
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,            
	}
    
    def get_articles(self, response) -> list:  
        return list(set(response.xpath('//h4[@class="post-title"]//a/@href').getall()))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="post-title"]/a/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="container"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="container"]//img/@src').getall()
       
    def date_format(self) -> str:
        return "%a, %d %b %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('(//span[@class="post-on has-dot"])[6]//text()').get().strip()

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
       return None