from typing import Optional
from scraper.OCSpider import OCSpider
import re
from dateutil import parser
from typing import Optional

class CentralBankOfMalaysia(OCSpider):
    name = "CentralBankOfMalaysia"
    
    
    start_urls_names = {
         "https://www.bnm.gov.my/banking-islamic-banking": "Banking & Islamic Banking",
         "https://www.bnm.gov.my/insurance-takaful": "Insurance & Takaful",
        "https://www.bnm.gov.my/development-financial-institutions": "Development Financial Institutions",
        "https://www.bnm.gov.my/money-services-business" :  "Money Services Business",
          "https://www.bnm.gov.my/intermediaries": "Intermediaries",
          "https://www.bnm.gov.my/payment-systems": "Payment Systems",
         "https://www.bnm.gov.my/dnfbp":"DNFBPs& NBFIs",
        "https://www.bnm.gov.my/regulations/currency": "Currency",

    }

    
    article_data_map = {}

    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        'DEFAULT_REQUEST_HEADERS': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
    }
    }
    HEADLESS_BROWSER_WAIT_TIME = 50000

    

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_date_map = {}
    
    def get_articles(self, response) -> list:
        print("DEBUG: Entered get_articles")

        # Extract article mapping with correct date inheritance
        article_mapping = self.extract_articles_with_dates(response)
        print(f"DEBUG: article_mapping found {len(article_mapping)} articles")

        self.article_date_map = article_mapping

        urls = list(article_mapping.keys())
        print(f"DEBUG: Returning {len(urls)} article URLs directly from article_mapping")

        return urls


    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        # Try to get title from the article_date_map
        full_url = response.url
        if hasattr(self, 'article_date_map') and full_url in self.article_date_map:
            return self.article_date_map[full_url].get('title', '').strip()

        # Fallback: Try XPath from article header
        title = response.xpath("//div[contains(@class, 'article-header')]//h2/text()").get()
        if title:
            return title.strip()

        # Fallback: Try content-based bold text as title
        alt_title = " ".join(response.xpath("//div[contains(@class, 'article-content-cs')]//b//text()").getall()).strip()
        return alt_title





      
    def get_body(self, response) -> str:
        # Check if the URL ends with .pdf (case-insensitive)
        if response.url.lower().endswith('.pdf'):
            return ""

        # Extract all text from the article content block
        body_parts = response.xpath("//div[contains(@class, 'article-content-cs')]//text()").getall()
        body_text = " ".join(part.strip() for part in body_parts if part.strip())

        return body_text
    

        

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%d %b %Y"
    
  

    def get_date(self, response) -> Optional[str]:
        full_url = response.url.rstrip('/')

        if hasattr(self, 'article_date_map') and full_url in self.article_date_map:
            raw_date = self.article_date_map[full_url].get('date')
            try:
                # Robust parse and format
                date_obj = parser.parse(raw_date)
                return date_obj.strftime(self.date_format())
            except Exception as e:
                print(f"ERROR parsing date '{raw_date}': {e}")
                return None

        header_date = response.xpath('//div[@class="article-header"]//span[contains(@class, "text-muted")]/text()').get()
        if header_date:
            try:
                date_obj = parser.parse(header_date.strip())
                return date_obj.strftime(self.date_format())
            except:
                return header_date.strip()

        texts = response.xpath('//div[contains(@class, "article-content-cs")]//text()').getall()
        combined_text = " ".join(texts)
        match = re.search(r'\b\d{1,2} (January|February|March|April|May|June|July|August|September|October|November|December) \d{4}\b', combined_text)
        if match:
            try:
                date_obj = parser.parse(match.group())
                return date_obj.strftime(self.date_format())
            except:
                return match.group()

        return None



    def get_authors(self, response):
        return []
    

    def get_document_urls(self, response, entry=None) -> list:
        document_urls = []

        # If the current URL itself is a document (like .pdf), just return it
        if response.url.lower().endswith(('.pdf', '.doc', '.docx', '.xls', '.xlsx')):
            document_urls.append(response.url)
            return document_urls

        # Otherwise, try to extract document links from HTML content
        file_links = response.xpath("//a[contains(@href, '.pdf') or contains(@href, '.doc') or contains(@href, '.xls')]")
        for link in file_links:
            href = link.xpath("./@href").get()
            if href:
                file_url = response.urljoin(href)
                document_urls.append(file_url)

        return document_urls

    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        return None
    


    def extract_articles_with_dates(self, response):
        article_mapping = {}
        rows = response.xpath("//table//tr")
        print(f"Initial rows found: {len(rows)}")

        for row_index, row in enumerate(rows):
            # Get both visible and hidden dates
            hidden_date = row.xpath(".//td[1]/span[@class='hidden']/text()").get()
            visible_date = row.xpath(".//td[1]/text()[not(parent::span)]").get()

            # Prefer the more readable date
            raw_date = visible_date or hidden_date
            cleaned_date = raw_date.strip() if raw_date else None

            print(f"\nProcessing row {row_index}")
            print(f"Raw date: {raw_date}, Cleaned: {cleaned_date}")

            links = row.xpath(".//a")
            print(f"Found {len(links)} link(s)")

            for link in links:
                href = link.xpath("./@href").get()
                title = link.xpath("normalize-space(.)").get()

                if href:
                    full_url = response.urljoin(href).rstrip('/')
                    print(f"  Link title: '{title}', href: '{href}'")
                    article_mapping[full_url] = {
                        "title": title,
                        "date": cleaned_date,
                        "article_url": full_url
                    }

        return article_mapping


