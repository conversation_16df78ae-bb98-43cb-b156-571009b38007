from scraper.OCSpider import <PERSON>CSpider
import scrapy
from typing import List
from scraper.utils.helper import body_normalization
import json

class PennsylvaniaDepartmentofTransportation(OCSpider):

    name = "PennsylvaniaDepartmentofTransportation"

    country = "US"

    # HEADLESS_BROWSER_WAIT_TIME = 30000 
    
    # custom_settings = {
	# 	"DOWNLOADER_MIDDLEWARES" : {
	# 		'scraper.middlewares.HeadlessBrowserProxy': 350,
	# 	},
	# 	"DOWNLOAD_DELAY" : 6,
	# }

    start_urls_names={
        "https://mdot.ms.gov/portal/news/" : "Press Releases",
    }

    api_start_url = {
        "https://mdot.ms.gov/portal/news/": {
            "url": "https://platform.cloud.coveo.com/rest/search/v2?organizationId=commonwealthofpennsylvaniaproductiono8jd9ckm",
            "payload": {
                "firstResult" : '0' ,
            }
        }
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization" : "Bearer xx4e57cda9-3464-437d-9375-b947ca6b72c8"
    }


    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload["firstResult"]
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=self.headers,
            formdata=payload,
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
        )
        
    charset = "utf-8"
    
    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        json_data = json.loads(response.text)
        articles = [item['PrintableUri'] for item in json_data.get('results', []) if 'PrintableUri' in item]
        return articles if articles else []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='text']//p//text()").getall())
    
    def date_format(self) -> str:
        return  "%B %d, %Y"

    def get_date(self, response):
        return response.xpath('//div[@class="cmp-hero__dateline"]//text()').get()

    def get_authors(self, response) -> str:
        return []
    
    def get_images(self, response) -> list:
        return []
    
    def get_page_flag(self) -> bool:
        return False 

    def get_next_page(self, response):
        current_page = int(response.meta.get("current_page"))
        total_articles = response.json().get('data', {}).get('count', 1)
        page_size = 10
        max_pages = int((total_articles / page_size) + 1)
        if current_page < max_pages:
            current_page = current_page + 1
            return str(current_page)
        else:
            return None

    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        next_page = self.get_next_page(response)
        payload["start"] = next_page
        if next_page:
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded;"},
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}") 