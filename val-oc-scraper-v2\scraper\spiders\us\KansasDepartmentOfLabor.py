from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re

class KansasDepartmentOfLabor(OCSpider):
    name = "KansasDepartmentOfLabor"

    country = "US"
    
    start_urls_names = {
        "https://www.dol.ks.gov/employers/advanced-components/list-detail-pages/news-list": "News List",
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 10000  # 10 seconds wait time
    
    charset = "utf-8"
    
    article_map = {}  # Mapping child articles with Title from start URL
    
    article_date_map = {}  # Mapping child articles with date from start URL
    
    article_data_map = {}  # Mapping child articles with PDF from start URL
    
    @property
    def language(self):
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        return self.extract_articles_with_meta(response)
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.meta.get("title", "")
    
    def get_body(self, response) -> str:
        raw_body_parts = response.xpath('//div[@class="detail-content"]//text()').getall()
        if raw_body_parts:
            return body_normalization(raw_body_parts)
        else:
            return " "  # Some articles have only PDF's to scrape
    
    def get_images(self, response) -> list:
        return []
    
    def get_authors(self, response) -> list:
        return ""
    
    def get_document_urls(self, response, entry=None):
        content_type = response.headers.get('Content-Type', b'').decode('utf-8')
        if 'application/pdf' in content_type:
            return [response.url]
        pdf_links = response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
        return [response.urljoin(link) for link in pdf_links if link.lower().endswith('.pdf')]
    
    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response):
        raw_date = response.meta.get("date") or self.article_date_map.get(response.url)
        match = re.search(r"\b\d{2}/\d{2}/\d{4}\b", raw_date)
        if match:
            return datetime.strptime(match.group(), self.date_format()).strftime("%m/%d/%Y")
        return datetime.fromisoformat(raw_date).strftime("%m/%d/%Y")
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if getattr(self, 'has_run_next_page', False):
            return None
        self.has_run_next_page = True
        link = response.xpath('//a[@class="filter-archive"]/@href').get()
        return response.urljoin(link.strip()) if link else None
    
    def extract_articles_with_meta(self, response):
        articles = response.xpath('//ul[contains(@class, "list-main")]/li')
        urls = []
        for article in articles:
            link = article.xpath('.//a[@class="item-title"]/@href').get()
            title = article.xpath('.//a[@class="item-title"]/text()').get()
            raw_date = article.xpath('.//p[contains(@class, "item-date")]/text()').get()
            if link and title and raw_date:
                full_url = response.urljoin(link.strip().split('?')[0])
                urls.append(full_url)
                self.article_map[full_url] = {"title": title.strip(), "date": raw_date.strip()}
                self.article_date_map[full_url] = raw_date.strip()
                if full_url.lower().endswith('.pdf'):
                    self.article_data_map[full_url] = {"pdf": [full_url]}
        return urls