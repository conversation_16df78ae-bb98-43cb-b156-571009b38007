from scraper.OCSpider import OCSpider

class DepartmentOfVeterinaryServicesArchive(OCSpider):
    name = "DepartmentOfVeterinaryServicesArchive"
    
    start_urls_names = {
        "https://www.dvs.gov.my/index.php/pages/view/1849": "Archive",  # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/1609": "Archive",  # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/1447": "Archive",  # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/1443": "Archive",  # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/1441": "Archive",  # Pagination is not supported
        "https://www.dvs.gov.my/index.php/pages/view/1439": "Archive",  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.dvs.gov.my/index.php/pages/view/1849",
        "https://www.dvs.gov.my/index.php/pages/view/1609",
        "https://www.dvs.gov.my/index.php/pages/view/1447",
        "https://www.dvs.gov.my/index.php/pages/view/1443",
        "https://www.dvs.gov.my/index.php/pages/view/1441",
        "https://www.dvs.gov.my/index.php/pages/view/1439"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//figure//table[@align='center']//tbody//tr"): 
                url = article.xpath(".//a//@href").get()
                title=article.xpath(".//a//text()").get()
                date = article.xpath(".//a//text()").get()
                if url and title and date:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    self.article_data_map[full_url]={"title": title, "date": date}
                    articles.append(full_url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry 

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %Y"

    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if date:
            if 'Pelanggan' in date:
                date_part = " ".join(date.split()[-2:])
                if 'Jan' in date_part:
                    date_part = 'Januari 2015'
            else:
                date_part = date.strip()
            month_map = {
                'Januari': 'January',
                'Februari': 'February',
                'Mac': 'March',
                'April': 'April',
                'Mei': 'May',
                'Jun': 'June',
                'Julai': 'July',
                'Ogos': 'August',
                'September': 'September',
                'Oktober': 'October',
                'November': 'November',
                'Disember': 'December'
            }
            for malay, eng in month_map.items():
                if malay in date_part:
                    date_part = date_part.replace(malay, eng)
                    break
            return date_part
        return ""
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None