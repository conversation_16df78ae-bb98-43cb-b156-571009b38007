import re
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfSerangRegency(OCSpider):
    name = "ParliamentOfSerangRegency"
    
    start_urls_names = {
        "https://dprd.serangkab.go.id/berita" : "News"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='card-body']//a[@class='d-block']//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='txt']//h4//text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//h4[@class='title_blog']//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='cover_blog']//img//@src").getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        raw = response.xpath("//div[@class='body_blog text-justify']//time//text()").get()
        if raw:
            date_part = raw.split("|")[0].strip()
            return date_part
        return ""
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//div[@class='container']//ul[@class='pagination']//a[@rel='next']//@href").get()
