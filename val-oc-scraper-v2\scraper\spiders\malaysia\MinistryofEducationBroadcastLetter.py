from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization 

class MinistryOfEducationBroadcastLetter(OCSpider):
    name = "MinistryOfEducationBroadcastLetter"

    start_urls_names = {
        'https://www.moe.gov.my/pemberitahuan/surat-siaran': 'ministry' # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.moe.gov.my/pemberitahuan/surat-siaran'
    }

    charset = "utf-8"

    HEADLESS_BROWSER_WAIT_TIME = 30000

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		}
	}

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_url_date_docUrl_mapping={}

    def get_articles(self, response) -> list:
        articles = response.xpath('//tr')
        for article in articles:
            title = article.xpath('.//td[1]//a/text()').get()
            child_url = article.xpath('.//td[1]//a/@href').get()
            date = article.xpath('.//td[2]/text()').get()
            if date and child_url:
                self.article_url_date_docUrl_mapping[child_url]=[title, date]
        return list(self.article_url_date_docUrl_mapping.keys())
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_url_date_docUrl_mapping.get(response.url)[0]

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//table//p//text()').getall())

    def get_date(self, response) -> str:
        return self.article_url_date_docUrl_mapping.get(response.url)[1]

    def date_format(self) -> str:
        return "%d %b %Y"

    def get_images(self, response) -> list[str]:
       return response.xpath('//p//img/@src').getall()

    def get_document_urls(self, response, entry=None):
        urls = response.xpath('//a[@class="doclink btn btn-primary"]/@href').getall()
        if not urls:
            return []
        return [
            {
                "url": response.urljoin(url),
                "headers": {
                    "User-Agent": (
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                        "AppleWebKit/537.36 (KHTML, like Gecko) "
                        "Chrome/124.0.0.0 Safari/537.36"
                    ),
                    "Referer": response.url
                }
            }
            for url in urls
        ]
       
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def next_page(self, response):
        return None