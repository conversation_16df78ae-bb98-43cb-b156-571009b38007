from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy
import re

class GeneralDepartmentofVietnamCustoms(OCSpider):
    name = "GeneralDepartmentofVietnamCustoms"

    start_urls_names = {
        "https://www.customs.gov.vn/index.jsp?pageId=4&cid=25": "NewsArticles", 
    }

    start_urls_with_no_pagination_set = {
        "https://www.customs.gov.vn/index.jsp?pageId=4&cid=25"
    }
    
    custom_settings = {
           "DOWNLOADER_MIDDLEWARES": {
               'scraper.middlewares.HeadlessBrowserProxy': 3000
           },
           "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 5000

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
    
    def parse_intermediate(self, response):
        articles =response.xpath('//div[@class="content_item"]//h3[@class="content_title"]//a/@href').getall()
        total_articles = len(articles)
        start_url = list(self.start_urls_names.keys())
        for i in start_url:
            for start_idx in range(0,total_articles, 100): 
                yield scrapy.Request(
                    url=i,  
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': i
                    },  
                    dont_filter=True
                )

    def get_articles(self, response) -> list:  
       articles = []
       for article in response.xpath('//div[@class="content_item"]'):
          url = article.xpath(".//h3[@class='content_title']//a/@href").get()
          date = article.xpath(".//p[@class='note-layout']//text()").get()
          title = body_normalization(article.xpath(".//h3[@class='content_title']//a//text()").getall())
          if url and date and title:
            articles.append(url)
            self.article_data_map[url] = {
              "date": date.strip(),"title": title
            }
       return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="component-title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//content[@class="component-content"]//p//text()').getall())
   
    def get_images(self, response) -> list:
        return response.xpath('//content[@class="component-content"]//p//img//@src').getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        dates =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        match = re.search(r"(\d{2}/\d{2}/\d{4})", dates)
        date_str = match.group(1) 
        dt = datetime.strptime(date_str, "%d/%m/%Y")
        formatted = dt.strftime("%Y-%m-%d")
        return formatted
    
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None