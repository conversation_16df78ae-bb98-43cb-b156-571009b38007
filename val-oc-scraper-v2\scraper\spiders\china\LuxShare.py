from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
class LuxShare(OCSpider):
    name = "LuxShare"

    start_urls_names = {
        "https://www.luxshare-ict.com/news/release.html": "立讯精密",
        "https://www.luxshare-ict.com/investors/announcement.html": "立讯精密",
        "https://www.luxshare-ict.com/investors/financial-reports.html": "立讯精密", 
    }

    article_data_map ={}  # Mapping child article with title, date and PDF from start URL

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            articles = []
            for article in response.xpath("//div[@class='invest_list_btm']//ul//li| //div[@class='news_list_cont']//ul//li"):
                url = article.xpath(".//a//@href").get()
                title = article.xpath(".//div[@class='ilbu_li_le_info mod_word1']//p//text() | .//div[@class='nrru_li_info']//p//text()").get()
                date = article.xpath(".//div[@class='nrru_li_time mod_word']//p//text() | .//div[@class='ilbu_li_le_time mod_word']//p//text()").get()
                if url and title and date:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"
                    clean_date = date.strip()
                    self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                    articles.append(full_url) 
            return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath("//div[@class='detail_wrap_le_btm']//p//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        if response.url.lower().endswith(".pdf"):
            return [response.url]
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")
        if pdf_url != 'None':
            return [pdf_url]
        else:
            return response

    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath("//div[@class='detail_wrap_le_btm']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='rp']//a//@href").get()
        if next_page:
            return next_page
        else:
            return None