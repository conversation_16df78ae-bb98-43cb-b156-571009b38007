import re
from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class HoChiMinhNationalPoliticalAcademyVietnam(OCSpider):
    name = "HoChiMinhNationalPoliticalAcademyVietnam"

    start_urls_names = {
        "https://hcma.vn/Pages/danh-sach-tin-tuc.aspx?cm=183": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    country = "Vietnam"

    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="col-md-12 nopadding divdsmobi"]//a//@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="news-details"]//h1[@class="contenTitle"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath('//div[@class="noidungtt" and @id="noidung"]//p//text()').getall()
        )

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="noidungtt" and @id="noidung"]//img//@src').getall()

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> Optional[str]:
        text = response.xpath('//div[@class="col-md-4"]//text()').getall()
        combined = " ".join(text).strip()
        match = re.search(r"(\d{2}/\d{2}/\d{4})", combined)
        return match.group(1) if match else None

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return None