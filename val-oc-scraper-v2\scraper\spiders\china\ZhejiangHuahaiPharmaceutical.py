from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import Optional
from datetime import datetime 
class ZhejiangHuahaiPharmaceutical(OCSpider):
    name = "ZhejiangHuahaiPharmaceutical"

    start_urls_names = {
        "https://www.huahaipharm.com/news.html": "华海药业",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    @property    # Site is in English mode
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="right"]/h2[@class="h2"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//p[@style="text-align:center;margin-bottom:15px;font-size:20px;"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article-box"]//p//text()').getall())
 
    def get_images(self, response) -> list:
        return response.xpath("//p//img//@src").getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date_str = response.xpath("//p[@style='text-align:center; margin-bottom: 20px;']/text()").get().strip()
        return datetime.strptime(date_str, "%Y-%m-%d").strftime(self.date_format())
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_page=response.xpath("//div[@class='SplitPage']//a[.//img[contains(@src, 'right')]]/@href").get()
        if next_page:
            return next_page
        else:
            return None