#VietnamAcademyofSocialSciences
#VietnamAcademyofScienceandTechnology
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
import scrapy
import re
from scraper.middlewares import HeadlessBrowserProxy
from datetime import datetime

class VietnamAcademyofSocialSciences(OCSpider):
    name = "VietnamAcademyofSocialSciences"

    start_urls_names = {
        f"https://vass.gov.vn/tin-tuc/danh-sach-tin-tuc-d14.html?Ascending=false&FieldSort=NgayHienThi&Page={i}": "News" for i in range(1,367,1)
        }   #Check the last page no i 

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//div[@class="col-md-9 col-12 left-pane"]//div[@class="img"]//a//@href').getall()
     
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p[@style="text-align:justify;"]//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//div[@class="main"]//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//span[@class="date"]//text()').get()
        clean_date = date.strip()   # "26/02/2024"
        dt = datetime.strptime(clean_date, "%H:%M %d/%m/%Y")
        formatted = dt.strftime("%Y-%m-%d")
        return formatted
        

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None