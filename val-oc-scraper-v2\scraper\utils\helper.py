import re
import urllib.parse
import requests

# Acceptable file extensions and MIME types
VALID_EXTENSIONS = ('.pdf', '.doc', '.docx')

def is_valid_doc_url(url):
    # Step 1: Basic URL syntax check
    try:
        parsed = urllib.parse.urlparse(url)
        if parsed.scheme not in ('http', 'https') or not parsed.netloc:
            raise Exception("Bad url")
    except Exception:
        raise Exception("Unable to access url")

    # Step 2: File extension check
    if not parsed.path.lower().endswith(VALID_EXTENSIONS):
        raise Exception("Invalid extension")

    # Step 3: HEAD request to check Content-Type
    try:
        response = requests.head(url, allow_redirects=True, timeout=5)
        if response.status_code !=200:
            raise Exception("PDF not downloadable")
    except requests.RequestException:
        raise Exception("Unable to access url")

    return url

def body_normalization(body, delimiter='\n'):
    body_list = []
    for p in body:
        element = p.replace("\t", "").replace("\r", "").replace("\n", "").replace("\u3000", "").replace("\xa0", "").strip()
        if element != "":
            body_list.append(element)
    return delimiter.join(body_list)

# Acceptable file extensions and MIME types
VALID_EXTENSIONS = ('.pdf', '.doc', '.docx')

def is_valid_doc_url(url):
    # Step 1: Basic URL syntax check
    try:
        parsed = urllib.parse.urlparse(url)
        if parsed.scheme not in ('http', 'https') or not parsed.netloc:
            raise Exception("Bad url")
    except Exception:
        raise Exception("Unable to access url")

    # Step 2: File extension check
    if not parsed.path.lower().endswith(VALID_EXTENSIONS):
        raise Exception("Invalid extension")

    # Step 3: HEAD request to check Content-Type
    try:
        response = requests.head(url, allow_redirects=True, timeout=5)
    except requests.RequestException:
        raise Exception("Unable to access url")
    return url


def body_normalization_us(text):
    text = (text.replace("\t", "").replace("\r", "").replace("\u3000", "").replace("\xa0", "").strip())
    return text.replace('Washington, D.C. -', '')\
        .replace('Atlanta, GA—', '')\
        .replace('Washington, DC –', '')\
        .replace('Washington, D.C. –', '')\
        .replace('WILBERFORCE, OHIO–', '')\
        .replace('Tupper Lake, N.Y. -', '')\
        .replace('Greenbush, N.Y. – ', '')\
        .replace('WASHINGTON, DC – ', '')\
        .replace('WASHINGTON –', '')\
        .replace('(ATLANTA) —', '')\
        .replace('SPRINGFIELD, IL —', '')\
        .replace('(ATLANTA) –', '')\
        .replace('(WASHINGTON) –', "")\
        .replace('WASHINGTON, D.C. —', "")\
        .replace('CAHOKIA HEIGHTS, IL —', "")\
        .replace('(WASHINGTON) —', "")\
        .replace('WASHINGTON\n –', '')\
        .replace('NORTH JERSEY —', '')\
        .replace('WASHINGTON—', '')\
        .replace('Washington, D.C.–', '')\
        .replace('Nicholasville, KY–', '')\
        .replace('Duluth, GA—', '')\
        .replace('MIAMI, FL -', '')\
        .replace('TALLAHASSEE, FL –', '')\
        .replace('WASHINGTON, D.C.-', '')\
        .replace('East Greenbush, N.Y. - ', '')\
        .replace('Champlain, N.Y. -', '')\
        .replace('East Greenbush, N.Y.–', '')\
        .replace('DAYTON, OHIO–', '')\
        .replace('Rome, N.Y. -', '')\
        .replace('Norcross, GA—', '')\
        .replace('Carlisle, KY–', '')\
        .replace('STUART, Fla. -', '')\
        .replace('Flemingsburg, KY–', '')\
        .replace('Birmingham, AL —', '')\
        .replace('KEY WEST, FL -', '')\
        .replace('WASHINGTON–', '')\
        .replace('OTSEGO –', '')\
        .replace('STUART, Fla - U.S', '')\
        .replace('Lexington, KY–', '')\
        .replace('PHOENIX—', '')
 
SOURCE_TIER = {
    "1": [
        "CommunistPartyOfChina",
        "StateCouncil",
        "peoplenews",
        "EuropeanCommission",
        "JakartaPost",
        "NITI",
        "NHK",
        "MinistryOfForeignAffairsIndonesia",
        "tempo",
        "NationalReserveBankOfIndia",
        "xinhuameiridaily",
        "Vnanet",
        "mospi",
        "VietnamCommunistParty",
        "MediaIndonesia",
        "Kompas",
        "VoiceOfVietnam",
        "houseofrepresentatives",
        "primeministersoffice",
        "tribunnews",
        "EuropeanParliamentPositions",
        "NationalAssemblyOfVietnam",
        "BankOfJapan",
        "LiberalDemocraticParty",
        "HouseOfCouncillors",
        "MinistryOfForeignAffairsJapan",
        "cabinetoffice",
        "officialeujournal",
        "VietnamGovernmentPortal",
        "officialeujournal",
        "HouseOfRepresentatives",
        "us_house_representative",
        "us_congress_press_release",
        "EuConsiliumPressRelease",
        "xinhuanet",
        "GovtOfFrance",
        "USAdmin_woproxy",
        "USAdmin_proxy",
        "GermanyGovtSpeeches"
    ],
    "2": [
        "CentralCommissionForDisciplineInspection",
        "NationalDevelopmentAndReformCommission",
        "StateAdministrationForMarketRegulation",
        "StateOwnedAssetsSupervisionAndAdministrationCommision",
        "PeopleBankOfChina",
        "gmwnews",
        "EUCoreperAgendas",
        "EUCoreper2Agendas",
        "EUCouncilAgendaOfMeetings",
        "EUPublicVoteResults",
        "committeesofeuropeanparliament",
        "RegisterofCommissionDocumentsCommisionProposals",
        "qnb",
        "EuropeanParliamentNews"
    ],
    "3": [
        "sina_news",
        "yicai",
        "ChinaSecuritiesRegulatoryCommission",
        "BankingAndInsuranceRegulatoryCommission",
        "MinistryOfAgricultureAndRuralAffairs",
        "MinistryOfCommerce",
        "MinistryOfEcologyAndEnvironment",
        "MinistryOfDefense",
        "MinistryOfEducation",
        "MinistryOfFinance",
        "MinistryOfForeignAffairs",
        "MinistryOfIndustryAndInformationTechnology",
        "MinistryOfHousingAndUrbanRuralDevelopment",
        "MinistryOfJustice",
        "MinistryOfScienceAndTechnology",
        "MinistryOfTransport",
        "MinistryOfNaturalResources",
        "NationalEnergyAdministration",
        "NationalFoodAndStrategicReserveAdminstration",
        "NationalHealthcareSecurityAdministration",
        "NMPA"
    ],
    "4": [
        "AVICIndustryAndFinanceNews",
        "SANAN",
        "HascoGroup",
        "WesternSecurities",
        "PerfectWorld",
        'FederalForeignOffice', # like this
        'zgshb',
        'CivilAviationAdministration',
        'COFCOGroupCoLtd',
        'AgriculturalBankofChinaCoLtd',
        'ctdnews',
        'NationalPublicComplaintsAndProposalsAdministration',
        'OfficeOfTheCentralCyberspaceAffairsCommission',
        'ccgp_site_information',
        'ChinaNonferrousMiningGroupCoLtd',
        'ChinaRailwayConstructionGroupCorporationLTD',
        'ChinaRailwayEngineeringGroupCoLtd',
        'ChinaGeneralNuclearPowerCorporationLtd',
        'UsCongressPressReleasesPattern2',
        'ctn',
        'ChinaFAWGroupCoLtd',
        'renmingongandaily',
        'ynetnews',
        'zhongguoyiyaodaily',
        'ChinaInternationalEngineeringConsultingCoLTD',
        'ChinaNationalRailwayGroupCorporationLTD',
        'ChinaThreeGorgesCorporation',
        'gongyishibao',
        'stdaily',
        'ChinaStateShipbuildingCorporation',
        'XinxingInternationalChinaGroupCoLtd',
        'ChinaOrientAssetManagementCoLtd',
        'MiningMetallurgyTechnologyGroupCoLtd',
        'ChinaDongfangElectricGroupCoLtd',
        'ChinaUnitedNetworkCommunicationsGroupCoLtd',
        'ChinaResourcesGroupCoLtd',
        'ChinaPutianInformationIndustryGroupCoLtd',
        'chinahightech',
        'USHouseRepresentativePattern1_101_150',
        'hunandaily',
        'NationalRailwayAdministration',
        'ben',
        'StateAdministrationOfTraditionalChineseMedicine',
        'ChinaAgriculturalDevelopmentGroupCoLtd',
        'henandaily',
        'NationalIntellectualPropertyAdministration',
        'zzdzb',
        'rmrbhwb',
        'ChinaSinochemGroupCoLtd',
        'Anhuidaily',
        'ningxiadaily',
        'xinhuameiridaily',
        'ChinaPetroleumNewsCenter',
        'PipelineChina',
        'CentralGovernmentBondRegistrationClearingCoLtd',
        'ChinaPostGroupCompanyLimited',
        'CECChinaElectronics',
        'bbtnews',
        'xinhuadaily',
        'ChinaPetroleumNewsCenter2',
        'GeneralInstituteMechanicalScienceResearchGroupCoLtd',
        'ElectronicsTechnologyGroupCorporation',
        'zhongguotiedaodaily',
        'ChinaInternationalDevelopmentCooperationAgency',
        'pkulaw_lar',
        'USHouseRepresentativePattern2_1_50',
        'MinistryOfWaterResources',
        'zhongguojiaoshidaily',
        'NationalImmigrationAdministration',
        'ChinaTourismGroupCoLtd',
        'peoplenews',
        'chongqingdaily',
        'xfrb',
        'ChinaHuarongAssetManagementCoLtd',
        'TaiwanWorkOffice',
        'ChinaInspectionandCertificationGroup',
        'ChinaNationalMachineryIndustryGroupCoLtd',
        'zhongguohuanjingdaily',
        'ChinaPublishingGroupCoLTD',
        'ChinaGeneralTechnologyCoLTD',
        'ChinaGoldGroupCoLtd',
        'ChinaPolyGroupCoLtd',
        'ChinaOceanShippingGroupCoLTD',
        'guizhoudaily',
        'nfrb',
        'ChinaEnergyConstructionGroupCoLTD',
        'szsb',
        'ChinaBuildingResearchInstituteCoLTD',
        'gansudaily',
        'cctv',
        'jcrb',
        'ChinaJianyinInvestmentcoLTD2',
        'zhongguojiaoyudaily',
        'cicn',
        'jjckb',
        'IndustrialCommercialBankofChinaCoLtd',
        'zhongguodianyingdaily',
        'ChinaStateConstructionGroupCoLtd',
        'shfzb',
        'USHouseRepresentativePattern1_51_100',
        'zhongguoshehuidaily',
        'zhongguoqingniandaily',
        'BankofCommunicationsCoLtd',
        'SinosteelGroupCoLTD',
        'DongfengMotorGroupCoLtd',
        'ChinaGalaxyFinancialHoldingCoLtd',
        'hubeidaily',
        'NationalCryptographyAdministration',
        'xianggangwenhuidaily',
        'ChinaNationalCoalGroupCoLTD',
        'zhongguoshuiwudaily',
        'CommissionForPublicSectorReform',
        'jjrb',
        'HongKongAndMacauAffairsOffice',
        'HongKongAndMacauAffairsOffice_1',
        'HongKongAndMacauAffairsOffice_2',
        'HongKongAndMacauAffairsOffice_3',
        'pkulaw_chl',
        'ChinaChengtongHoldingGroupCoLtd',
        'AgriculturalDevelopmentBankofChina',
        'zqrb',
        'zgshbnews',
        'ChinaNationalOffshoreOilCorporationLTD',
        'ChinaNationalAviationGroupCoLTD',
        'ChinaExportCreditInsuranceCorporation',
        'NationalCultureHeritageAdministration',
        'TheUnitedFrontWorkDepartment',
        'ChinaAviationFuelGroupCoLTD',
        'ChinaNationalChemicalCorporationLtd',
        'ChinaInvestmentCorporationLimited',
        'ChinaNationalTobaccoCorporation',
        'wangyi',
        'NationalGovernmentOfficesAdmin',
        'ChinaForestryGroupCoLtd',
        'jiangxidaily',
        'zejiangdaily',
        'AuditOffice',
        'ChinaDevelopmentInvestmentGroupCoLtd',
        'OverseasChineseTownGroupCoLtd',
        'ChinaCommunicationsConstructionGroupCoLtd',
        'ChinaDevelopmentBank',
        'ChinaGalaxyInvestmentManagementCoLtd',
        'ChinaConstructionBankLTD',
        'ChinaInternationalTechnicalIntelligenceCoLTD',
        'ChinaInformationCommunicationTechnologyGroupCoLTD',
        'ChinaRailwayMaterialsGroupCoLtd',
        'zzqyb',
        'ChinaNationalPharmaceuticalGroupCoLtd',
        'xinhuanet_2',
        'laoningdaily',
        'ChinaTobacco',
        'MinistryOfCultureAndTourism',
        'PartySchoolOfTheCentralCommitteeOfCPC',
        'ChinaCITICGroupCoLtd',
        'UsCongressPressReleasesPattern3',
        'OfficialEUJournal',
        'ChinaSaltIndustryGroupCoLtd',
        'YouyanTechnologyGroupCoLtd',
        'xinmin',
        'ChinaReinsuranceCoLtd',
        'caacmedia',
        'CentralPeopleGovernmentOfThePeopleRepublicOfChina',
        'StateSecrecyBureau',
        'ChinaSteelResearchTechnologyGroupCoLTD',
        'MinistryOfCivilAffairs',
        'cb',
        'ChinaHuanengGroupCorporation',
        'CounsellorOfficeAndCentralInstitueForCultureAndHistory',
        'ChinaCindaAssetManagementCoLtd',
        'zzyzb',
        'xinjiangdaily',
        'ChinaCoalGeologyAdministration',
        'ChinaMinmetalsCorporationLTD',
        'ChinaNanguangGroupCoLTD',
        'AnshanIronSteelGroupCoLtd',
        'StateCouncil2',
        'ChinaMerchantsGroup',
        'ChinaXDGroupCoLtd',
        'USHouseRepresentativePattern1_201_250',
        'sszn',
        'ExportImportBankofChina',
        'ChinaRailwayRollingStockCorporationLtd',
        'xizangdaily',
        'fujiandaily',
        'MinistryOfVeternalAffaires',
        'ChinaCoalTechnologyIndustryGroupCoLTD',
        'cnii',
        'zhongguoribaodaily',
        'nanfangdaily',
        'guangmingdaily',
        'ChinaRadioAndTelevisionNetworkCoLTD',
        'ccgp_procurement',
        'NationalEthnicsAffairsCommission',
        'ChinaCivilAviationInformationGroupCoLtd',
        'ChinaGreatWallAssetManagementCoLtd',
        'GeneralAdministrationMetallurgicalGeologyChina',
        'ChinaLifeInsuranceGroupCorporation',
        'cqnnews',
        'USHouseRepresentativePattern1_151_200',
        'zhongguohangtiandaily',
        'zhongguoxiaofeizhedaily',
        'zhongguominzhudaily',
        'ChinaTelecom',
        'zhongguoxinxidaily',
        'rmzxb',
        'NationalNuclearSafetyAdministration',
        'ChinaAeroEngineCorporation',
        'ChinaHuadianGroupCorporation',
        'ChinaFirstHeavyGroupCoLtd',
        'fzbnews',
        'zhongguohangkongdaily',
        'InstituteOfPartyHistoryAndLiterature',
        'sichuandaily',
        'ChinaForeignCultureGroupCoLTD',
        'MinistryOfHumanResourcesAndSocialSecurity',
        'CommissionForPoliticalAndLegalAffairs',
        'HarbinElectricGroupCoLtd',
        'qnb',
        'ChinaRailwaySignalCommunicationGroupCoLtd',
        'jingji',
        'nongmindaily',
        'ceh',
        'CommercialAircraftCorporationChinaLtd',
        'dagongdaily',
        'ChinaPetroleumChemicalCorporation',
        'ChinaEnergyConservationEnvironmentalProtectionGroupCoLtd',
        'pcn',
        'zhongguoshangbaodaily',
        'zhonghuadushudaily',
        'ChinaAerospaceScienceAndTechnologyCorporation',
        'UsCongressPressReleasesPattern1',
        'jiaotonganquanzhoukandaily',
        'StatePostBureau',
        'StateArchivesBureau',
        'gongrenribaodaily',
        'ChinaGrainReservesManagementGroupCoLTD',
        'ChinaSouthernAirlinesGroupCoLTD',
        'BankofChinaCorporationLimited',
        'ChinaBuildingMaterialsGroupCoLtd',
        'GeneralAdministrationOfSport',
        'ChinaEverbrightGroupCorporation',
        'cnfood',
        'USHouseRepresentativePattern2_101_150',
        'ChinaCivilizationNetwork',
        'ChinaNorthIndustriesGroupCorporationLimited',
        'bjd',
        'hainandaily',
        'gmwnews',
        'heilongjiangdaily',
        'CentralCommitteeOfTheCommunistPartyOfChina',
        'ChinaConstructionTechnologyCoLtd',
        'StateAdministrationOfForeignExchange',
        'ChinaJianyinInvestmentcoLTD',
        'MinistryOfEmergencyManagement',
        'jfjbmap',
        'zhongguozhengquandaily',
        'InternationalDepartment',
        'neimenggudaily',
        'StateForestryAndGrasslandAdministration',
        'xsdb',
        'PowerConstructionCorporationOfChinaLTD',
        'ChinaHualuGroupCoLTD',
        'zzzzrsb',
        'USHouseRepresentativePattern1_1_50',
        'ChinaDatangGroupCorporation',
        'NationalEnergyGroup',
        'StatePowerNivestmentCorporation',
        'zhongguoguofangdaily',
        'USHouseRepresentativePattern2_51_100',
        'PeopleInsuranceGroupCompanyOfChinaLimited',
        'cpdnews',
        'xinhuanet_1',
        'wenyibaodaily',
        'jilindaily',
        'StateAdministrationOfTaxation',
        'AluminumCorporationOfChinaLtd',
        'sciencenet',
        'tianjindaily',
        'ChinaSouthernPowerGrid',
        'NMPASpider',
        'ChinaguoxinholdingcoLTD',
        'jiefangdaily',
        'ChinaNationalChemicalEngineeringGroupCoLTD',
        'USSenatePressReleasePattern1',
        'USSenatePressReleasePattern2',
        'USSenatePressReleasePattern3',
        'USSenatePressReleasePattern4',
        'USSenatePressReleasePattern5',
        'USSenatePressReleasePattern6',
        'USSenatePressReleasePattern7',
        'zhongguowenwudaily',
        'yunnandaily',
        'PaymentsandClearingAssociationofChina',
        'NationalAssociationofFinancialMarketInstitutionalInvestors',
        'NationalInternetFinanceAssociationOfChina',
        'ChinaNationalAssociationofFinanceCompanies',
        'ChinaCertifiedTaxAgentsAssociation',
        'AssetManagementAssociationofChina',
        'ChinaTrusteeAssociation',
        'ChinaFinancingGuaranteeAssociation',
        'ChinaMicrocreditCompaniesAssociation',
        'InsuranceAssetManagementAssociationOfChina',
        'ChinaMergersAcquisitionsAssociation',
        'ChinaEducationInvestorsChamberofCommerce',
        'ChinaEnvironmentChamberofCommerce',
        'ChinaNewspaperAssociation',
        'ChinaXinhuaBookstoreAssociation',
        'ChinaPrintingTechnologyAssociation',
        'ChinaPublicRelationsAssociation',
        'ChinaCultureandEntertainmentIndustryAssociation',
        'ChinaPeriodicalAssociation',
        'PublisherAssociationofChina',
        'ChinaAssociationOfPerformingArts',
        'ChinaCultureEntertainmentIndustryAssociation',
        'ChinaEnterpriseCulturePromotionAssociation',
        'ChinaCulture',
        'ChinaMediaCulturePromotionAssociation'
        'BooksAndPeriodicalsDistributionAssociationOfChina',
        'ChinaMinistryOfCultureAndTourism',
        'ChinaPeriodicalAssociation',
        'ChinaFruitMarketingAssociation',
        'ChinaCooperationForNgoAssociation',
        'ChinaSteviaAssociation'
        'ChinaInternationalCultureAssociation',
        'ChinaPeriodicalAssociation',
        'CopyrightSocietyofChina',
        'PublisherAssociationOfChina',
        'ChinaMediaCulturePromotionAssociation',
        'ChinaScienceFilmVideoAssociation',
        'BooksAndPeriodicalsDistributionAssociationOfChina',
        'ChinaPublicCultureCenterAssociation',
        'ChinaSoftwareIndustryAssociation',
        'ChinaChamberofTourism',
        'ChinaAssociationOfRuralEnergyIndustry',
        'ChinaCustomBrokerAssociation',
        'ChinaPublicCultureCenterAssociation',
        'ChinaRadarIndustryAssociation',
        'ChinaCooperationForNgoAssociation',
        'ChinaTourismAssociation',
        'InternetSocietyOfChina',
        'ChinaNuclearIndustrySurveyDesignAssociation',
        'ChinaMunicipalEngineeringAssociation',
        'ChinaSteviaAssociation',
        'ChinaCommunicationsStandardsAssociation',
        'ChinaCommunicationsStandardsAssociation2',
        'AssociationOfChinaRareEarthIndustry',
        'ChinaInternationalExchangeAndPromotiveAssociationForMedicalAndHealthCare',
        'ChinaAssociationOfPesticideDevelopmentAndApplication',
        'ChinaQualityManagementAssociationForElectronicsIndustry',
        'ChinaAssociationDevelopmentZones',
        'ChinaFisheriesAssociation',
        'ChinaIsotopeAndRadiationAssociation',
        'ChinaMobileCommunicationsAssociation',
        'ChinaSemiconductorIndustryAssociation',
        'ChinaCivilAirportsAssociation1',
        'ChinaCivilAirportsAssociation2',
        'TheChineseEducationalArtAssociation',
        'ChinaMaritimeSafetyAssociation',
        'ChinaAssociationPromotionHealthScienceTechnology'
        'ChinaLocalRailwayAssociation',
        'AssociationForPromotionOfWestChinaResearchAndDevelopment',
        'ChinaTouristAttractionsAssociation',
        'ChinaCooperativeTradeEnterprisesAssociation',
        'ChinaAssociationOfShippingAgencies',
        'ChinaContainerIndustryAssociation',
        'ChinaNationalAssociationOfEngineeringConsultants',
        'ChinaExpressAssociation',
        'ChinaAirTransportAssociation',
        'ChinaAssociationPromotionHealthScienceTechnology',
        'ChinaPaintingsPhotographerAssociation',
        'ChinaMetrologyAssociation',
        'ChinaAssociationforEducationalTechnology',
        'ChinaChamberOfCommerceForImport',
        'ChinaAssociationOfRailwayEngneeringConstruction',
        'ChinaAssociationForQualityInspection',
        'GiantNetwork',
        'SanqiMutualEntertainment',
        'RohngshengPetrochemical',
        'ChinaLongsheng',
        "CHNews",
        "ChongqingRuralCommercialBank",
        "ZhongtaiSecuritiesCoLtd",
        "NewChinaInsurance",
        "ChinaSpacesatCoLTd",
        "ShanxiXinghuacunFenjiuDistillery",
        "WingtechTechnolgyCoLtd",
        "HTSEC",
        "CPIC",
        "ChinaFortuneLandDevelopmentCoLtd",
        "SDICPower",
        "ChinaYangtzePowerCoLtd",
        "AECCPowerCoLtd",
        "ChinaTourismGroupDutyFreeCorporationLimited"
        "ChinaFortuneLandDevelopmentCoLtd"
        "HTSEC",
        "seazen",
        "Hengli",
        "GuotaiHaitong",
        "Chinasatcom",
        "XingyuCoLtd",
        "FiiFoxconn",
        "CiatongSecurities",
        "HongtaSecurities",
        'Junzheng',
        'ChinaTourismGroupDutyFreeCorporationLimited',
        'Seazen',
        'CiatongSecurities',
        'FiiFoxconn',
        'ShanghaiLingang',
        'JonjeeHiTech',
        'ChinaTourismGroupDutyFreeCorporationLimited',
        'GuosenSecurities',
        'OrientSecurities',
        "ZijinMining",
        "MetallurgicalCorporationofChinaLimited",
        "IndustrialSecurities",
        "LONGI",
        "Junzheng",
        "HongtaSecurities",
        "USI",
        "JINKEShares",
        "Adisseo",
        "ShaanxiCoalIndustryCompanyLimited",
        "TSINGTAO"
        ],
    "5": [
        "ColoradoOfficeOfEconomicDevelopmentandInternationalTrade",
        "UtahDepartmentOfWorkforceServices",
        "OhioDepartmentOfJobAndFamilyServices",
        "MontanaDepartmentOfCommerce",
        "GovernorOfficeOfBusinessAndEconomicDevelopment",
        "NebraskaDepartmentOfEconomicDevelopment",
        "NewAmericaFoundation",
        "CarnegieEndowmentForInternationalPeace",
        "BipartisanPolicyCenter",
        "AtlanticCouncil",
        "CenterForStrategicAndInternationalStudies",
        "WilsonCenter",
        "RANDCorporation",
        "PewResearchCenter",
        "CenterOnBudgetAndPolicyPriorities",
        "IllinoisDepartmentOfCommerceAndEconomicOpportunity",
        "KansasDepartmentOfCommerce",
        "MarylandDepartmentOfCommerce",
        "MaineDepartmentOfEconomicAndCommunityDevelopment",
        "LouisianaEconomicDevelopment",
        "KentuckyCabinetForEconomicDevelopment",
        "IndianaEconomicDevelopmentCorporation",
        "IdahoDepartmentOfCommerce",
        "HawaiiDepartmentofBusinessEconomicDevelopmentAndTourism",
        "NewMexicoDepartmentofWorkforceSolutions",
        "NewHampshireEmploymentSecurity",
        "OhioDepartmentOfJobAndFamilyServices",
        "NebraskaDepartmentofLabor",
        "NorthCarolinaDepartmentOfCommerce",
        "LouisianaWorkforceCommission",
        "MontanaDepartmentofLaborandIndustry",
        "AlaskaDepartmentOfLaborAndWorkforceDevelopment",
        "NevadaAttorneyGeneral",
        "KentuckyTransportationCabinet",
        "VermontAgencyofTransportation",
        "PennsylvaniaDepartmentofTransportation",
        "OregonDepartmentofTransportation",
        "NevadaDepartmentofTransportation",
        "MississippiDepartmentofTransportation",
        "NewHampshireDepartmentofTransportation",
        "NebraskaDepartmentofTransportation",
        "NorthCarolinaDepartmentofTransportation",
        "NorthDakotaDepartmentofTransportation",
        "NewYorkStateDepartmentofTransportation",
        "NewJerseyDepartmentofTransportation",
        "MissouriAttorneyGeneral",
        "MissouriDepartmentofTransportation",
        "DepartmentOfTheTreasuryNewJersey",
        "LouisianaDepartmentOfTheTreasury",
        "OfficeOfStateTreasuryArizona",
        "StateOfDelawareNews",
        "MississippiDepartmentOfFinanceAndAdministration",
        "OfficeOfTheStateTreasureMiami",
        'AlabamaStateTreasury',
        'CaliforniaStateTreasurersOffice',
        'NewMexicoStateTreasurer',
        'NorthCarolinaDepartmentOfStateTreasurer',
        'NorthDakotaStateTreasurer',
        'OhioTreasurerofState',
        'OklahomaStateTreasurer',
        'OregonStateTreasury',
        'PennsylvaniaTreasuryDepartment',
        'SouthCarolinaStateTreasurer',
        'RhodeIslandOfficeoftheGeneralTreasurer',
        'ColoradoDepartmentoftheTreasury',
        'NewYorkStateDepartmentofTaxationandFinance',
        'KansasStateTreasurer',
        'MinnesotaOfficeOfTheStateTreasurer',
        'MontanaDepartmentOfAdministration',
        'NevadaStateTreasurer',
        'IowaStateTreasurer',
        'NewJerseyDepartmentOfTreasury',
        'MichiganDepartmentOfTreasury'
        'DepartmentOfTheTreasuryNewJersey',
        'MaineOfficeOfTheStateTreasurer',
        'DelawareStateTreasury',
        'GeorgiaOfficeOfTheStateTreasurer',
        'MassachusettsStateTreasury',
        'ArizonaStateTreasurersOffice',
        'SouthDakotaStateTreasurerOffice',
        'TexasComptrollerOfPublicAccount',
        'UtahStateTreasurerOffice',
        'VermontOfficeOfTheStateTreasurer',
        'WashingtonStateTreasurerOffice',
        'WestVirginiaStateTreasurerOffice',
        'WisconsinDepartmentOfRevenue',
        'WyomingStateTreasurerOffice',
        'ConnecticutOfficeOfTheStateTreasurer',
        'IllionoisOfficeOfTheStateTreasurers',
        'NewYorkAttorneyGeneral',
        'NorthDakotaAttorneyGeneral',
        'OhioAttorneyGeneral',
        'OklahomaAttorneyGeneral',
        'GeorgiaAttorneyGeneral',
        'IowaAttorneyGeneral',
        'TennesseeAttorneyGeneral',
        'TexasComptTexasAttorneyGeneral',
        'VirginiaAttorneyGeneral',
        'WashingtonAttorneyGeneral',
        'WisconsinAttorneyGeneral',
        'AlbamaOfficeOfAttorneyGeneral',
        'CaliforniaStateOfAttorneyGeneral',
        'ConnecticutOfficeOfAttorneyGeneral',
        'ColoradoAttorneyGeneral',
        'FloridaOfficeOfAttorneyGeneral',
        'IdahoOfficeOfAttorneyGeneral',
        'NorthCarolinaAttorneyGeneral',
        'HawaiiAttorneyGeneral',
        'MichiganAttorneyGeneral',
        'OregonAttorneyGeneral',
        'MaineAttorneyGeneral',
        'MassachusettsAttorneyGeneral',
        'MississippiAttorneyGeneral',
        'MontanaAttorneyGeneral',
        'NebraskaAttorneyGeneral',
        'NewHampshireAttorneyGeneral',
        'NewJerseyAttorneyGeneral',
        'ArkansasAttorneyGeneral',
        'NewMexicoAttorneyGeneral',
        'DelawareAttorneyGeneral',
        'IdahoOfficeOfAttorneyGeneral',
        'IllinoinsAttorneyGeneral',
        'KansasAttorneyGeneral',
        'LouisianaAttorneyGeneral',
        'NorthCarolinaAttorneyGeneral',
        'IllinoisStateTreasurersOffice',
        'AlaskaAttorneyGeneral',
        'ArizonaAttorneyGeneral',
        'SouthCarolinaAttorneyGeneral',
        'SouthDakotaAttorneyGeneral',
        'RhodeIslandAttorneyGeneral',
        'RhodeIslandDepartmentOfTransportation',
        'SouthDakotaDepartmentofTransportation',
        'TennesseeDepartmentofTransportation',
        'VirginiaDepartmentOfTransportation',
        'WashingtonStateDepartmentOfTransportation',
        'WestVirginiaDepartmentOfTransportation',
        'NewYorkEmpireStateDevelopment',
        'NorthCarolinaDepartmentOfCommerce',
        'OhioDevelopmentServicesAgency',
        'OklahomaDepartmentOfCommerce',
        'PennsylvaniaDepartmentOfCommunity',
        'RhodeIslandCommerceCorporation',
        'SouthCarolinaDepartmentOfCommerce',
        'BusinessOregon',
        'NorthDakotaDepartmentOfCommerce',
        'SouthDakotaGovernorsOfficeOfEconomicDevelopment',
        'TennesseeDepartmentOfEconomic',
        'MinnesotaDepartmentOfTransportation',
        'MassachusettsDepartmentOfTransportation',
        'LoiusianaDepartmentOfTransportation',
        'MichiganDepartmentOfTransportation',
        'OregonAttorneyGeneral',
        'PennsylvaniaAttorneyGeneral',
        'RhodeIslandAttorneyGeneral',
        'UtahAttorneyGeneral',
        'VermontAttorneyGeneral',
        'WestVirginiaAttorneyGeneral',
        'NewMexicoStateDepartmentofTransportation',
        'ArizonaDepartmentOfTransportation',
        'ArkansasDepartmentOfTransportation',
        'CaliforniaDepartmentOfTransportation',
        'FloridaDepartmentOfTransportation',
        'HawaiiDepartmentOfTransportation',
        'IdahoTransportationDepartment',
        'KansasDepartmentOfTransportation',
        'WisconsinDepartmentOfTransportation',
        'WyomingDepartmentOfTransportation',
        'MaineDepartmentOfTransportation',
        'AlabamaDepartmentOfTransportation',
        'ConnecticutDepartmentOfTransportation',
        'IllinoisDepartmentOfTransportation',
        'IowaDepartmentOfTransportation',
        'AlaskaDepartmentOfTransportationAndPublicFacilities',
        'ColoradoDepartmentOfTransportation',
        'UtahDepartmentOfTransportation',
        'GeorgiaDepartmentOfTransportation',
        'WyomingAttorneyGeneral'
        'MaineDepartmentOfLabour',
        'AlbamaDepartmentOfLabour',
        'ArizonaDepartmentOfEconomicSecurity',
        'IdahoDepartmentOfLabour',
        'TexasWorkforceCommisson',
        'MichiganDepartmentOfLaborAndEconomicOpportunity',
        'MarylandDepartmentOfLabour',
        "JobServiceNorthDakota",
        "WashingtonStateEmploymentSecurityDepartment",
        "VermontDepartmentOfLabor",
        "WisconsinDepartmentOfWorkforceDevelopment",
        "NewJerseyDepartmentOfLaborAndWorkforceDevelopment",
        "GeorgiaDepartmentOfLabor",
        "ArkansasDivisionOfWorkforceServices",
        "DelawareDepartmentOfLabor",
        "ColoradoDepartmentOfLaborAndEmployment",
        "CaliforniaDepartmentOfIndustrialRelations",
        "FloridaDepartmentOfEconomicOpportunity",
        "IllinoisDepartmentOfEmploymentSecurity",
        "MassachusettsExecutiveOfficeOfLaborAndWorkforceDevelopment",
        "IndianaDepartmentofWorkforceDevelopment",
        'VirginiaDepartmentOfTransportation',
        'WestVirginiaDepartmentOfTransportation',
        'WashingtonStateDepartmentOfTransportation',
        'TennesseeDepartmentofTransportation',
        "TennesseeDepartmentOfLaborAndWorkforceDevelopment",
        'SouthDakotaDepartmentOfTransportation',
        'RhodeIslandDepartmentOfTransportation',
        'SouthDakotaDepartmentOfLabour',
        'TexasDepartmentOfTransportation',
        'PennsylvaniaDepartmentOfLaborAndIndustry',
        "MassachusettsExecutiveOfficeOfHousingAndEconomicDevelopment",
        "MichiganEconomicDevelopmentCorporation",
        "MississippiDevelopmentAuthority",
        "MissouriDepartmentOfEconomicDevelopment",
        "MontanaDepartmentofCommerce",
        "NebraskaDepartmentofEconomicDevelopment",
        "WyomingBusinessCouncil",
        "NewJerseyEconomicDevelopmentAuthority",
        "ForeignPolicyResearchInstitute",
        "MiddleEastInstitute",
        "PetersonInstituteForInternationalEconomics",
       "NationalBureauOfEconomicResearch",
       "MercatusCenter",
       "ResourcesfortheFuture",
       "CenterForDataInnovation",
       "BelferCenterForScienceAndInternationalAffairs",
       "CenterForInternationalSecurityAndCooperation",
       "InformationTechnologyAndInnovationFoundation",
        'TexasEconomicDevelopmentCorporation',
        'UtahGovernorOfficeOfEconomicOpportunity',
        'VermontDepartmentOfEconomicDevelopment',
        'VirginiaEconomicDevelopmentPartnership',
        'WashingtonStateDepartmentOfCommerce',
        'WestVirginiaDepartmentOfEconomicDevelopment',
        'WisconsinEconomicDevelopmentCorporation',
        'AlaskaDepartmentOfCommerceCommunityAndEconomicDevelopment',
        'AlabamaDepartmentOfCommerce',
        'ArizonaCommerceAuthority',
        'ArkansasEconomicDevelopmentCommission',
        'ConnecticutDepartmentOfEconomicAndCommunityDevelopment',
        'DelawareDepartmentOfTransportation',
        'DelawareDivisionOfSmallBusiness',
        'EnterpriseFlorida',
        'GeorgiaDepartmentOfEconomicDevelopment',
        'GovernorOfficeOfBusinessAndEconomicDevelopment'
        'MaineDepartmentOfLabour'
        'GovernorOfficeOfBusinessAndEconomicDevelopment',
        'ConnecticutDepartmentOfLabor',
        'HawaiiDepartmentOfLaborAndIndustrialRelations',
        'IowaWorkforceDevelopment',
        'KansasDepartmentOfLabor',
        'MississippiDepartmentOfEmploymentSecurity',
        'MissouriDepartmentOfLaborAndIndustrialRelations',
        'NewYorkStateDepartmentOfLabor',
        'OklahomaEmploymentSecurityCommission',
        'OregonEmploymentDepartment',
        'SouthCarolinaDepartmentOfEmploymentAndWorkforce',
        'HeritageFoundations',
        'AmericanEnterpriseInstitute',
        'CatoInstitute',
        'HooverInstitution',
        'HudsonInstitute',
        'CenterForAmericanProgress',
        'EconomicPolicyInstitute',
        'UrbanInstitute',
        'ManhattanInstitute',
        'BrookingsInstitution',
        'InstituteForPolicyStudies',
        'MilkenInstitute',
        'CenterNewAmericanSecurity',
        'StimsonCenter',
        'WorldResourcesInstitute',
        'GermanMarshallFundOfUnitedStates',
        'MigrationPolicyInstitute',
        'AspenInstitute',
        'ThirdWay',
        'WoodrowWilsonInternationalCenterForScholars',
        'CenterForGlobalDevelopment',
        'CenterForClimateAndEnergySolutions',
        'MaineDepartmentOfLabour'
        'ChinaSpacesatCoLTd',
        'CarlsbergChina',
        'JHUSHI',
        'SYTECH',
        'MaineDepartmentOfLabour',
        'NewYorkStateDepartmentOfTransportation',
        'IndianaDepartmentOfWorkforceDevelopment',
        'NebraskaDepartmentOfLabor',
        'TexasEconomicDevelopmentCorporation',
        'GuangzhouPharHoldings',
        'UtahDepartmentOfWorkforceServices'
        "Haier",
        "Fuyao",
        "yuyuantm",
        "Greenland",
        "conch",
        "JiangsuChangjiang",
        "hundsun",
        "ZhejiangHuahaiPharmaceutical",
        "KweichowMoutai",
        "ZhongJinGoldCorporationLimited",
        "ChinaShipbuildingIndustryCorporation",
        "Tongwei",
        "Kingfa",
        "ChengduShengdiPharmaceutical",
        "Tiantanbio",
        "ParliamentOfSouthSulawesi",
        "ParliamentOfTanggamusRegency",
        "ParliamentOfWestSumatera",
        "ParliamentOfTanahBumbuRegency",
        "NewHampshireDepartmentOfTransportation"
    ],
    "6": [
        "ParliamentOfIslandsTidoreCity",
        "ParliamentOfLabuhanbatuRegency",
        "ParliamentOfBandaAcehCity",
        "ParliamentOfNaganRayaRegency",
        "ParliamentOfEastJava",
        "ParliamentOfSintangRegency",
        "ParliamentOfSumenepRegency",
        "ParliamentOfSolokCity",
        "ParliamentOfSolokRegency",
        "ParliamentOfWestSulawesi",
        "ParliamentOfSouthSulawesi",
        "RegentOfEastFlores",
        "ParliamentOfNorthAcehRegency",
        "ParliamentOfNorthTorajaRegency",
        "ParliamentOfWestAcehRegency",
        "ParliamentOfCentralSulawesi",
        "ParliamentOfTanggamusRegency"
        "ParliamentofPadangCity",
        "ParliamentofPinrangRegency",
        "ParliamentOfMamujuRegency",
        "ParliamentOfMakassarCity",
        "ParliamentOfPidieJayaRegency",
        "ParliamentOfTasikmalayaRegency",
        "ParliamentOfTanahBumbuRegency",
        "ParliamentOfTanggamusRegency",
        "ParliamentOfEastTanjungJabungRegency",
        "ParliamentOfPalembangCity",
        "ParliamentOfBoyolaliRegency",
        "ParliamentOfTangerangCity",
        "ParliamentOfTegalCity",
        "ParliamentOfKediriRegency",
        "ParliamentOfSouthKalimantan",
        "ParliamentOfWestSumatera"
        "ParliamentOfSabangCity"
        "ParliamentofPadangCity",
        "ParliamentofPinrangRegency",
        "ParliamentofPadangCity",
        "ParliamentofSekadauRegency",
        "ParliamentofMimikaRegency",
        "ParliamentOfSabangCity",
        "ParliamentOfKamparRegency",
        "ParliamentOfEastKalimantan",
        "Parliamentofsemarangregency",
        "ParliamentofRrokanhilirregency",
        "ParliamentofSemarangCity",
        "ParliamentOfSerangRegency",
        "ParliamentofPontianakCity",
        "ParliamentOfJembranaRegency",
        "ParliamentOfEastKalimantan",
        "ParliamentofSekadauRegency",
        "Parliamentofmojokertocity",
        "Parliamentofsemarangregency"
        "ParliamentOfEastKalimantan",
        "ParliamentOfBireuenRegency",
        "ParliamentOfAcehJayaRegency",
        "ParliamentOfLhokseumaweCity",
        'ParliamentOfYogyakartaCity',
        'ParliamentOfKudusRegency',
        'ParliamentOfKuninganRegency',
        'ParliamentOfNorthMaluku',
        'ParliamentOfKaimanaRegency',
        'ParliamentOfSelayarIslandsRegency',
        'ParliamentOfMedanCity',
        'ParliamentOfMetroCity',
        'Parliamentofsemarangregency',
        "RegentOfGianyar",
        "ParliamentofRrokanHilirregency",
        "ParliamentOfLamonganRegency",
        "ParliamentofRrokanHilirregency",
        "ParliamentOfSemarangCity",
        "ParliamentofNgawiRegency",
        "ParliamentofPasuruanCityberita",
        "ParliamentofPurbalinggaRegency",
        "ParliamentofSarolangunRegency",
        "ParliamentofPurwakartaRegency",
        "MinistryOfForeignAffairsMFA",
        "ParliamentOfPurbalinggaRegency",
        "ParliamentOfPurbalinggaRegency",
        "ParliamentofMimikaRegencyss"
        "ParliamentOfPurbalinggaRegency",
        "ParliamentOfWestSumatera",
        "ParliamentOfAceh"
    ],
    "7": [
        "PrimeMinisterOfficeParliamentaryReply",
        "PrimeMinisterOfficeSpeeches",
        "PrimeMinisterOfficePolicies",
        "PrimeMinisterOfficeNews",
        "PrimeMinisterOfficeSpeechArchive",
        "SelangorStateGovernmentStateSecretaryCirculars",
        "SelangorStateGovernmentAnnualReports"
        "ParliamentOfMalaysiaLHOralAnswers",
        "MinistryOfCommunicationsStatistics",
        "MinistryOfCommunicationsAnnualReports",
        "SecuritiesCommissionMalaysia",
        "CentralBankOfMalaysia",
        "BursaMalaysia",
        "FederalTerritoriesDirectorOfLandsAndMinesOfficeNewsClippings",
        "FederalTerritoriesDirectorOfLandsAndMinesOfficeNewsClippings",
        "FederalTerritoriesDirectorOfLandsAndMinesOfficeNewsClippings",
        "MinistryOfCommunicationsAnnualReports",
        "ParliamentOfMalaysiaAgongsAddresses",
        "ParliamentOfMalaysiaPressReleases",
        "MalaccaStateGovernmentSpeeches",
        "MalaysianMinisterofFinance",
        "LabuanFinancialServicesAuthority"
        "MinistryOfCommunicationsAnnualReports",
        "MalaysianQuarantineAndInspectionServicesStrategicPlan",
        "ParliamentOfMalaysiaLHWrittenAnswers",
        "MalaccaStateGovernmentBulletin",
        "ParliamentOfMalaysiaLHBills",
        "MalaccaStateGovernmentSpeeches",
        "MalaysianQuarantineAndInspectionServicesActs",
        "ParliamentOfMalaysiaLHHansard",
        "ParliamentOfMalaysiaUHOralAnswers",
        "ParliamentOfAceh",
        "PenangStateGovernmentAnnualReports"
        "MinistryOfRuralAndRegionalDevelopmentNewsClippings",
        "PenangStatGovernmentStrategicPlan",
        "PenangStateGovernmentAnnualReports",
        "MinistryOfRuralAndRegionalDevelopmentNewsClippings",    
        "MinistryOfRuralAndRegionalDevelopment",
        "ParliamentOfMalaysiaLHBills",
        "CompaniesCommissionOfMalaysia",
        "SelangorStateGovernmentAnnualReports",
        "MalaysiaDepositInsuranceCorporation",
        "MinistryOfWorksPublications",
        "MinistryofCommunicationsChiefSecretarysSpeeches",
        "MinistryofWorksChiefSecretarysSpeeches",
        "MinistryOfWorksDeputyMinistersSpeeches",
        "MinistryOfWorksMinistersSpeeches",
        "PenangStateGovernmentBulletin",
        "MinistryOfRuralAndRegionalDevelopmentCirculars",
        "MinistryOfRuralAndRegionalDevelopmentSpeeches"
        "ParliamentOfMalaysiaPressReleases",
        "PerakStateGovernmentSultanAddresses",
        "ParliamentOfMalaysiaPressReleases",
        "PenangStateGovernmentBulletin",
        "DMediaSystemPressReleases",
        "DMediaSystemPressCoverage",
        "DepartmentOfOccupationalSafetyAndHealthNewsCoverage",
        "DepartmentOfOccupationalSafetyAndHealthSpeeches",
        "DepartmentOfOccupationalSafetyAndHealthNewsActs",
        'PerakStateGovernmentSultanAddresses'
        "ParliamentOfMalaysiaPressReleases",
        "PenangStateGovernmentBulletin",
        'ParliamentOfMalaysia'
        "ParliamentOfMalaysiaPressReleases",
        "PenangStateGovernmentBulletin",
        "SelangorStateGovernmentAnnualAuditReports",
        "MinistryOfFinanceSpeeches",
        "MinistryOfFinanceNews",
        "MinistryOfFinancePressClippings"
        "ParliamentOfMalaysiaPressReleases",
        "PenangStateGovernmentBulletin",
        "SelangorStateGovernmentAnnualAuditReports",
        "SelangorStateGovernmentAnnualReports",
        "MinistryOfRuralAndRegionalDevelopmentArchive",
        "MinistryOfFinanceAnnouncements",
        "MinistryOfRuralAndRegionalDevelopmentPublications",
        "ParliamentOfMalaysiaUHBills",
        "ParliamentOfMalaysiaUHWrittenAnswers",
        "MalaysianMinisterOfFinance",
        "ParliamentOfMalaysiaLHOralAnswers"
        "MinistryOfEconomyNewsClippings",
        "RoyalMalaysianCustomsDepartmentNews",
        "RoyalMalaysianCustomsDepartmentAnnouncements",
        "NationalLandscapeDepartmentReferences",
        "NationalLandscapeDepartmentNewsClippings",
        "DepartmentOfNationalUnityAndIntegrationPressReleases",
        "DepartmentOfNationalUnityAndIntegrationSpeeches",
        "DepartmentOfNationalUnityAndIntegrationPublications",
        "ValuationAndPropertyServicesDepartmentBulletin",
        "ValuationAndPropertyServicesDepartmentAnnualReports",
        "ValuationAndPropertyServicesDepartmentNewsClippings",
        "ParliamentOfMalaysiaLHOralAnswers",
        "NationalHousingDepartmentPolicies",
        "MinistryOfFinancePressReleases",
        "DepartmentOfThePremierOfSarawakNews",
        "SabahStateGovernment",
        "AttorneyGeneralsChambers",
        "MinistryOfEconomyOtherSpeeches",
        "MinistryOfEconomyMinistersSpeeches",
        "MinistryOfEconomyChiefSecretarysSpeeches",
        "HigherEducationDepartmentAnnouncements",
        "HigherEducationDepartmentPublications",
        "AccountantGeneralsDepartmentGovernmentFinancialStatement",
        "AccountantGeneralsDepartmentAnnualReports",
        "PerakStateGovernmentBulletins",
        "PenangStateGovernmentStrategicPlan",
        "LabuanFinancialServicesAuthority",
        "InlandRevenueBoardOfMalaysiaPressReleases",
        "DepartmentOfAgricultureArchive",
        "DepartmentOfFisheriesMalaysiaArchive",
        "ImmigrationDepartmentOfMalaysiaNewsClippings",
        "InlandRevenueBoardOfMalaysiaActs",
        "InlandRevenueBoardOfMalaysiaAnnouncements",
        "InlandRevenueBoardOfMalaysiaPressReleases",
        "MinistryOfRuralAndRegionalDevelopmentSpeeches",
        "PenangStateGovernmentStrategicPlan",
        "AccountantGeneralsDepartmentStrategicPlan",
        "TheOfficeOftheGovernorOfSarawak",
        "LabuanFinancialServicesAuthority",
        "FederalTerritoriesDirectorofLandsandMinesOfficePublications",
        "BankRakyatAnnualReports",
        "BankRakyatFinancialStatements",
        "BankRakyatNews",
        "CentralBankOfMalaysiaNoticesAnnouncements",
        "BursaMalaysiaNasionalSpeeches",
        "BursaMalaysiaNasionalPressReleases",
        "BursaMalaysiaNasionalArticles",
        "BankSimpananNasionalAnnualReports",
        "ProtectionDivisionPressReleases",
        "CentralBankOfMalaysiaSpeeches",
        "CentralBankOfMalaysiaAnnualReports",
        "CentralBankOfMalaysiaPressReleases"
        "DepartmentOfAgriculturePressReleases",
        "NationalHousingDepartmentReports",
        "NationalHousingDepartmentBulletin",
        "NationalHousingDepartmentNewsCoverages",
        "AccountantGeneralsDepartmentAnnouncements",
        "LocalGovernmentDepartmentCirculars",
        "LocalGovernmentDepartmentStateByLaws",
        "AccountantGeneralDepartmentNewsClippings",
        "DepartmentOfLabourSarawakArchive",
        "DepartmentofLabourSarawakPublications",
        "HigherEducationDepartmentPressReleases",
        "HigherEducationDepartmentPolicyDocuments",
        "NationalLandscapeDepartmentPublications",
        "MinistryOfInvestmentTradeAndIndustryPressReleases",
        "MinistryOfInvestmentTradeAndIndustrySpeeches",
        "MinistryOfInvestmentTradeAndIndustryReports",
        "NationalRegistrationDepartmentAnnouncements",
        "NationalRegistrationDepartmentNewsClippings",
        "DepartmentofWaterSupplyAnnouncementsandActivities",
        "DepartmentOfVeterinaryServicesNewsClippings",
        "AccountantGeneralsDepartmentCircular",
        "DepartmentOfVeterinaryServicesPressReleases",
        "BankSimpananNasionalPressReleases",
        "MinistryOfFinancePressClippings",
        "PerakStateGovernmentChiefMinistersSpeeches",
        "MinistryOfRuralAndRegionalDevelopmentSpeeches"
        "AuditorGeneralOfficePressReleases",
        "ImmigrationDepartmentofMalaysiaAnnouncements",
        "MinistryOfHealthActs"
        "MinistryofHigherEducationMinistersSpeeches",
        "MinistryofHumanResourcesPressReleases",
        "MinistryofHigherEducationArchive",
        "MinistryofHigherEducationDeputyMinistersSpeeches",
        "ChiefGovernmentSecurityOfficeAnnouncements",
        "CentralBankofMalaysiaRatesStatistics",
        "SelangorStateMuftiDepartmentBulletin",
        "SelangorStateMuftiDepartmentAnnouncements",
        "SelangorStateMuftiDepartmentGuidelines",
        "SelangorStateMuftiDepartmentPressReleases",
        "SelangorStateMuftiDepartmentFatwa",
        "IntellectualPropertyCorporationofMalaysiaCopyrightAct",
        "IntellectualPropertyCorporationofMalaysiaGeographicalIndicationAct",
        "IntellectualPropertyCorporationofMalaysiaIndustrialDesignAct",
        "IntellectualPropertyCorporationofMalaysiaPressReleases",
        "IntellectualPropertyCorporationofMalaysiaPatentAct",
        "IntellectualPropertyCorporationofMalaysiaTrademarkAct",
        "MinistryofHumanResourcesArchive",
        "LocalGovernmentDepartmentMonthlyNews",
        "MinistryOfEconomyDeputyMinistersSpeeches",
        "MinistryofHigherEducationPressReleases",
        "NationalSafetyCouncilSOP",
        "MinistryOfDefenceNews",
        "MinistryOfDefenceNewsArchive",
        "MinistryOfInvestmentTradeAndIndustryNewsCoverage",
        "MinistryOfHigherEducationNewsCoverage",
        "MinistryOfHigherEducationAnnouncements",
        "MinistryOfDefencePressReleases2024",
        "MinistryOfDefenceMinistersSpeeches",
        "MinistryOfHealthActs",
        "MinistryOfHealthPressReleases",
        "MinistryofHealthPressReleaseArchive",
        "SustainableEnergyDevelopmentAuthorityMalaysiaAnnouncements",
        "SustainableEnergyDevelopmentAuthorityMalaysiaNews",
        "MinistryOfInvestmentTradeAndIndustryPolicies",
        "MinistryOfInvestmentTradeAndIndustryStrategicPlan",
        "CybersecurityMalaysiaAnnualReports",
        "CybersecurityMalaysiaPressReleases",
        "MinistryOfDigitalAnnouncements",
        "DepartmentOfFisheriesMalaysiaNewsClippings",
        "CentralBankOfMalaysiaPressReleases",
        "SelangorStateMuftiDepartmentNews",
        "FederalTerritorySyariahCourtPublications",
        "MinistryofTransportParliamentHansard",
        "MinistryofTourismArtsandCulture",
        "MinistryofNaturalResourcesEnvironmentSustainabilityNewsArchive",
        "MinistryofEducationPressReleases",
        "MinistryofEducationCirculars",
        "MinistryofEducationAnnouncements",
        "MinistryofEducationAnnouncements",
        "MinistryofDefenceDeputyMinistersSpeeches"
        "MinistryofDefenceDeputyMinistersSpeeches",
        "MalaysianExaminationCouncilAnnualReports",
        "MinistryofDefenceAnnualReports",
        "MinistryofFinanceBudget",
        "MinistryofFinanceEconomicReports",
        "MinistryofHealthNewsClippings",
        "MinistryofHealthPublications",
        "MinistryofHomeAffairsAnnouncements",
        "MinistryofHomeAffairsNews",
        "MinistryofHomeAffairsNewsClippings",
        "MinistryofHomeAffairsPressReleases",
        "MinistryofHomeAffairsReports",
        "MinistryofHomeAffairsSpeeches",
        "CustodianofNationalWaterAssetsAnnualReports",
        "SustainableEnergyDevelopmentAuthorityMalaysiaPressReleases",
        "NationalSafetyCouncilFederalNews",
        "MinistryOfInvestmentTradeAndIndustryStrategicPlan"
        "FederalTerritorySportsCouncilWIPERSAnnualReports",
        "FederalTerritortSportsCouncilWIPERSNewsBroadcast",
        "FederalTerritorySportsCouncilWIPERSSpeeches",
        "SustainableEnergyDevelopmentAuthorityMalaysiaLegislation",
        "LabuanFinancialServicesAuthorityPressReleases",
        "LandAndSurveyDepartmentNews",
        "LandSurveyorsBoardNotices",
        "LandSurveyorsBoardCirculars",
        "LandPublicTransportAuthorityAnnouncements",
        "LandPublicTransportAuthorityCirculars",
        "RoyalMalaysianCustomsDepartmentNewsClippings",
        "DepartmentOfFisheriesMalaysiaNewsClippings",
        "MalaysianExaminationCouncilGazette",
        "DepartmentofWildlifeandNationalParksGuidelines",
        "MinistryofHomeAffairsArchive",
        "PahangStateGovernmentPolicies",
        "PahangStateGovernmentNews",
        "MinistryofNaturalResourcesandEnvironmentSustainabilityPressReleasesArchive",
        "FederalTerritorySportsCouncilWIPERSNews",
        "CustodianofNationalWaterAssetsNewsPressReleases",
        "MinistryofDefenceDeputyMinistersSpeeches",
        "DepartmentofIrrigationandDrainageNews",
        "DepartmentofIrrigationandDrainageAnnouncementsArchive",
        "DepartmentofIrrigationandDrainageAnnualReports",
        "DepartmentofIrrigationandDrainageWaterResourcersPublication",
        "MinistryofEducationBroadcastLetter",
        "MinistryofHealthCirculars",
        "MinistryofTransportPressReleases",
        "EducationServiceCommissionCirculars",
        "EducationServiceCommissionAnnouncements",
        "EducationServiceCommissionAnnouncement",
        "EducationServiceCommissionStrategicPlans",
        "EducationServiceCommissionBulletins",
        "EducationServiceCommissionActivities",
        "RegistrationofCompanySpeeches",
        "FederalTerritorySportsCouncilWIPERSActsandLegislations",
        "RegistrationofCompanyPressReleases",
        "DepartmentofWildlifeandNationalParksJournal",
        "SelangorStateGovernmentPolicies",
        "DepartmentOfIrrigationAndDrainageAnnouncementsArchive",
        "DepartmentOfIrrigationAndDrainageAnnualReports",
        "MinistryOfHomeAffairsPressReleases",
        "MinistryOfHomeAffairsSpeeches",
        "MalaysiaDepartmentofInsolvencyStatistics",
        "DepartmentOfIrrigationAndDrainageNews",
        "LabuanFinancialServicesAuthorityPressReleases"
         "DepartmentofWildlifeAndNationalParksBulletin",
         "DepartmentofWildlifeAndNationalParksNewsClippings",
         "DepartmentofWildlifeAndNationalParksPlans",
         "EducationServiceCommissionNewsClippings",
         "EducationServiceCommissionPolicies",
         "MinistryofHealthPressReleaseArchive",
         "RegistrationofCompanyAnnualReports",
         "SelangorStateGovernmentClippingsArchive",
         "SelangorStateGovernmentNewsClippings",
         "SelangorStateGovernmentStateSecretarysCirculars",
         "SustainableEnergyDevelopmentAuthorityMalaysiaRoadmap",
         "DepartmentofWildlifeandNationalParksOtherPublications",
         "FederalTerritorySportsCouncilNewsClippings",
         "MalaysiaHighwayAuthoritAnnualReports",
         "MalaysiaHighwayAuthorityBulletin",
         "MalaysiaHighwayAuthorityPressReleases",
         "MalaysiaHighwayAuthorityAnnouncements",
         "MalaysiaHighwayAuthorityNews",
         "MalaysiaPetroleumResourcesCorporationPublications",
         "MalaysiaDepartmentofIslamicDevelopmentPublications",
         "MalaysiaDigitalEconomyCorporationNewsletters",
         "MalaysiaDepartmentofIslamicDevelopmentAnnouncements",
         "MalaysiaDepartmentofIslamicDevelopmentPressReleases",
         "MalaysiaDepartmentofIslamicDevelopmentGuidelines",
         "MalaysiaHighwayAuthorityNewsClippings",
         "MalaysiaDepartmentofInsolvencyStatistics",
         "MalaysiaDigitalEconomyCorporationInsights",
         "MalaysiaDigitalEconomyCorporationReports",
         "MalaysiaDigitalEconomyCorporationPressReleases",
         "LandPublicTransportAuthorityGuidelines",
         "MalaysiaHighwayAuthorityGuidelines",
         "MalaysiaPetroleumResourcesCorporationPressReleases"
         "MinistryofHealthPressReleaseArchivenews",
         "MalaysiaPetroleumResourcesCorporationPressReleases",
         "MalaysiaPetroleumResourcesCorporationPublications",
         "MalaysiaQualificationsAgencyMQAPressReleases",
         "MalaysiaQualificationsAgencyMQAPolicies",
         "MalaysiaQualificationsAgencyMQAReports",
        "KedahSyariahJuciaryDepartmentNews",
        "KedahSyariahJuciaryDepartmentNewsClippings",
        "KedahSyariahJuciaryDepartmentEnactment",
        "LangkawiDevelopmentAuthoritySpeeches",
        "LangkawiDevelopmentAuthorityAnnualReports",
        "LangkawiDevelopmentAuthorityNewsClippings",
        "LangkawiDevelopmentAuthorityPressReleases",
        "LangkawiMunicipalCouncilPublications",
        "MalaccaCustomaryLandsDevelopmentCorporationNews"
        "DepartmentofWildlifeAndNationalParksBulletin",
        "DepartmentofWildlifeAndNationalParksNewsClippings",
        "DepartmentofWildlifeAndNationalParksPlans",
        "EducationServiceCommissionNewsClippings",
        "EducationServiceCommissionPolicies",
        "MinistryofHealthPressReleaseArchive",
        "RegistrationofCompanyAnnualReports",
        "SelangorStateGovernmentClippingsArchive",
        "SelangorStateGovernmentNewsClippings",
        "SelangorStateGovernmentStateSecretarysCirculars",
        "SustainableEnergyDevelopmentAuthorityMalaysiaRoadmap",
        "LandPublicTransportAuthorityPublications",
        "LandAndSurveyDepartmentNewsClippings",
        "LabuanFinancialServicesAuthorityLegislation",
        "MalaysiaPetroleumResourcesCorporationPublications",
         "SustainableEnergyDevelopmentAuthorityMalaysiaRoadmap",
        "MalaysiaQualificationsAgencyNotificationLetters",
        "MalaysiaQualificationsAgencyCirculars",
        "MalaysiaQualificationsAgencyAnnualReports",
        "SustainableEnergyDevelopmentAuthorityMalaysiaRoadmap",
        "LangkawiMunicipalCouncilNews",
        "LegalAidDepartmentNewsClippings",
        "LegalAidDepartmentBulletin",
        "LabuanFinancialServicesAuthorityPressReleases",
        "LabuanFinancialServicesAuthorityPressReleases",
        "UDAHoldingsBerhadPressReleases",
        "RegistrationOfCompanyBulletin",
        "EnergyCommissionReports",
        "EnergyCommissionPressReleases",
        "EnergyCommissionBusinessPlan",
        "EnergyCommissionAnnualReports",
        "DepartmentOfWildlifeandNationalParksAnnualReports",
        "LegalAidDepartmentNews",
        "DepartmentOfIrrigationAndDrainageWaterResourcesPublication",
        "MinistryOfEducationBroadcastLetter"
        "RoadTransportDepartmentPublications",
        "PublicWorksDepartmentSarawakPolicies",
        "LegalAffairsDepartmentStrategicPlans",
        "LegalAffairsDepartmentAnnualReports",
        "LegalAffairsDepartmentBulletin",
        "LegalAffairsDepartmentSpeeches",
        "LegalAffairsDepartmentNewsClippings",
        "MalaccaCustomaryLandsDevelopmentCorporationStrategicPlan",
        "MalaccaCustomaryLandsDevelopmentCorporationDirectorsOrders",
        "MalaccaCustomaryLandsDevelopmentCorporationLaws"
        "LegalAffairsDepartmentPublications",
        "LegalAffairsDepartmentPressReleases",
        "MalaccaCustomaryLandsDevelopmentCorporationGazette",
        "MalaccaCustomaryLandsDevelopmentCorporationCirculars",
        "RoadTransportDepartmentAnnouncements",
        "MalaysiaCompetitionCommissionAnnualReports",
        "PublicWorksDepartmentMalaccaPolicies",
        "PublicWorksDepartmentMalaccaBulletin",
        "MalaysiaDepartmentofInsolvencyActs",
        "MalaysiaCompetitionCommissionAnnouncements",
        "MalaysiaCompetitionCommissionMediaReleases",
        "MalaysiaCompetitionCommissionNewsClippings",
        "MalaysiaCompetitionCommissionGuidelines"
        "LabuanFinancialServicesAuthorityLegislation",
        "LandPublicTransportAuthorityPressReleases",
        "MalaccaIslamicReligiousCouncilAnnouncements",
        "MalaccaIslamicReligiousCouncilAnnualReports",
        "MalaccaIslamicReligiousCouncilEnactments",
        "MalaccaIslamicReligiousCouncilNews",
        "MalaccaVeterinaryServicesDepartmentGuidelines",
        "MalaccaVeterinaryServicesDepartmentNewsClippings",
        "MalaccaVeterinaryServicesDepartmentStrategicPlans",
        "MalaysiaCivilDefenceForceCirculars",
        "PublicWorksDepartmentMalaccaNews",
        "PublicWorksDepartmentSarawakNews",
        "LegalAffairsDepartmentPublications",
        "LegalAffairsDepartmentPressReleases",
        "MalaccaCustomaryLandsDevelopmentCorporationGazette"
        "MalaccaCustomaryLandsDevelopmentCorporationCirculars",
        "MalaysiaDepartmentofInsolvencyStrategicPlans",
        "MalaysiaDepartmentofInsolvencyNewsClippings",
        "MalaysiaDepartmentofInsolvencyAnnualReports",
        "MalaysiaDepartmentofInsolvencyActs",
        "MalaysiaCompetitionCommissionPublications",
        "MalaysiaCompetitionCommissionGuidelines",
        "MalaysiaCompetitionCommissionMarketReview",
        "MalaysiaCompetitionCommissionNewsletters",
        "MalaysiaCompetitionCommissionInternationalPublications"
        ],
    "8": [
        "MinistryOfCultureSportsAndTourismVietnam",
        "MinistryOfHealthVietnam",
        "MinistryOfHomeAffairsVietnam",
        "GeneralDepartmentofTaxationVietnam",
        "TheStateBankOfVietnam",
        "TheGovernmentInspectorateofVietnam",
        "TheFinaceTimes",
        "AnGiangProvincesPortalVietnam",
        "NationalPoliticalPublishingHouse",
        "HoChiMinhNationalPoliticalAcademyVietnam",
        "VietnamGovernmentCommitteeforReligiousAffairsMinistryofHomeAffairs",
        "StateRecordsandArchivesDepartmentofVietnamMinistryofHomeAffairsVietnam",
        "LuatVietnamLegaldocumentsofVietnameselawsVietnam",
        "LegislativeResearchInstituteVietnam",
        "VietnamNewsVietnam",
        "VietNamGovernmentPortalHaNoiCapitalVietnam",
        "InternalAffairsDepartmentVietnam",
        "PublicSecurityNewsVietnam",
        "TheoreticalCouncilVietnam",
        "StandingCommitteeLLoftheNationalAssemblyVietnam",
        "LegalAndJudiciaryCommitteesVietnam",
        "TheFinaceTimes",
        "TheGovernmentOfficeOfVietnam",
        "EconomicDepartmentVietnam",
        "TheFinaceTimes",
        "EconomicDepartmentVietnam"
        "TheFinaceTimes"
        "TheFinaceTimes",
        "TheSaigonTimesWeeklyofVietnam",
        "LegislatureResearchReview",
        "StateManagementReview",
        "ExternalInformationReviewofVietnam",
        "ScienceandLifeNewspaper",
        "TheSportsCulturesNewspaper",
        "MinistryOfJusticeVietnam",
        "MinistryOfEthnicAndReligiousAffairsVietnam",
        "EconomicsandUrbanNewspaper",
        "MinistryOfConstructionVietnam"
        "VietnamInvestmentReview",
        "VietnamEconomicTimes",
        "PeoplesArmyNewspaperofVietnam",
        "TheEducationandTimesNewspaper",
        "BankingReviewofVietnam",
        "BankingTimes",
        "AuditingNewspaper",
        "JournalofAuditingStudiesofVietnam",
        "SaiGonGiaiPhongNewspaperofVietnam",
        "StateSecuritiesCommissionOfVietnam",
        "ForestProtectionDepartmentVietnam"
        "VnExpressofVietnam",
        "TuoiTreNewsofVietnam",
        "VietnamPlus",
        "VTCNews",
        "VietnamTelevisionOnlineNewspaper",
        "NhanDanPeopleNewspaperofVietnam",
        "TienPhongNewspaperofVietnam",
        "TheYouthNewspaperofVietnam",
        "HaNoiMoiNewspaperofVietnam"
        "StateSecuritiesCommissionOfVietnam"
        "AirForceandAirDefenceNewspaper",
        "VietnamWomenNewspaper",
        "VietnamEnergyOnline",
        "UrbanPeopleMagazine",
        "TheHealthandLifeNewspaper",
        "PublicSecurityNews",
        "ThePublicHealthNewspaper",
        "CapitalMilitaryDefenseNewspaper",
        "WorkersNewspaper",
        "EconomicsandUrbanNewspaper",
        "CustomsNews",
        "ForeignAffairsCommitteeofVietnam",
        "EconomicandFinancialCommitteeofVietnam",
        "NationalDefenseSecurityandForeignAffairsCommitteeofVietnam",
        "ScienceTechnologyandEnvironmentCommittee",
        "FinanceandBudgetCommittee",
        "CultureandSocialCommittee",
        "JudicalCommittee",
        "CentralPostOfficeDepartment",
        "VietnamNationalAuthorityOfTourism",
        "GeneralDepartmentofVietnamCustoms",
        "DirectorateforStandardsMetrologyandQuality",
        "VietnamAcademyofScienceandTechnology",
        "TheJusticeNewspaper",
        "VietnamAcademyofSocialSciences",
        "VietNamSocialSecurity",
        "CapitalMilitaryDefenseNewspaper",
        "CommunistReview",
        "VietnamLawandLegalForumMagazine",
        "StateAuditOfficeofVietnam",
        "TheProcuracyMagazineOfVietnam",
        "VietNamGovernmentPortalHoChiMinhcity",
        "BaRiaVungTauProvincesPortalOfVietnam",
        "HaNoiPortalofVietnam",
        "HaiPhongPortal",
        "BusinessandTradeReview",
        "ArchitectureMagazineofVietnamAssociationofArchitects",
        "DigitalWorldMagazine",
        "MotherlandNewspaper",
        "VietnamTimes",
        "VietnamAquacultureMagazine"
        "HoChiMinhCityPortal",
        "Vietnamnet",
        "StateSecuritiesCommissionOfVietnam",
        "TheBusinessReview"
        "MinistryOfConstructionVietnam"
        "VietnamAgricultureNewspaper",
        "HanoiTimesofvietname",
        "VietnamLaws",
        "TheFinancialInvestmentReviewVietnamFinance",
        "VietNamNationalAuthorityOfTourism",
        "VietnamLaws"
        "VietnamLaws",
        "HaNoiTimesofVietnam",
        "AirForceandAirDefenceNewspaper",
        "VietnamAgricultureNewspaper",
        "TheEconomicandFinancialInformationChannelofVietnam"

    ],
    "9" : [
        "CentralDrugsStandardControlOrganizationCENTRALVietnam",
        
    ]
}

def get_source_tier(name, source=None):
    for tier, sources in SOURCE_TIER.items():
        # this will handle cases spiders are split into multiple spiders
        # and we use the same source, to identify the tier
        if source in sources or name in sources:
            return tier
    raise Exception("Spider not listed in SOURCE_TIER, please update!") 


def extract_text(el):
    if el is None or "gsc-link-list" in el.get("class", ""):
        return ""
    text_parts = []
    if el.text:
        text_parts.append(el.text)
    for child in el:
        text_parts.append(extract_text(child))
        if child.tail:
            text_parts.append(child.tail)
    if el.tag == 'a':
        return f" {''.join(text_parts).strip()}({el.get('href', '')}) "
    text = ''.join(text_parts)
    if el.tag == 'p':
        text += '\n'
    return text