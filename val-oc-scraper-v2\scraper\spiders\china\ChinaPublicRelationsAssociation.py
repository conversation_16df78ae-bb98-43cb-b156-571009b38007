from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaPublicRelationsAssociation(OCSpider):
    name = "ChinaPublicRelationsAssociation"

    start_urls_names = {
        "http://www.cpra.org.cn/node_1008493.html": "associationUpdatesList",   # This start_url also contains child articles of different websites
        "http://www.cpra.org.cn/node_1008494.html": "industryTrendsList",
        "http://www.cpra.org.cn/node_1008495.html": "partyConstruction"
    }

    charset = "utf-8"

    # To include only the child articles that start with the home page URL ("http://www.cpra.org.cn/")
    include_rules = [r'^http://www\.cpra\.org\.cn/.*']

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = response.xpath("//div[@class='box1l']//h2")
        return articles
    
    def get_href(self, entry) -> str:
        return entry.xpath("./a/@href").get()
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        date_str = response.xpath("//div[@class='box1']//h3//text()")
        date = date_str.re_first(r"\d{4}-\d{2}-\d{2}")
        return date
    
    def get_title(self, response) -> str:  
        return response.xpath("//div[@class='box1']//h2//text()").get()
    
    def get_authors(self, response):
        return []
    
    def get_body(self,response) -> str:
        return body_normalization(response.xpath("//div[@class='box1']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='box1']//p//img/@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        current_page_number = response.xpath('//*[@id="autopage"]//span/text()').get()
        if current_page_number:
            current_page_index = int(current_page_number)
            next_page_xpath = f'//*[@id="autopage"]/center/a[{current_page_index}]/@href'
            next_page = response.xpath(next_page_xpath).get()
            if next_page:
                return next_page
        return None