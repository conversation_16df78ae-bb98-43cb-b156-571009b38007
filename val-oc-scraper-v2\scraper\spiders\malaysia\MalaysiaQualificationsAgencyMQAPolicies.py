from scraper.OCSpider import OCSpider
import re

class MalaysiaQualificationsAgencyMQAPolicies(OCSpider):
    name = "MalaysiaQualificationsAgencyMQAPolicies"

    start_urls_names = {
        "https://www.mqa.gov.my/new/pubs_compilationpolicies.cfm#gsc.tab=0": "Policies"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.mqa.gov.my/new/pubs_compilationpolicies.cfm#gsc.tab=0"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}

    MAX_ARTICLES_PER_BATCH = 500

    def get_articles(self, response) -> list:
        articles = []
        count = 0
        for row in response.xpath("//table[@class='table table-bordered table-striped']/tbody/tr"):
            if count >= self.MAX_ARTICLES_PER_BATCH:
                break
            url = row.xpath(".//td[3]//a/@href").get()
            title = row.xpath("./td[2]/text()").get()
            if not url or not title:
                continue
            title = re.sub(r'\s+', ' ', title.strip())
            if len(title) > 500:
                title = title[:500]
            url = response.urljoin(url.strip())
            year_match = re.search(r"\b(\d{4})(?:\s*-\s*(\d{4}))?\b", title)
            year = year_match.group(1) if year_match else ""
            self.article_data_map[url] = {
                "title": title,
                "date": year
            }
            articles.append(url)
            count += 1
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None