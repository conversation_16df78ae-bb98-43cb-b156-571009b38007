from scraper.OCSpider import OCSpider
import re

class CybersecurityMalaysiaAnnualReports(OCSpider):
    name = "CybersecurityMalaysiaAnnualReports"

    start_urls_names = {
        "https://www.cybersecurity.my/portal-main/document/annual-report": "Annual Report"
    }

    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles=[]
        for article in response.xpath("//section//div[@class='col-lg-9']//div[@class='document-wrapper']"): 
                url = article.xpath(".//a//@href").get()
                title=article.xpath(".//h6//text()").get()
                img =article.xpath(".//div[contains(@class, 'document-image')]/@style").get()
                date = title.split(" ")[-1]
                if url and title and img:
                    full_url = url
                    title= title.strip()
                    date= date.strip()
                    self.article_data_map[full_url]={"title": title, "date": date, "img": img}
                articles.append(full_url)
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")


    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        style = self.article_data_map.get(response.request.meta.get('entry'), {}).get("img", "")
        img = re.search(r'url\((.*?)\)', style).group(1) if style else []
        return img
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return response.xpath("//ul//li[@class='next']//a//@href").get()