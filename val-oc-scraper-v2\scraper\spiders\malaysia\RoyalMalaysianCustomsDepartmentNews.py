from scraper.OCSpider import OCSpider
import re
from scraper.middlewares import HeadlessBrowserProxy
from scraper.utils.helper import body_normalization
import scrapy

class RoyalMalaysianCustomsDepartmentNews(OCSpider):
    name = "RoyalMalaysianCustomsDepartmentNews"
    
    start_urls_names = {
        "https://www.customs.gov.my/ms/hl/Pages/SNews.aspx": "News"
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"    
    
    def get_articles(self, response) -> list:
        return response.xpath("//table[@class='table table-hover table-sm table-bordered dataTable no-footer']//a//@href").getall()

    def get_href(self, entry) -> str:
        return f"https://www.customs.gov.my{entry}"

    def get_title(self, response) -> str:
        return response.xpath("//h4//text()").get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div//p[@class='MsoNormal']//text() | //tr//div[contains(@class, 'External')]//@href | //tr//div[contains(@class, 'External')]").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> str:
        date_text = response.xpath("//p[contains(text(), 'Tarikh')]//text()").get()
        date = re.search(r'\d{2}/\d{2}/\d{4}', date_text).group() if date_text else None
        return date
    
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        # No next page to scrape
        return None