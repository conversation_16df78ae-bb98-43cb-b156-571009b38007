from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime 

class VermontDepartmentOfLabor(OCSpider):
    name = "VermontDepartmentOfLabor"

    country = "US"

    start_urls_names = {
        "https://labor.vermont.gov/news": "News",
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self): 
        return "English"

    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h2[@class='margin-0']/a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='margin-0']/span[@property='dc:title']/text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@property="content:encoded"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_str = response.xpath('//div[contains(@class, "field--name-field-date")]/text()').get()
        if date_str:
            date_str = date_str.strip()
            return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")
        return None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//nav[@class="usa-pagination"]//a[@class="usa-pagination__link usa-pagination__next-page"]/@href').get()
        return response.urljoin(next_page) if next_page else None