from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization
import urllib.parse
from datetime import datetime
class KansasDepartmentOfCommerce(OCSpider):
    name = 'KansasDepartmentOfCommerce'

    country = "US"

    HEADLESS_BROWSER_WAIT_TIME = 100

    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 1,
	}

    start_urls_names = {
        "https://www.kansascommerce.gov/news/": "News",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Central"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//a[@class='im-news-story']/@href").getall()
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='copy']//p//text() | //div[@class='copy']//ul//li//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='container margin-container']//img//@src").getall()

    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response) -> str:
        date_text = response.xpath("//p[@class='date']//text()").get()
        return date_text.strip()

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        #No Next page to scrape
        return None