from scraper.OCSpider import OCSpider

class MalaysiaCompetitionCommissionNewsletters(OCSpider):
    name = "MalaysiaCompetitionCommissionNewsletters"

    start_urls_names = {
        'https://www.mycc.gov.my/newsletter' : 'Newsleetters',
    }

    start_urls_with_no_pagination_set = {
        "https://www.mycc.gov.my/newsletter"  # Pagination is not suported
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('///div[@class="newsletter-wrapper"]')
        for row in rows:
            title = row.xpath('.//div[@class="newsletter-title"]//text()').get()
            pdf_link = row.xpath('.//div[@class="newsletter-pdf"]//a/@href').get()
            date = row.xpath('.//div[@class="newsletter-date"]//text()[position() mod 2 = 1]').get()
            if title and pdf_link and date:
                title = title.strip()
                pdf_link = response.urljoin(pdf_link.strip())
                self.article_title_pdf_mapping[pdf_link] = (title, pdf_link, date)
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
       return "%d %b %Y"

    def get_date(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[2]

    def get_images(self, response) -> list[str]:
        return []

    def get_document_urls(self, response, entry=None)->list:
        return [self.article_title_pdf_mapping.get(response.url, ("", "", ""))[1]]

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None
    