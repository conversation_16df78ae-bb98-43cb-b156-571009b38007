from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class HTSEC(OCSpider):
    name = "HTSEC"

    start_urls_names = {
        "https://www.htsec.com/ChannelHome/2191666/index.shtml" : "海通证券",
        "https://www.htsec.com/ChannelHome/20170915/index.shtml" : "海通证券", 
        "https://www.htsec.com/ChannelHome/73663/index.shtml" : "海通证券",
        "https://www.htsec.com/ChannelHome/2016102402/index.shtml":"海通证券",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "private_enterprise"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="main_list"]//ul//li/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="article"]//div[@class="data"]/text()').re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//div[@class="article"]//p//a[contains(@href,".pdf")]//@href | //div[@class="article"]//p//a[contains(@href,".doc")]//@href').getall()
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if response.status == 404:
            return None
        current_url = response.url
        if "index.shtml" in current_url:
            next_page_url = current_url.replace("index.shtml", "index_2.shtml")
        else:
            import re
            match = re.search(r"index_(\d+)\.shtml", current_url)
            if match:
                current_page_num = int(match.group(1))
                next_page_num = current_page_num + 1
                next_page_url = current_url.replace(
                    f"index_{current_page_num}.shtml",
                    f"index_{next_page_num}.shtml"
                )
            else:
                return None
        return next_page_url