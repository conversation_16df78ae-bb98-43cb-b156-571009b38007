from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import requests


class ChinaFisheriesAssociation(OCSpider):
    name = 'ChinaFisheriesAssociation'

    start_urls_names = {
        'http://www.china-cfa.org/xwzx/zxdt/': '最新动态 ', 
        'http://www.china-cfa.org/xwzx/xhdt/': '协会动态',
        'http://www.china-cfa.org/xwzx/xydt/': '行业动态 ',
        'http://www.china-cfa.org/tzgg/': '通知公告',
        'http://www.china-cfa.org/p/list.php?tid=251': '党建专题',
    }

    # To include only the child articles that start with the home page URL ("http://www.china-cfa.org/"),
    include_rules = [r'^http://www\.china-cfa\.org/.*']
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        relative_articles = response.xpath('//div[@class="list"]/ul//li/a/@href').getall()
        articles = [response.urljoin(article) for article in relative_articles]
        final_urls = []
        for article_url in articles:
            final_url = self.resolve_redirect(article_url)
            final_urls.append(final_url)
        return final_urls
    
    # Some child articles on the same domain redirect to external sites.  
    # To ensure we get the actual final URLs—whether redirected or not—without affecting the include/exclude rules.
    def resolve_redirect(self, url: str) -> str:
        try:
            response = requests.get(url, allow_redirects=True, timeout=10, verify=False)   # HTTP GET request to handle redirects
            return response.url if response else url
        except requests.RequestException as e:
            self.logger.error(f"Error fetching {url}: {e}")
            return url
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//div[@class="cheader"]//p/text()').get()
    
    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M:%S'
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@id="othermessage"]//p/span[contains(text(), "日期")]/text()').re_first(r'^日期：(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})')
    
    def get_authors(self, response):
        return response.xpath('//p[@class="fl"]/span[3]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="TRS_Editor"]//p//text()').getall())
    
    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class="TRS_Editor"]//p//img/@src').extract()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath('//ul[@class="pagination"]/li//a[contains(text(), "下一页")]/@href').get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:   # Condition to check if the next page is the same as the current page
                    self.logger.info(f"Next page resolves to the current page URL: {response.url}. Stopping pagination.")
                    return None   # Stop crawling
                else:
                    self.logger.info(f"Found next page: {next_page_url}")
                    return next_page_url
            else:
                self.logger.info("No next page found.")
                return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None