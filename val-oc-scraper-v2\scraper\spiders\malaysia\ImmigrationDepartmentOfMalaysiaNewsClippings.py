from scraper.OCSpider import OCSpider
import scrapy

class ImmigrationDepartmentOfMalaysiaNewsClippings(OCSpider):
    name = "ImmigrationDepartmentOfMalaysiaNewsClippings"

    start_urls_names = {
        "https://www.imi.gov.my/index.php/keratan-akhbar/" : "News"
    }

    start_urls_with_no_pagination_set = {
        "https://www.imi.gov.my/index.php/keratan-akhbar/"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def parse_intermediate(self, response):
        articles = response.xpath('//tbody//tr//td[3]//a//@href').getall()
        total_articles = len(articles)
        start_url = response.meta.get("start_url")
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'articles': articles, 
                        'start_url': start_url
                    },
                    dont_filter=True
            )

    def get_articles(self, response) -> list:  
        articles= response.xpath('//tbody//tr//td[3]//a//@href').getall()
        start_idx = response.meta.get('start_idx', 0) 
        end_idx = start_idx + 100       
        return articles[start_idx:end_idx]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//section[@class='has_eae_slider elementor-section elementor-top-section elementor-element elementor-element-fd26236 elementor-section-boxed elementor-section-height-default elementor-section-height-default']//div[@class='elementor-element elementor-element-42e9d5c elementor-widget elementor-widget-text-editor']//div[@class='elementor-widget-container']//text()").get()
    
    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='elementor-element elementor-element-ba4a4fb elementor-widget elementor-widget-image']//div[@class='elementor-widget-container']//img//@src").getall()
       
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> str:
        date_text = response.xpath("(//section[@class='has_eae_slider elementor-section elementor-top-section elementor-element elementor-element-fd26236 elementor-section-boxed elementor-section-height-default elementor-section-height-default']//div[@class='elementor-container elementor-column-gap-default']//div[@class='has_eae_slider elementor-column elementor-col-50 elementor-top-column elementor-element elementor-element-777c0b4']//div[@class='elementor-widget-wrap elementor-element-populated']//div[@class='elementor-widget-container'])[7]//text()").get()
        if date_text:
            return date_text.strip()  
        return ""

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None