from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization
from typing import Optional
import dateparser
import re

class DepartmentOfOccupationalSafetyAndHealthSpeeches(OCSpider):
    name = "DepartmentOfOccupationalSafetyAndHealthSpeeches"
    
    start_urls_names = {
        "https://dosh.gov.my/media/teks-ucapan-pengetua-pengarah/": "Text of the Director General's Speech",
    }

    start_urls_with_no_pagination_set = {
        "https://dosh.gov.my/media/teks-ucapan-pengetua-pengarah/"
    }

    country= "Malaysia"

    charset = "utf-8"

    api_start_urls = {
        "https://dosh.gov.my/media/teks-ucapan-pengetua-pengarah/": { 
            "url":"https://dosh.gov.my/media/teks-ucapan-pengetua-pengarah/"
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page", 1)      
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"].format(current_page=current_page)
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers={
                "Content-Type": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;",
            },
            dont_filter=True,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "current_page": current_page,
            },
        )

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    article_date_map = {}

    def get_articles(self, response) -> list:
        articles = response.xpath("//div[contains(@class, 'kt-accordion') and contains(@class, 'kt-accordion-pane') and not(ancestor::div[contains(@class, 'kt-accordion-pane')])]")
        return articles

    def get_href(self, entry) -> str:
        index_raw = entry.xpath("count(preceding-sibling::div[contains(@class, 'kt-accordion-pane')])").get()
        index = int(float(index_raw))  # Fix the conversion issue here
        return f"?article_index={index}"

    def get_title(self, article) -> str:
        return article.xpath(".//span[contains(@class, 'kt-blocks-accordion-title')]/text()").get()

    def get_body(self, article) -> str:
        texts = article.xpath(".//div[contains(@class, 'kt-accordion-panel-inner')]//text()[not(ancestor::script) and not(ancestor::style)]").getall()
        return body_normalization(texts)

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d-%m-%Y"

    def get_date(self, article) -> Optional[str]:
        text = " ".join(article.xpath(".//text()").getall()).strip()
        match = re.search(r"\b\d{1,2} \w+ \d{4}\b", text)
        if match:
            raw_date = match.group(0)
            parsed = dateparser.parse(raw_date, languages=["ms"])
            if parsed:
                formatted = parsed.strftime("%d-%m-%Y")
                return formatted
        return None

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None