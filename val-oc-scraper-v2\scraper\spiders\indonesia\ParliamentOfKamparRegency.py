from datetime import datetime
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfKamparRegency(OCSpider):
    name = "ParliamentOfKamparRegency"
    
    start_urls_names = {
        #"https://dprd.kamparkab.go.id/dokumenkat/keputusan-dprd": "Keputusan DPRD", # No date is present on any article
        "https://dprd.kamparkab.go.id/artikelkat/berita":"Berita"
    }

    charset = "utf-8"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h2/a/@href").getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h2/a/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "entry-content"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        MONTH_MAP = {
            'Januari': 'January',
            'Februari': 'February',
            'Maret': 'March',
            'April': 'April',
            'Mei': 'May',
            'Juni': 'June',
            'Juli': 'July',
            'Agustus': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Desember': 'December'
        }
        raw_date = response.xpath('//div[@class= "entry-meta"]//ul//li//a/time/text()').get()
        if not raw_date:
            return None
        raw_date = raw_date.strip()
        raw_date = raw_date.replace("WIB", "").strip()
        for indo_month, eng_month in MONTH_MAP.items():
            if indo_month in raw_date:
                raw_date = raw_date.replace(indo_month, eng_month)
                break
        try:
            date_obj = datetime.strptime(raw_date, "%d %B %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError as e:
            raise e
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath('//a[@rel="next"]/@href').get()