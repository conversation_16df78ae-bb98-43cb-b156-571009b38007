from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import Optional

class NebraskaDepartmentOfLabor(OCSpider):
    name = "NebraskaDepartmentOfLabor"
    
    country = "US"

    start_urls_names = {
        "https://dol.nebraska.gov/PressRelease": "Newsroom",
    }

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 6
    }
    
    charset = "utf-8"
    
    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='panel-heading']//a//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='newsTitle']//h2//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='newsContent']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m/%d/%Y "
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='formSeparatorLine']//strong/following-sibling::text()[1]").get()
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None