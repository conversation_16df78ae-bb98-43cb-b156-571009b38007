from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfNorthTorajaRegency(OCSpider):
    name = "ParliamentOfNorthTorajaRegency"

    start_urls_names = {
        "https://dprdtorut.com/public/berita" : "News"
    }
    
    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Makassar"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='card-body']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h2//text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='card-body']//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='col-md-9']//img//@src").getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d %H:%M:%S"
    
    def get_date(self, response) -> str:
        return response.xpath("//span[@class='d-block p-2']//figcaption//text()").get().strip()
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//ul[@class='pagination']//li//a[@rel='next']//@href").get()