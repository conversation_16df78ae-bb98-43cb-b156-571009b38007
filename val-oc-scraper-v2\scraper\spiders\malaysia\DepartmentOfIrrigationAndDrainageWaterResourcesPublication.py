from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import unquote, urljoin
import re
import requests

class DepartmentOfIrrigationAndDrainageWaterResourcesPublication(OCSpider):
    name = "DepartmentOfIrrigationAndDrainageWaterResourcesPublication"

    start_urls_names = {
       "https://www.water.gov.my/index.php/pages/view/2134?mid=695": "Annual Report"
    }

    start_urls_with_no_pagination_set = {
        "https://www.water.gov.my/index.php/pages/view/2134?mid=695"
    }

    charset = "iso-8859-1"
    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(
            hbp.get_proxy(response.url, timeout=10000),
            callback=self.parse
        )
        request.meta['start_url'] = response.request.meta.get('start_url', response.url)
        yield request

    def get_articles(self, response) -> list:
        mapping = {}
        articles = []
        base_url = 'https://www.water.gov.my'
        rows = response.xpath('//div[@class="product"]')
        for row in rows:
            title = row.xpath('./@title').get()
            year_match = re.search(r'\b(20\d{2}|19\d{2})\b', title or '')
            date = year_match.group(0) if year_match else None
            article_url = row.xpath('./@data-url').get()
            if title and date and article_url:
                article_url = urljoin(base_url, article_url)
                if article_url.lower().startswith("http") and self.is_url_ok(article_url):
                    normalized_link = unquote(article_url).rstrip('/')
                    articles.append(normalized_link)
                    mapping[normalized_link] = {
                        'title': title.strip(),
                        'pdf': normalized_link if normalized_link.lower().endswith(".pdf") else None,
                        'date': date
                    }
        self.article_to_pdf_mapping.update(mapping)
        return list(set(articles))

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")

    def get_body(self, response) -> str:
        return ''

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get('date', '')

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        normalized_url = unquote(response.url).rstrip('/')
        pdf_url = self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
        if pdf_url and pdf_url.lower().startswith("http"):
            return [pdf_url]
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None
    
    def is_url_ok(self, url):
        try:
            r = requests.head(url, allow_redirects=True, timeout=10)
            return r.status_code == 200
        except requests.RequestException:
            return False