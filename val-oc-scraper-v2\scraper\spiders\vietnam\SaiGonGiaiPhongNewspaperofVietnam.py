
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import Optional
import scrapy
import json
from urllib.parse import urljoin
from datetime import datetime

class SaiGonGiaiPhongNewspaperofVietnam(OCSpider):
    name = "SaiGonGiaiPhongNewspaperofVietnam"

    start_urls_names = {
        "https://www.sggp.org.vn/xahoi/": "News",
        }
    
    api_start_url = {
        "https://www.sggp.org.vn/xahoi/": 
            "https://api.sggp.org.vn/api/morenews-zone-199-{page}.html?show_author=1&phrase=",
        
    }

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_url = self.api_start_url.get(start_url)
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        current_page = response.meta.get("current_page", 1)
        url = api_url.format(page=current_page)
        yield scrapy.Request(
            url=url,
            method="GET",
            dont_filter=True,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "current_page": current_page
            },
        )
        
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
        try :
            base_url = "https://www.sggp.org.vn/"
            data = json.loads(response.text)
            articles =[]
            map = {}
            for item in data.get("data",{}).get("contents",[]) :
               url = item.get("url")
               date_part = item.get("date")
               if date_part :
                   date = datetime.fromtimestamp(date_part).date().strftime("%Y-%m-%d")  
               title = item.get("title")
               if url and title and date:
                   full_url = urljoin(base_url,url)
                   articles.append(full_url)
                   map[full_url] = {
                       'title' : title,
                       'date' : date
                   }
            self.article_data_map.update(map)
            return articles
        except Exception as e:
            return
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.url,{}).get("title","")
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article"]//p//text()').getall())
   
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return self.article_data_map.get(response.url,{}).get("date","")
    
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None)->list:
        return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return str(int(response.meta.get("current_page")) + 1)

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        start_url = response.meta.get('start_url')
        api_url = self.api_start_url.get(start_url)
        if not api_url:
           self.logger.error("API url not found for start_url")
           return
        next_page = self.get_next_page(response)
        if next_page:
            url = api_url.format(page=next_page)
            yield scrapy.Request(
                url=url,
                method="GET",
                dont_filter=True,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "current_page": next_page
                },
            )
            