import json
import scrapy
from scraper.OCSpider import OCSpider
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
load_dotenv()

class ChinaAirTransportAssociation(OCSpider):
    name = "ChinaAirTransportAssociation"

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    start_urls_names = {
        'https://www.cata.org.cn/portal/content/content-list/xwzx/xhdt' : '新闻中心',
        'https://www.cata.org.cn/portal/content/content-list/tzgg' : '通知公告',
        'https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx' : '研究园地'  # Only pdf are present in start URL
    }

    charset = "iso-8859-1"

    api_start_urls = {
        'https://www.cata.org.cn/portal/content/content-list/xwzx/xhdt': {
            "url": "https://www.cata.org.cn/portal/content/contenList",
            "payload": {
                "pageSize": "15",
                "pageNum": "1",
                "contentType": "xhdt",
                "isAsc": "asc",
                "orderByColumn": "",
                "contentTitle": ""
            },
        },
        'https://www.cata.org.cn/portal/content/content-list/tzgg': {
            "url": "https://www.cata.org.cn/portal/content/contenList",
            "payload": {
                "pageSize": "15",
                "pageNum": "1",
                "contentType": "tzgg",
                "isAsc": "asc",
                "orderByColumn": "",
                "contentTitle": ""
            },
        },
        'https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx': {
            "url": "https://www.cata.org.cn/portal/content/contenList",
            "payload": {
                "pageSize": "15",
                "pageNum": "1",
                "orderByColumn": "",
                "isAsc": "asc",
                "contentTitle": "",
                "contentType": "hxzx"
            },
        }
    }

    article_data_map = {}

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload["pageNum"]
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            formdata=payload,
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
        )

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        start_url = response.meta.get("start_url")
        if start_url == "https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx":
            try:
                data = json.loads(response.text)
                article_urls = []
                for item in data.get("rows", []):
                    article_id = item.get("id")
                    title = item.get("contentTitle")
                    date = item.get("contentDate")
                    if article_id and title and date:
                        full_url = f"https://www.cata.org.cn/portal/content/download/document_0/{article_id}"
                        self.article_data_map[full_url] = {
                            "title": title,
                            "date": date,
                            "pdf": [full_url]
                        }
                        article_urls.append(full_url)
                return article_urls
            except Exception as e:
                self.logger.error(f"Error processing API response: {e}")
                return []
        else:
            try:
                data = json.loads(response.text)
                articles = data.get("rows", [])
                start_url = response.meta.get("start_url")
                article_urls = [
                    self.construct_article_url(article, start_url)  # Pass start_url to construct_article_url
                    for article in articles
                    if article
                ]
                return self.get_proxy_articles(article_urls)
            except json.JSONDecodeError as e:
                self.logger.error(f"Error decoding JSON: {e}")
                return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response):
        start_url = response.meta.get("start_url")
        # If start is https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx than scraping title through start URL
        if start_url == "https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx":
            return self.article_data_map[response.request.meta.get('entry')].get("title")
        else:
            return response.xpath('//div[@class="col-sm-12"]/div[@id="contentTitleDiv"]//text()').get()
    
    def get_body(self, response):
        start_url = response.meta.get("start_url")
        # If start is https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx than body should be none as Child article has PDF only
        if start_url == "https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx":
            return ""
        else:
            return body_normalization(response.xpath('//div[@id="contentContDiv"]//p//text()').getall())
    
    def get_images(self, response):
        start_url = response.meta.get("start_url")
        # If start is https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx than image should be none as Child article has PDF only
        if start_url == "https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx":
            return []
        else:
            return response.xpath('//div[@id="app"]//div[@class="cont"]/p//img/@src').getall()
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response):
        start_url = response.meta.get("start_url")
        # If start is https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx than scraping date through start URL
        if start_url == "https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx":
            return self.article_data_map[response.request.meta.get('entry')].get("date")
        else:
            return response.xpath('//div[@id="contentDateDiv"]//text()').re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        start_url = response.meta.get("start_url")
        if start_url == 'https://www.cata.org.cn/portal/content/content-list/yjyd/hxzx':
            return self.article_data_map[response.request.meta.get('entry')].get("pdf")
        else:
            return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        current_page = int(response.meta.get("current_page"))
        total_articles = response.json().get("total")
        payload = response.meta.get("payload")
        page_size = int(payload.get("pageSize"))
        max_pages = int((total_articles / page_size) + 1)
        if current_page < max_pages:
            current_page = current_page + 1
            return str(current_page)
        else:
            return None
    
    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        next_page = self.get_next_page(response)
        payload["pageNum"] = next_page
        if next_page:
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded;"},
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")
    
    def construct_article_url(self, article, start_url):
        article_id = article.get('id')
        if start_url == "https://www.cata.org.cn/portal/content/content-list/xwzx/xhdt":
            return f"https://www.cata.org.cn/portal/content/show-content/{article_id}/xwzx"
        elif start_url == "https://www.cata.org.cn/portal/content/content-list/tzgg":
            return f"https://www.cata.org.cn/portal/content/show-content/{article_id}/tzgg"
    
    def get_proxy_articles(self, articles):
        try:
            hbp = HeadlessBrowserProxy()
            # Article URLs to include the proxy
            proxy_urls = [hbp.get_proxy(url, timeout = 30000) for url in articles]
            return proxy_urls
        except Exception as e:
            self.logger.error(f"Failed to fetch proxy articles: {e}")
            return []