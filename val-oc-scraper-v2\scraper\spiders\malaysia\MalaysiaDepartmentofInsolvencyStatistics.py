from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import time
import re

class MalaysiaDepartmentofInsolvencyStatistics(OCSpider):
    name = "MalaysiaDepartmentofInsolvencyStatistics"

    start_urls_names = {
        "https://www.mdi.gov.my/statistik/": "News"
    }

    charset = "iso-8859-1"
    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # ✅ Initialize Selenium driver
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Comment if you want GUI
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        self.driver = webdriver.Chrome(service=Service(), options=chrome_options)

    def closed(self, reason):
        # ✅ Clean up Selenium driver
        if hasattr(self, 'driver'):
            self.driver.quit()

    def get_articles(self, response) -> list:
        year = "2025"
        articles = []

        try:
            # ✅ Open URL with Selenium
            self.driver.get(response.url)
            time.sleep(3)  # Let page load

            # ✅ Click the Statistik Kebankrapan 2025 button
            xpath = (
                f'//a[contains(@href, "popup") and contains(text(), "Statistik Kebankrapan {year}")]'
                f' | //div[@data-ha-element-link and .//h2[contains(text(), "Statistik Kebankrapan {year}")]]'
            )

            wait = WebDriverWait(self.driver, 10)
            element = wait.until(EC.element_to_be_clickable((By.XPATH, xpath)))
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(1)
            element.click()
            time.sleep(2)  # Wait for popup to load

        except Exception as e:
            print(f"❌ Failed to open popup for {year}: {e}")

        # ✅ Proceed with Scrapy response extraction (as you had)
        for article in response.xpath('//div[@class="e-n-tabs-content"]'):
            link = article.xpath(".//a//@href").get()
            title = article.xpath('.//a//text()').get()
            if link:
                articles.append(link)
                self.article_data_map[link] = {
                    "title": title,
                    "link": link
                }

        print(self.article_data_map)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        year = "2025"
        parsed_date = datetime.strptime(year, "%Y")
        formatted_date = parsed_date.strftime("%Y")
        return formatted_date

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None