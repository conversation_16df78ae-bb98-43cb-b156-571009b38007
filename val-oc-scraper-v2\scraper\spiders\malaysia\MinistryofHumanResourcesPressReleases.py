from typing import Optional
from scraper.OCSpider import OCSpider
import re
from datetime import datetime
import scrapy
import html

class MinistryofHumanResourcesPressReleases(OCSpider):
    name = "MinistryofHumanResourcesPressReleases"

    start_urls_names = {
        "https://www.mohr.gov.my/index.php/media-berita/kenyataan-media": "News"
    }

    start_urls_with_no_pagination_set = {
        "https://www.mohr.gov.my/index.php/media-berita/kenyataan-media"
    }

    charset = "iso-8859-1"
    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_data_map = {}

    def parse_intermediate(self, response):
        all_articles = response.xpath("//div[@class='sppb-addon-content']//li").getall()
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)

        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx,
                    'articles': all_articles,
                    'start_url': start_url
                },
                dont_filter=True
            )

    def get_articles(self, response) -> list:
        articles = []
        try:
            for link in response.xpath("//div[@class='sppb-addon-content']//li"):
                url = link.xpath(".//a[contains(@href, '.pdf')]//@href").get()
                title = link.xpath(".//a//text()").get()
                if url and title:
                    if "%202(" in url:
                        print(f"⚠️ Skipping known 404 URL pattern: {url}")
                        continue
                    full_url = response.urljoin(url.strip())
                    cleaned_title = html.unescape(title).strip()
                    self.article_data_map[full_url] = {"title": cleaned_title}
                    articles.append(full_url)
            start_idx = response.meta.get('start_idx', 0)
            end_idx = start_idx + 100
            return articles[start_idx:end_idx]

        except Exception as e:
            self.logger.error(f"Error in get_articles: {e}")
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d.%m.%Y"

    def get_date(self, response):
        url = response.url
        title = self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        print(f"\n🔍 Processing title for URL: {url}")
        print(f"Title: {title}")
        if not title:
            print(f"⚠️ Title is missing for URL: {url}")
            return None
    # Match date in parentheses, allowing optional space before ')'
        match = re.search(r"\((\d{1,2}\.\d{1,2}\.\d{4})\s*\)", title)
        if match:
            date_str = match.group(1)
            print(f"✅ Extracted date: {date_str}")
            return date_str
    # Try matching date ranges (e.g., (24.03.2024 - 27.03.2024))
        match = re.search(r"\((\d{1,2}\.\d{1,2}\.\d{4})\s*-\s*\d{1,2}\.\d{1,2}\.\d{4}\)", title)
        if match:
            date_str = match.group(1)
            print(f"✅ Extracted first date from range: {date_str}")
            return date_str
        print(f"❌ No date found in title: {title}")
        return None

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return None