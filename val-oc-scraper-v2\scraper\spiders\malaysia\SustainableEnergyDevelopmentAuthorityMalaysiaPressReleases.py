from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class SustainableEnergyDevelopmentAuthorityMalaysiaPressReleases(OCSpider):
    name = "SustainableEnergyDevelopmentAuthorityMalaysiaPressReleases"
    
    start_urls_names = {
        "https://www.seda.gov.my/media/press-release/": "Press Release"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.seda.gov.my/media/press-release/"
    }

    charset = "utf-8"
    
    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        return response.xpath('//article[contains(@class, "post")]//h3[@class="entry-title"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="page-title-inner-wrap"]/h1[@class="dfd-page-title"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content"]/p//text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> Optional[str]:
        return response.xpath('//span[contains(@class, "entry-date")]/text()').get()

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None