from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
class ChongqingRuralCommercialBank(OCSpider):
    name = "ChongqingRuralCommercialBank"

    start_urls_names = {
        "https://www.cqrcb.com/cqrcb/aboutus/presscenter/bankannounce/index.html": "渝农商行",
        "https://www.cqrcb.com/cqrcb/aboutus/presscenter/ourdynamic/index.html": "渝农商行",
        "https://www.cqrcb.com/cqrcb/investorRelations/investdisclosure/agnotice/aybgg/index.html": "渝农商行",
        "https://www.cqrcb.com/cqrcb/investorRelations/investdisclosure/agnotice/agddh/index.html": "渝农商行",
        "https://www.cqrcb.com/cqrcb/investorRelations/investdisclosure/hgnotice/hybgg/index.html": "渝农商行",
        "https://www.cqrcb.com/cqrcb/investorRelations/investdisclosure/hgnotice/hgddh/index.html": "渝农商行",
        "https://www.cqrcb.com/cqrcb/investorRelations/report/annualreport/index.html": "渝农商行",
        "https://www.cqrcb.com/cqrcb/investorRelations/report/interimreport/index.html": "渝农商行",
        "https://www.cqrcb.com/cqrcb/investorRelations/report/quarterlyreport/index.html": "渝农商行",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='sideCont']//ul//a//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h1//text()').get().strip()
    
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath("//div[@class='sideCont']//p//text()").getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d "
    
    def get_date(self, response, entry=None) -> int:
        return response.xpath("//div[@class='sideCont']//p//span//text()").get()
        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf = response.xpath("//div[@class='sideCont']//p//a[contains(@href, '.pdf')]/@href").get()
        return f'https://www.cqrcb.com{pdf}'
    
    def get_images(self, response, entry=None) -> List[str]:
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='pages']//a[contains(text(),'下一页')]//@href").get()
        if next_page:
            return next_page
        else:
            return None    