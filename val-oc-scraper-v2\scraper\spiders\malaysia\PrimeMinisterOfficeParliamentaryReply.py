from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class PrimeMinisterOfficeParliamentaryReply(OCSpider):
    name = "PrimeMinisterOfficeParliamentaryReply"

    start_urls_names = {
        "https://www.pmo.gov.my/parliamentary-reply/" : "Parliamentary Reply"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def get_articles(self, response) -> list:  
        return response.xpath('//td[@class="dtr-control"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="entry-title"]//text()').get()
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return "" 
    
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//time//text()').get()

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        return response.xpath('//div[@class="wp-block-file"]//a/@href').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to crawl
        return None