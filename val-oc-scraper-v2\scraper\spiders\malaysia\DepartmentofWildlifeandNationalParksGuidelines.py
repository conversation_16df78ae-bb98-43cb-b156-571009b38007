from scraper.OCSpider import OCSpider
import dateparser

class DepartmentOfWildlifeAndNationalParksGuidelines(OCSpider):
    name = "DepartmentOfWildlifeAndNationalParksGuidelines"

    start_urls_names = {
        "https://www.wildlife.gov.my/index.php/penerbitan/105-garis-panduan": "News"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.wildlife.gov.my/index.php/penerbitan/105-garis-panduan"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:
       articles= []
       for article in response.xpath('//*[@id="tm-content"]/article/table//tbody//tr'):
           link = article.xpath(".//td[2]//a//@href").get()
           title = article.xpath('.//td[2]//a//text()').get()
           date= response.xpath('////*[@id="tm-content"]/article/p[1]/time//text()').get()
           if link:
               articles.append(link)
               self.article_data_map[link]={
                    "title":title,"link":link,"date":date
                }
       return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        parsed_date = dateparser.parse(date, languages=['ms','en'])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("link", "")
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None 