from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List, Union, Optional
from datetime import datetime
import re

class NewJerseyEconomicDevelopmentAuthority(OCSpider):
    name = "NewJerseyEconomicDevelopmentAuthority"

    country = "US"

    start_urls_names = {
        "https://www.njeda.gov/press-room/": "Press Releases"
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    charset = "utf-8"

    @property
    def language(self) -> str:
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"

    def get_articles(self, response) -> List[str]:
        return response.xpath("//a[contains(text(), 'Read More')]/@href").getall()

    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) -> Union[str, None]:
        title= response.xpath("//h1//text()").get()
        return title.strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//p//text()").getall())

    def get_images(self, response) -> List[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        date_text = response.xpath("//div[@class='text-blue-100'][1]/text()").get()
        return date_text.strip() if date_text else datetime.today().strftime(self.date_format())

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if not self.get_articles(response):
            return None
        current_url = response.url
        match = re.search(r'/page/(\d+)/', current_url)
        current_page = int(match.group(1)) if match else 1
        next_page = current_page + 1
        next_page_url = f"https://www.njeda.gov/press-room/page/{next_page}/"
        return next_page_url