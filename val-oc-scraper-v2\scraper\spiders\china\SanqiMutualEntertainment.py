from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
import json

class SanqiMutualEntertainment(OCSpider):
    name = "SanqiMutualEntertainment"

    start_urls_names = {
        "https://www.37wan.net/investor.html": "三七互娱",
        "https://www.37wan.net/investor.html#financial" : "三七互娱",
        "https://www.37wan.net/investor.html#notice" : "三七互娱",
        "https://www.37wan.net/media.html" : "三七互娱"
    }

    api_start_urls = {
        "https://www.37wan.net/media.html": {
            "url": "https://www.37wan.net/api/web/news/companyNewsList?year={current_year}",}}

    charset="iso-8859-1"

    article_to_date_mapping = {}  # Mapping title, date and PDF with child articles from start URL

    def parse_intermediate(self, response):
        start_url = response.request.meta['start_url']
        api_data = self.api_start_urls.get(start_url)
        if api_data:
            current_year = response.meta.get("current_year", datetime.now().year)
            api_url=api_data.get("url")
            url = api_url.format(current_year=current_year)
            yield scrapy.Request(
                url = url,
                method = "GET",
                dont_filter=True,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": url,
                    "current_year": current_year,
                    }
            )
        else :
            hbp = HeadlessBrowserProxy()
            request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
            )
            request.meta['start_url'] = response.request.meta['start_url']
            yield request

    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        relative_articles = response.xpath('//div[@class="month-info"]//a/@href').getall()
        if relative_articles :
              self.article_date_mapping(response)
              articles = [response.urljoin(article) for article in relative_articles]
              return articles
        else:
            hbp = HeadlessBrowserProxy()
            data =json.loads(response.text)
            article_urls =[]
            data_list = data.get("data", {}).get("list", {})
            for articles in data_list.values():
                for article in articles:
                    id = article.get("id")
                    url =hbp.get_proxy(f"https://www.37wan.net/news.html?id={id}",timeout=30000)
                    article_urls.append(url)
            return article_urls

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        try:
            mapping_entry = self.article_to_date_mapping.get(response.url)
            if mapping_entry:
                title = mapping_entry.get("title")
            return title
        except Exception as e:
            pass
        try:
            title = response.xpath('//div[@class="title"]//text()').get()
            if title:
                return title
        except Exception as e:
            pass
    
    def get_body(self, response) -> str:
        try :
            body = body_normalization(response.xpath('//div[@class="detail-content"]//p//text()').getall())
            if body:
                return body
        except Exception as e:  
            pass
        return ""
    
    def get_images(self, response) -> list:
        try :
            images = response.xpath('//div[@class="detail-content"]//img/@src').getall()
            if images:
                return images
        except Exception as e:  
            pass
        return ""
    
    def date_format(self) -> str:
        return "%Y.%m.%d"
    
    def get_date(self, response) -> str:
        try :
            date = response.xpath('//div[@id="date"]//text()').get()
            if date:
                dateobj = datetime.strptime(date, "%Y-%m-%d")
                date = dateobj.strftime("%Y.%m.%d")
        except Exception as e:
            pass
        try :
            mapping_entry = self.article_to_date_mapping.get(response.url)
            if mapping_entry:
                date = mapping_entry.get("date")
                print(date)
        except Exception as e:
            pass
        return date
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None)->list :
        try:
            mapping_entry = self.article_to_date_mapping.get(response.url)
            if mapping_entry:
                pdf_urls = mapping_entry.get("pdf_urls")
            if pdf_urls:
                return [pdf_urls]
            else:
                return None
        except Exception as e:
            pass
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        current_year =str(int(response.meta.get("current_year"))-1)
        print(current_year)
        if current_year:
            if response.status!=200:
                return None
            return current_year
        else:       
            return None
    
    def go_to_next_page(self, response, start_url, current_page=None):
        api_url = response.meta.get("api_url")
        if api_url:
            next_page = self.get_next_page(response)
            if next_page:
                yield scrapy.Request(
                    url=api_url.format(current_year=next_page),
                    method="GET",
                    dont_filter=True,
                    callback=self.parse_intermediate,
                    meta={
                        "start_url": start_url,
                        "api_url": api_url,
                        "current_year": next_page,
                    },
                )

    def article_date_mapping(self, response):
        mapping={}
        year_blocks = response.xpath('//div[contains(@class, "year-wrap")]')
        for year_block in year_blocks:
            year = year_block.xpath('.//p[@class="year-info"]/text()').get()
            if not year:
                continue
            entries = year_block.xpath('.//div[@class="month-info"]')
            for entry in entries:
                url = entry.xpath('.//a/@href').get()
                title = entry.xpath('.//span[3]//text()').get(default="").strip()
                date_part = entry.xpath('.//span[1]//text()').get(default="").strip()  # MM.DD
                if date_part and year:
                    try:
                        full_date = f"{year}.{date_part}"  # e.g., "2025-01-22"
                    except Exception as e:
                        full_date = None
                        self.logger.warning(f"Date parsing failed for: {date_part}, error: {e}")
                else:
                    full_date = None
                    self.logger.debug(f"Missing date or year for URL: {url}, entry content: {entry.get()}")
                full_url = response.urljoin(url)
                mapping[full_url] = {
                    "title": title,
                    "date": full_date,
                    "pdf_urls": full_url
                }
        self.article_to_date_mapping.update(mapping)            