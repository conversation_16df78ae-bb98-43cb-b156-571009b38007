from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MinistryOfFinanceSpeeches(OCSpider):
    name = "MinistryOfFinanceSpeeches"

    start_urls_names = {
        "https://mof.gov.my/portal/ms/arkib3/ucapan": "News"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
     
    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="page-header"]//h2//a//@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//*[@id="sp-title"]/div/div/div/h2/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//*[@id="sp-component"]/div/div[2]/div/p/text() |  //*[@id="sp-component"]/div/div[2]/div[6]/ol/li/text()').getall())

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//span[@class="published"]//time//text()').get()
        parsed_date = dateparser.parse(date, languages=['ms'])
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        next_page = response.xpath('//a[@aria-label="Go to seterusnya page"]//@href').get()
        if  next_page:
            return next_page
        return None 