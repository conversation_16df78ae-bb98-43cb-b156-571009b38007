from datetime import datetime
from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfKaimanaRegency(OCSpider):
    name = "ParliamentOfKaimanaRegency"

    start_urls_names = {
        "https://dprd.kaimanakab.go.id/berita/": "Berita",
        # "https://dprd.kaimanakab.go.id/produk-hukum-3/": "",  # no articles to scrape with date
    }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    charset = "utf-8"
    
    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="elementor-widget-container"]//h1//a/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "elementor-widget-container"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> str:
        MONTH_MAP = {
            'Januari': 'January',
            'Februari': 'February',
            'Maret': 'March',
            'April': 'April',
            'Mei': 'May',
            'Juni': 'June',
            'Juli': 'July',
            'Agustus': 'August',
            'September': 'September',
            'Oktober': 'October',
            'November': 'November',
            'Desember': 'December'
        }
        raw_date = response.xpath('//span[@class="elementor-icon-list-text elementor-post-info__item elementor-post-info__item--type-date"]//text()').get()
        if not raw_date:
            return None
        raw_date = raw_date.strip()
        for indo_month, eng_month in MONTH_MAP.items():
            if indo_month in raw_date:
                raw_date = raw_date.replace(indo_month, eng_month)
                break
        try:
            date_obj = datetime.strptime(raw_date, "%B %d, %Y")
            return date_obj.strftime("%Y-%m-%d")
        except ValueError as e:
            raise e
    
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class= "elementor-widget-container"]//figure//img/@src').getall() 
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page =  response.xpath('//nav[@class= "elementor-pagination"]//a[@class="page-numbers"]/@href').get() 
        if next_page :
            return next_page
        return None