from typing import Optional
from scraper.OCSpider import OCSpider
from urllib.parse import unquote
import re

class AlaskaDepartmentOfCommerceCommunityAndEconomicDevelopment(OCSpider):
    name = "AlaskaDepartmentOfCommerceCommunityAndEconomicDevelopment"

    country = "US"

    start_urls_names = {
        "https://www.commerce.alaska.gov/web/": "Press Releases",
    }

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }

    charset = "iso-8859-1"
    
    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/Anchorage"

    def get_articles(self, response):
        self.article_date_map = {}
        articles = []
        for entry in response.xpath('//div[@id="dnn_ctr839_HtmlModule_lblContent"]//ul/li/a'):
            href = entry.xpath('./@href').get()
            title_date_text = entry.xpath('./text()').get()
            full_url = response.urljoin(href)
            decoded_url = unquote(full_url)      
            match = re.match(r'\[.*?\]\s*(.*?)\s*\((\d{1,2}/\d{1,2}/\d{4})\)', title_date_text)
            if match:
                title, date_str = match.groups()
            else:         
                title_match = re.match(r'(.*?)\s*\((\d{1,2}/\d{1,2}/\d{4})\)', title_date_text)
                title, date_str = title_match.groups() if title_match else (title_date_text, "")
            self.article_date_map[decoded_url] = {
                "title": title.strip(),
                "date": date_str.strip()
            }
            articles.append(full_url)
        return articles
 
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response):
        url = unquote(response.url)
        meta = self.article_date_map.get(url)
        return meta.get("title")
    
    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response):
        url = unquote(response.url)
        meta = self.article_date_map.get(url)
        return meta.get("date")

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url] if response.url.lower().endswith(".pdf") else []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        #No next page to scrape
        return None