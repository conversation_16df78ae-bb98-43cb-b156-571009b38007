from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import re
from typing import Optional

class FederalTerritoriesDirectorOfLandsAndMinesOfficeNewsClippings(OCSpider):
    name = "FederalTerritoriesDirectorOfLandsAndMinesOfficeNewsClippings"
    
    start_urls_names = {
        "https://www.ptgwp.gov.my/portal/web/guest/keratan-akhbar": "Newspaper Clippings"  # Pagination is not supported
    }
    start_urls_with_no_pagination_set = {
        "https://www.ptgwp.gov.my/portal/web/guest/keratan-akhbar"
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.GeoProxyMiddleware': 350
            },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    article_data_map = {}

    charset = "utf-8"

    proxy_country = "us"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_date_map = {}
    
    def get_articles(self, response) -> list:
        self.article_data_map = self.extract_articles_with_dates(response)
        return [url for url in self.article_data_map.keys() if not url.lower().endswith(".jpg")]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        article_url = response.url
        article_info = self.article_data_map.get(article_url)
        if article_info and article_info.get("title"):
            return article_info["title"].strip()
        return response.url.split("/")[-1] or "No title found"

    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> Optional[str]:
        article_url = response.url
        article_info = self.article_data_map.get(article_url)
        if not article_info:
            return ""
        raw_date = article_info.get("date")
        if not raw_date:
            return ""
        try:
            parts = raw_date.strip().split(".")
            if len(parts) == 3:
                day = parts[0].zfill(2)
                month = parts[1].zfill(2)
                year = parts[2]
                normalized_date = f"{day}.{month}.{year}"
            else:
                normalized_date = raw_date.strip()
            date_obj = datetime.strptime(normalized_date, "%d.%m.%Y")
            formatted_date = date_obj.strftime("%Y-%m-%d")
            return formatted_date
        except Exception as e:
            return ""

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None

    def extract_articles_with_dates(self, response):
        mapping = {}
        date_nodes = response.xpath("//strong[contains(text(), '[')]")
        for i, date_node in enumerate(date_nodes):
            raw_date = date_node.xpath("string(.)").get()
            match = re.search(r"\[\s*([\d\.]+)\s*\(", raw_date)
            if match:
                clean_date = match.group(1).strip()
            else:
                self.logger.warning(f"Could not extract clean date from: {raw_date}")
                continue
            link_node = date_node.xpath("ancestor::tr/following-sibling::tr[1]//a")
            href = link_node.xpath("@href").get()
            title = link_node.xpath("string(.)").get()
            if href and title:
                full_url = response.urljoin(href.strip())
                mapping[full_url] = {
                    "title": title.strip(),
                    "date": clean_date
                }
        self.article_data_map.update(mapping)
        return mapping  