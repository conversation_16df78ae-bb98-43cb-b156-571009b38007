from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from scraper.utils.helper import body_normalization

class MalaysianMinisterOfFinance(OCSpider):
    name = "MalaysianMinisterOfFinance"

    start_urls_names = {
        "https://www.mof.gov.my/portal/ms/berita" :  "news and media",
        "https://www.mof.gov.my/portal/ms/arkib3/pengumuman" : "announcements",
        "https://www.mof.gov.my/portal/berita/siaran-media" : "media",
        "https://www.mof.gov.my/portal/berita/ucapan" : "speeches"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "FS"
    
    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    malay_to_eng_month = {
        "Januari": "Jan",
        "<PERSON>ruari": "Feb",
        "<PERSON>": "Mar",
        "April": "Apr",
        "<PERSON>": "May",
        "Jun": "Jun",
        "Julai": "Jul",
        "Ogos": "Aug",
        "September": "Sep",
        "Oktober": "Oct",
        "November": "Nov",
        "Disember": "Dec"
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def get_articles(self, response) -> list: 
        base_url = "https://www.mof.gov.my"
        articles = [] 
        for entry in response.xpath('//div[@class="item-info"]//a/@href') or response.xpath('//h2[@itemprop="headline"]//a/@href') or response.xpath('//div[@class="item-info"]//a/@href'):
            url = entry.get()
            if url:
                full_url = base_url + url 
                articles.append(full_url)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@itemprop="headline"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article-details "]//p//text()').getall())

    def get_images(self, response) -> list:
        base_url = "https://www.mof.gov.my"
        image_urls = response.xpath('//div[@class="article-details "]//img/@src').getall()
        return [
            base_url + img if img.startswith("/") else img
            for img in image_urls if img.strip()
        ]

    def date_format(self) -> str:
        return "%d %b %Y"

    def get_date(self, response) -> str:
        start_url = response.meta.get('start_url')
        if  "berita/ucapan" in start_url or "arkib3/pengumuman" in start_url or "berita/siaran-media" in start_url:
            date_text = response.xpath('//time//text()').get().strip()
            if date_text:
                for malay_month, eng_month in self.malay_to_eng_month.items():
                    if malay_month in date_text:
                        date_text = date_text.replace(malay_month, eng_month)
                        break
                return date_text.strip()
        else:
            return response.xpath('//time//text()').get().strip()

    def get_authors(self, response):
        return ""
   
    def get_document_urls(self, response, entry=None):
        base_url = "https://www.mof.gov.my"
        pdf_links = response.xpath('//div[@class="article-details "]//a[contains(@href, ".pdf")]/@href').getall()
        full_links = [link if link.startswith("http") else base_url + link for link in pdf_links]
        return full_links

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        start_url = response.meta.get('start_url')
        if "arkib3/pengumuman" in start_url:
            base_url = "https://www.mof.gov.my"
            url = response.xpath('//a[@aria-label="Go to seterusnya page"]/@href').get()
            full_url= base_url + url if url else None
            return full_url
        else:
            return None