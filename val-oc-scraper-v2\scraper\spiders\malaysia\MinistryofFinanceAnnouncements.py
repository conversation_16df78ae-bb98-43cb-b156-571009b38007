from scraper.OCSpider import OCSpider
from datetime import datetime
from scraper.utils.helper import body_normalization

class MinistryOfFinanceAnnouncements(OCSpider):
    name = "MinistryOfFinanceAnnouncements"
    
    start_urls_names = {
        "https://mof.gov.my/portal/ms/berita/pengumuman": "announcements",  # Pagination is not supported
        "https://mof.gov.my/portal/ms/arkib3/pengumuman" : "archived_announcements"
    }

    start_urls_with_no_pagination_set = {
        "https://mof.gov.my/portal/ms/berita/pengumuman" : "announcements"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='article']//h2//a//@href | //div[@class='item-info']//a//@href | //div[@class='page-header']//h2//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='article']//h2/a/text() | //div[@class='item-info']/a/text() | //h1/text() | //h2/text() | //div[@class='page-header']//a//text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@itemprop='articleBody']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='sppb-addon-content']//img//@src | //a[@class='sppb-magnific-popup sppb-image-lightbox sppb-addon-image-overlay-icon']//@href").getall()
        
    def date_format(self) -> str:
        return "%Y/%m"

    def get_date(self, response):
        MALAY_TO_ENGLISH_MONTH = {
            "Januari": "January",
            "Februari": "February",
            "Mac": "March",
            "April": "April",
            "Mei": "May",
            "Jun": "June",
            "Julai": "July",
            "Ogos": "August",
            "September": "September",
            "Oktober": "October",
            "November": "November",
            "Disember": "December"
        }
        raw_date = response.xpath("//div[@class='article-info']//time/text()").get()
        if raw_date:
            raw_date = raw_date.strip()
            for malay, english in MALAY_TO_ENGLISH_MONTH.items():
                if malay in raw_date:
                    raw_date = raw_date.replace(malay, english)
                    break
            try:
                dt = datetime.strptime(raw_date, "%d %B %Y")
                return dt.strftime(self.date_format())
            except ValueError:              
                return ""
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        pdfs= response.xpath('//div[@itemprop="articleBody"]//a[contains(@href,".pdf")]//@href').getall()
        pdf =[]
        for i in pdfs:
            if 'http' not in i:
                i = 'https://mof.gov.my' + i
            else:
                i=i
            pdf.append(i)
        return pdf
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//div[@class="archive"]//li[@class="page-item next-wrapper"]//a/@href').get()