from scraper.OCSpider import OCSpider
import dateparser
import scrapy

class NationalRegistrationDepartmentNewsClippings(OCSpider):
    name = "NationalRegistrationDepartmentNewsClippings"

    start_urls_names = {
        "https://www.imi.gov.my/index.php/keratan-akhbar/": "News"  # Pagination is not supported
        }

    start_urls_with_no_pagination_set = {
        "https://www.imi.gov.my/index.php/keratan-akhbar/"
    }

    charset = "utf-8"
    
    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    def parse_intermediate(self, response):
        articles =response.xpath('//*[@id="footable_11293"]/tbody/tr/td[3]/a//@href').getall()
        total_articles = len(articles)
        start_url = list(self.start_urls_names.keys())
        for i in start_url:
            for start_idx in range(0,total_articles, 100):  
                yield scrapy.Request(
                    url=i,  
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': i
                    },  
                    dont_filter=True
                )

    def get_articles(self, response) -> list:
        articles= response.xpath('//*[@id="footable_11293"]/tbody/tr/td[3]/a//@href').getall()
        start_idx = response.meta.get('start_idx', 0) 
        end_idx = start_idx + 100       
        return articles[start_idx:end_idx]
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//*[@id="main"]/div/section[3]/div/div[1]/div/div[3]/div/text()').get()
    
    def get_body(self, response) -> str:
        return  ""

    def get_images(self, response) -> list:
        return response.xpath('//*[@id="main"]/div/section[3]/div/div[2]/div/div/div/img//@src').getall()
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        curr_date1=  response.xpath('//*[@id="main"]/div/section[3]/div/div[1]/div/div[7]/div//text()').get()
        parsed_date = dateparser.parse(curr_date1, languages=['en']) 
        return parsed_date.strftime("%Y-%m-%d")
        
    def get_authors(self, response):
        return response.xpath('//*[@id="main"]/div/section[3]/div/div[1]/div/div[5]/div//text()').getall()
       
    def get_document_urls(self, response, entry=None) -> list:
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) : 
        return None 