from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class RegentOfEastFlores(OCSpider):
    name = "RegentOfEastFlores"
    
    start_urls_names = {
        "https://florestimurkab.go.id/portal/": "News"

        # All link redirecting to same link above
        #  https://florestimurkab.go.id/beranda/category/berita-flotim/ 
        #  https://florestimurkab.go.id/beranda/category/pengumuman/ 
        #  https://florestimurkab.go.id/beranda/category/berita-propinsi/  
    }    

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Makassar"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='pagelayer-posts-container']//a[@rel='bookmark']//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='entry-title']//text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='pagelayer-col-holder']//p//text() | //div[@class='pagelayer-col-holder']//ol//li//text() | //div[@class='entry-content clear']//p//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='pagelayer-col-holder']//p//img//@src | //div[@class='pagelayer-col-holder']//ol//li//img//@src | //div[@class='entry-content clear']//p//img//@src").getall()
    
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> Optional[str]:
        return response.xpath("//header[@class='entry-header']//span[@itemprop='datePublished']//text()").get().strip()
    
    def get_authors(self, response):
        return response.xpath("//header[@class='entry-header']//span[@itemprop='author']//text()").get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None