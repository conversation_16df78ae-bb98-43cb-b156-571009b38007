from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re
import urllib.parse
import scrapy
import logging
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

class CenterForAmericanProgress(OCSpider):
    name = "CenterForAmericanProgress"
    country = "US"

    start_urls_names = {
        "https://www.americanprogress.org/press/?f-press_type=press-release": "Press",
    }

    api_start_urls = {
        "https://www.americanprogress.org/press/?f-press_type=press-release": {
            "url": "https://www.americanprogress.org/press/?pg=1&f-press_type=press-release",
            "payload": {
                "pg": 1,
                "f-press_type": "press-release"
            }
        }
    }
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {'scraper.middlewares.HeadlessBrowserProxy': 350},
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000
    charset = "utf-8"
    article_date_map = {}

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/New_York"

    @property
    def headers(self):
        return {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36"
        }

    # def parse_intermediate(self, response):
    #     start_url = response.meta.get("start_url")
    #     api_data = self.api_start_urls.get(start_url)
    #     if not api_data:
    #         return
    #     payload = response.meta.get("payload", api_data["payload"].copy())
    #     api_url = api_data["url"]
    #     full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"

    #     logging.debug(f"Requesting URL: {full_api_url}")

    #     yield scrapy.Request(
    #         url=full_api_url,
    #         method="GET",
    #         callback=self.parse,
    #         meta={
    #             "start_url": start_url,
    #             "api_url": api_url,
    #             "payload": payload,
    #             "current_page": payload["pg"]
    #         }
    #     )

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            logging.warning(f"[parse_intermediate] No API data found for start_url: {start_url}")
            return

        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]

        # Extract article URLs from current page
        articles = self.get_articles(response)

        for article in articles:
            yield scrapy.Request(
                url=article,
                callback=self.parse_article,
                meta={
                    "start_url": start_url,
                    "payload": payload,
                    "api_url": api_url
                }
            )

        # Stop if no articles are found
        if not articles:
            logging.info("[parse_intermediate] No articles found on current page. Stopping pagination.")
            return

        # Go to next page
        next_page = response.meta.get("current_page", 1) + 1
        payload["pg"] = next_page
        next_page_url = f"{api_url}?{urllib.parse.urlencode(payload)}"

        logging.debug(f"[parse_intermediate] Requesting next page URL: {next_page_url}")

        yield scrapy.Request(
            url=next_page_url,
            method="GET",
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            }
        )



    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        links = response.xpath('//div[@class="stream1-entries"]//article[contains(@class, "card2")]//a[contains(@class, "card2-link")]/@href').getall()
        return [response.urljoin(link) for link in links if link]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title = response.xpath('//h1[@class="header2-title -t:1"]/text()').get()
        return title.strip() if title else ""

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="wysiwyg -xw:4 -mx:a" and @data-cap-block-name="wysiwyg - 1"]//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        return self.article_date_map.get(response.url)

    def get_authors(self, response):
        return []

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        next_page = int(current_page) + 1
        articles = self.get_articles(response)
        return next_page

    def get_page_flag(self) -> bool:
        return False

    # def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
    #     api_data = self.api_start_urls.get(start_url)
    #     api_url = api_data["url"]
    #     payload = response.meta.get("payload", {}).copy()
    #     next_page = self.get_next_page(response, current_page)
    #     payload["pg"] = next_page
    #     full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"

    #     logging.debug(f"Next page URL: {full_api_url}")

    #     yield scrapy.Request(
    #         url=full_api_url,
    #         method="GET",
    #         callback=self.parse_intermediate,
    #         meta={
    #             "start_url": start_url,
    #             "api_url": api_url,
    #             "payload": payload,
    #             "current_page": next_page
    #         },
    #     )

    # def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
    #     print(">>> go_to_next_page called")
    #     print(f"Current start_url: {start_url}")
    #     print(f"Current page: {current_page}")

    #     api_data = self.api_start_urls.get(start_url)
    #     print(f"api_data: {api_data}")

    #     api_url = api_data["url"]
    #     print(f"Original API URL: {api_url}")

    #     parsed_url = urlparse(api_url)
    #     existing_query = parse_qs(parsed_url.query)
    #     print(f"Existing query params: {existing_query}")

    #     payload = response.meta.get("payload", {}).copy()
    #     print(f"Payload from response.meta: {payload}")

    #     next_page = self.get_next_page(response, current_page)
    #     print(f"Next page number: {next_page}")

    #     payload["pg"] = next_page
    #     print(f"Updated payload: {payload}")

    #     # Merge existing query and payload
    #     merged_query = {**existing_query, **{k: [str(v)] for k, v in payload.items()}}
    #     query_string = urlencode(merged_query, doseq=True)
    #     full_api_url = urlunparse(parsed_url._replace(query=query_string))

    #     print(f"Final full API URL: {full_api_url}")
    #     logging.debug(f"Next page URL: {full_api_url}")

    #     print(">>> Yielding new scrapy.Request")
    #     yield scrapy.Request(
    #         url=full_api_url,
    #         method="GET",
    #         callback=self.parse_intermediate,
    #         meta={
    #             "start_url": start_url,
    #             "api_url": parsed_url._replace(query="").geturl(),
    #             "payload": payload,
    #             "current_page": next_page
    #         },
    #     )

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        print(">>> go_to_next_page called")
        print(f"Current start_url: {start_url}")
        print(f"Current page: {current_page}")

        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            print(f"[GO TO NEXT PAGE] No API data found for: {start_url}")
            return

        api_url = api_data["url"]
        print(f"Original API URL: {api_url}")

        parsed_url = urlparse(api_url)
        existing_query = parse_qs(parsed_url.query)
        print(f"Existing query params: {existing_query}")

        payload = response.meta.get("payload", {}).copy()
        print(f"Payload from response.meta: {payload}")

        # 🔍 Get articles from current page
        articles = self.get_articles(response)
        if not articles:
            logging.info("[GO TO NEXT PAGE] No articles found in last page. Stopping pagination.")
            return

        next_page = self.get_next_page(response, current_page)
        print(f"Next page number: {next_page}")

        payload["pg"] = next_page
        print(f"Updated payload: {payload}")

        # Merge existing query and payload
        merged_query = {**existing_query, **{k: [str(v)] for k, v in payload.items()}}
        query_string = urlencode(merged_query, doseq=True)
        full_api_url = urlunparse(parsed_url._replace(query=query_string))

        print(f"Final full API URL: {full_api_url}")
        logging.debug(f"Next page URL: {full_api_url}")

        print(">>> Yielding new scrapy.Request")
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": parsed_url._replace(query="").geturl(),
                "payload": payload,
                "current_page": next_page
            },
        )




    def extract_articles_with_dates(self, response):
        articles = response.xpath('//div[@class="stream1-entries"]//article[contains(@class, "card2")]')
        for article in articles:
            url = article.xpath('.//a[contains(@class, "card2-link")]/@href').get()
            date_raw = article.xpath('.//time/@datetime').get()
            if url and date_raw:
                try:
                    date_obj = datetime.strptime(date_raw.strip(), "%Y-%m-%d")
                    formatted_date = date_obj.strftime(self.date_format())
                except ValueError:
                    formatted_date = date_raw.strip()
                full_url = response.urljoin(url)
                self.article_date_map[full_url] = formatted_date
        return self.article_date_map
