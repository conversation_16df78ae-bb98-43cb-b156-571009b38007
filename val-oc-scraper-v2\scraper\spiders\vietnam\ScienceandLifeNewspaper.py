from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ScienceandLifeNewspaper(OCSpider):
    name = "ScienceandLifeNewspaper"

    start_urls_names = {
        "https://khoahocdoisong.vn/khoa-hoc-cong-nghe/": "News"# Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://khoahocdoisong.vn/khoa-hoc-cong-nghe/"
    }

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"

    def get_articles(self, response) -> list:  
        return response.xpath('//h2[@class="story__heading"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="article__title cms-title "]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="container"]//div[@class="sda_middle"]//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="container"]//img//@src').getall()

    def date_format(self) -> str:
        return "%d/%m/%Y"

    def get_date(self, response) -> str:
        return  response.xpath('//time//text()').re_first(r"(\d{1,2}/\d{1,2}/\d{4})")

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return None