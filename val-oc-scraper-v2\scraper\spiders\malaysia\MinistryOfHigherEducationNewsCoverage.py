from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MinistryOfHigherEducationNewsCoverage(OCSpider):
    name = "MinistryOfHigherEducationNewsCoverage"

    start_urls_names = {
        "https://www.mohe.gov.my/en/broadcast/media-coverage?start=0": "News"
    }

    start_urls_with_no_pagination_set = {}
    
    charset = "utf-8"

    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
     
    def get_articles(self, response) -> list:  
        return response.xpath("//td[@class='list-title']//a//@href").getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1[@property='headline']//text()").get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath("//div[@class='uk-margin-top']//p//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='uk-margin-top']//img//@src | //div[@class='uk-text-center uk-margin-top']//img//@src").getall()
       
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//article[@class='uk-article']//time//text()").get() 
        
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath("//div[@class='pagination']//a[@class='next']//@href").get()