from typing import List, Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import re
class ROBAM(OCSpider):
    name = "ROBAM"

    start_urls_names = {
        "https://www.robam.com/news.html": "老板电器",
        "https://www.robam.com/about/investment.html ": "老板电器",
        "https://www.robam.com/about/company/172.html": "老板电器", 
    }

    article_data_map ={}  # Mapping child article with title, date and PDF from start URL

    charset = "iso-8859-1"

    def parse_intermediate(self, response):
        news_articles = response.xpath("//div[@class='news-block']//div[@class='news-list']//a//@href").getall()
        investment_articles = response.xpath("//div[@class='investment']//ul//li//a//@href").getall()
        company_articles = response.xpath("//div[@class='company']//ul//li//a//@href").getall()
        articles = news_articles + investment_articles + company_articles
        total_articles = len(articles)
        start_url = list(self.start_urls_names.keys())
        for i in start_url:
            for start_idx in range(0,total_articles, 100):  # Indexing for virtual pagination to extract more than 100 articles from single page
                yield scrapy.Request(
                    url=i,  
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': i
                    },  
                    dont_filter=True
                )
                
    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            try:
                articles = []
                article_blocks = (
                response.xpath("//div[@class='news-block']//div[@class='news-list']//a[@class='news-item hv-scale-big']") or
                response.xpath("//div[@class='investment']//ul//li") or
                response.xpath("//div[@class='company']//ul//li")
            )
                for article in article_blocks:
                    url = article.xpath(".//a//@href | .//@href").get()
                    title = article.xpath(".//div[@class='title hv-blue text-wrap-2']//text() | .//div[@class='d1']//text()").get()
                    date = article.xpath(".//div[@class='date']//text()").get()
                    if '.pdf' in url.lower():
                        date = "None"
                    if url and title and date:
                        full_url = url
                        title = title.strip()
                        if '.pdf' in url.lower():
                            pdf = full_url
                        else:
                            pdf = "None"
                        clean_date = date.strip()
                        self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                        articles.append(full_url) 
                start_idx = response.meta.get('start_idx', 0) 
                end_idx = start_idx + 100       
                return articles[start_idx:end_idx]
            except Exception as e:
                return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath("//div[@class='main']//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> str:
        if '.pdf' in response.url.lower():
            for pattern in [r'/upload/(\d{4})/(\d{2})/(\d{2})/', r'/upload/(\d{4})(\d{2})/(\d{2})/']:
                match = re.search(pattern, response.url)
                if match:
                    year, month, day = match.groups()
                    return f"{year}-{month}-{day}"
            return None 
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", None)
        
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        if response.url.lower().endswith(".pdf"):
            return [response.url]
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")
        if pdf_url != 'None':
            return [pdf_url]

    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath("//div[@class='main']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='pagination']//a[contains(text(),'下一页')]//@href").get()
        if next_page:
            return next_page
        else:
            return None