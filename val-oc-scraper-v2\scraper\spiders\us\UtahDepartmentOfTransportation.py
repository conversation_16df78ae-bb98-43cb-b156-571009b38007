from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class UtahDepartmentOfTransportation(OCSpider):
    name = "UtahDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://udot.utah.gov/connect/": "PUBLIC NOTICE",
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Anchorage"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="featured-posts large-feature"]/a[@class="post hasphoto"]/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2[span[@class="post-date"]]/text()[1]').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//article//h3/text() | //article//p/text()").getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = (response.xpath('//h2/span[@class="post-date"]/text()').get()).strip()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:  
        # No next page to scape
        return None