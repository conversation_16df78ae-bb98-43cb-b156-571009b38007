from scraper.OCSpider import OCSpider
import re

class MinistryOfRuralAndRegionalDevelopmentArchive(OCSpider):
    name = "MinistryOfRuralAndRegionalDevelopmentArchive"

    start_urls_names = {
        "https://www.rurallink.gov.my/kenyataan-media-2024/": "news",
        "https://www.rurallink.gov.my/kenyataan-media-tahun-2021/": "news",
        "https://www.rurallink.gov.my/kenyataan-media/": "news",
        "https://www.rurallink.gov.my/kenyataan-media-2023/": "news",
        "https://www.rurallink.gov.my/arkib/arkib-kenyataan-media-tahun-2020-2016/": "news",
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}
    
    def get_articles(self, response) -> list:
        articles = []       
        for entry in response.xpath('//div[@class="eael-accordion-content clearfix"]//ul//li'):
            url = entry.xpath('.//a/@href').get()
            title = entry.xpath('.//a//text()').get()   
            match = re.match(r".*/uploads/(\d{4})/(\d{2})/", url)
            if match:
                year, month = match.groups()
                date = f"{year}/{month}"
            if url and title and date:
                self.article_data_map[url]={"title":title.strip(),"date":date.strip()}
                articles.append(url)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y/%m"
    
    def get_date(self, response) -> str:
         return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
    
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None