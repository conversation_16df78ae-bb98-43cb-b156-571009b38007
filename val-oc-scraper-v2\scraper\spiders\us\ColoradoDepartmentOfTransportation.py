from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re

class ColoradoDepartmentOfTransportation(OCSpider):
    name = "ColoradoDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://www.codot.gov/news/top-news": "Top News",
    }
        
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Denver"
    
    def get_articles(self, response) -> list:
        return response.xpath('//table[@class="listing"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[(@class="documentFirstHeading")]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="parent-fieldname-text"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_text = response.xpath('//div[@class="documentDescription description"]/text()').get()
        date_cleaned = re.sub(r'\s*,\s*', ', ', date_text.strip().split(" - ")[0]) 
        date_cleaned = re.sub(r'(\b[A-Za-z]{3,}\.)', lambda m: m.group(1)[:-1], date_cleaned)  # Example: https://www.codot.gov/news/2024/december/temporary-full-closures-valley-road-wcr32
        date_cleaned = re.sub(r'([A-Za-z]+)(\d{1,2},)', r'\1 \2', date_cleaned)
        # Extract or append year
        extracted_year = re.search(r'/(\d{4})/', response.url)
        date_cleaned += f", {extracted_year.group(1)}" if extracted_year and not re.search(r'\d{4}', date_cleaned) else ""
        date_format = "%b %d, %Y" if re.match(r'^[A-Za-z]{3} \d{1,2}, \d{4}$', date_cleaned) else "%B %d, %Y"
        return datetime.strptime(date_cleaned, date_format).strftime("%m-%d-%Y")
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//a[span[@class="label" and contains(text(), "Next 30 items")]]/@href').get()
        if not next_page:
           return None
        else:
            return next_page