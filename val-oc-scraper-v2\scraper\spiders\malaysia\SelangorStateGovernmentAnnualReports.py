from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
import re

class SelangorStateGovernmentAnnualReports(OCSpider):
    name = "SelangorStateGovernmentAnnualReports"

    start_urls_names = {
        "https://www.selangor.gov.my/index.php/pages/view/2126?mid=994": "Laporan Tahunan PSUK"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}  # Mapping date, PDF URL and title to child articles from start URL

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    def get_articles(self, response) -> list:  
        base_url = "https://www.selangor.gov.my"
        articles =[]
        mapping = {}
        for entry in response.xpath('//div[@class="product"]'):
            url= entry.xpath('./@data-url').get()
            full_url = base_url + url if url else None
            if full_url:
                articles.append(full_url)
            title = entry.xpath('./@title').get()
            year = None
            match = re.search(r'20\d{2}', title)
            if match:
                year = match.group()
            mapping[full_url] = {
                'title': title.strip() if title else None,
                'date': year.strip() if year else None,
                'pdf_url': full_url
            }
        self.article_to_pdf_mapping.update(mapping)
        return articles
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        article_url = response.url
        if article_url in self.article_to_pdf_mapping:
            title = self.article_to_pdf_mapping[article_url].get('title')
            if title:
                return title.strip()
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""

    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        article_url = response.url
        if article_url in self.article_to_pdf_mapping:
            date = self.article_to_pdf_mapping[article_url].get('date')
            if date:
                return date.strip()

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None):
        article_url = response.url
        if article_url in self.article_to_pdf_mapping:
            pdf_url = self.article_to_pdf_mapping[article_url].get('pdf_url')
            if pdf_url:
                return [pdf_url]
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to crawl
        return None