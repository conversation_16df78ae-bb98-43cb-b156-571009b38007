# from typing import Optional
# from scraper.OCSpider import OCS<PERSON>er
# from scraper.utils.helper import body_normalization
# from scraper.middlewares import HeadlessBrowserProxy
# import scrapy
# from datetime import datetime
# import re

# class LuatVietnamLegaldocumentsofVietnameselawsVietnam(OCSpider):
#     name = "LuatVietnamLegaldocumentsofVietnameselawsVietnam"
    
#     start_urls_names = {
#         "https://luatvietnam.vn/tin-van-ban-moi-c186-article.html": "News"
#     }

#     start_urls_with_no_pagination_set = {}

#     charset = "utf-8"

#     # def parse_intermediate(self, response):
#     #     hbp = HeadlessBrowserProxy()
#     #     request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse)
#     #     request.meta['start_url'] = response.request.meta['start_url']
#     #     yield request

#     custom_settings = {
#         "DOWNLOADER_MIDDLEWARES": {
#            'scraper.middlewares.GeoProxyMiddleware': 350,
#         },
#         "DOWNLOAD_DELAY": 1,
#     }

#     country = "Vietnam"

#     @property
#     def language(self): 
#         return "Bahasa Malaysia"
    
#     @property
#     def source_type(self) -> str:
#         return "ministry"
    
#     @property
#     def timezone(self):
#         return "Asia/Ho_Chi_Minh"
    
#     def get_articles(self, response) -> list:
#         return response.xpath('//h3[@class="article-title title-2"]//a//@href | //h3[@class="article-title font"]//a//@href').getall()

#     def get_href(self, entry) -> str:
#         return entry
    
#     def get_title(self, response) -> str:
#         return response.xpath('//h1[@class="the-article-title"]//text()').get()

#     def get_body(self, response) -> str:
#         return body_normalization(response.xpath('//div[@itemprop="articleBody"]//text()').getall())

#     def get_images(self, response) -> list:
#         return response.xpath('//div[@itemprop="articleBody"]//img//@src').getall()
    
#     def date_format(self) -> str:
#         return "%d/%m/%Y"
    
#     def get_date(self, response) -> Optional[str]:
#         date_string = response.xpath('//div[@class="date-publish"]//text()').get()
#         if not date_string:
#             return None

#         # Example raw: "Thursday, August 21, 2025 ,13:10"
#         date_string = date_string.strip()

#         # Remove weekday if present (before first comma)
#         if "," in date_string:
#             date_string = date_string.split(",", 1)[1].strip()  # "August 21, 2025 ,13:10"

#         # Remove time if present
#         date_string = re.sub(r",\s*\d{1,2}:\d{2}$", "", date_string).strip()  # "August 21, 2025"

#         try:
#             parsed_date = datetime.strptime(date_string, "%B %d, %Y")
#             return parsed_date.strftime("%d/%m/%Y")  # → "21/08/2025"
#         except ValueError:
#             return date_string  # fallback
    
#     def get_authors(self, response):
#         return []
    
#     def get_page_flag(self) -> bool:
#         return False
    
#     def get_next_page(self, response) -> Optional[str]:
#         return response.xpath('//div[@class="pagination-page"]//a[contains(text(), "›")]/@href').get()


from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from datetime import datetime
import re

class LuatVietnamLegaldocumentsofVietnameselawsVietnam(OCSpider):
    name = "LuatVietnamLegaldocumentsofVietnameselawsVietnam"
    
    start_urls_names = {
        "https://luatvietnam.vn/tin-van-ban-moi-c186-article.html": "News"
    }

    start_urls_with_no_pagination_set = {}

    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
           'scraper.middlewares.GeoProxyMiddleware': 350,
        },
        "DOWNLOAD_DELAY": 1,
    }

    country = "Vietnam"
    proxy_country = "VN"   # 👈 this fixes the AttributeError

    @property
    def language(self): 
        return "Vietnamese"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    def get_articles(self, response) -> list:
        return response.xpath(
            '//h3[@class="article-title title-2"]//a//@href | '
            '//h3[@class="article-title font"]//a//@href'
        ).getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="the-article-title"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath('//div[@itemprop="articleBody"]//text()').getall()
        )

    def get_images(self, response) -> list:
        return response.xpath('//div[@itemprop="articleBody"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_string = response.xpath('//div[@class="date-publish"]//text()').get()
        if not date_string:
            return None

        date_string = date_string.strip()

        # Remove weekday if present
        if "," in date_string:
            date_string = date_string.split(",", 1)[1].strip()

        # Remove time if present
        date_string = re.sub(r",\s*\d{1,2}:\d{2}$", "", date_string).strip()

        try:
            parsed_date = datetime.strptime(date_string, "%B %d, %Y")
            return parsed_date.strftime("%d/%m/%Y")
        except ValueError:
            return date_string  # fallback
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return response.xpath(
            '//div[@class="pagination-page"]//a[contains(text(), "›")]/@href'
        ).get()
