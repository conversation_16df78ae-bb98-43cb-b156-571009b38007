from typing import Optional
from scraper.OCSpider import OCSpider

class PahangStateGovernmentPolicies(OCSpider):
    name = "PahangStateGovernmentPolicies"
    
    start_urls_names = {
            "https://www.pahang.gov.my/index.php/pages/view/1133?mid=226": "News" #pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.pahang.gov.my/index.php/pages/view/1133?mid=226"
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    article_data_map = {}

    page_updated_date = None  # will store extracted date from start URL

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    def get_articles(self, response) -> list:
        date_parts = response.xpath("//div[@class='website--stc']//span[@class='last_update']//text()").getall()
        self.page_updated_date = date_parts[-1].strip() if date_parts else ""
        articles = []
        for article in response.xpath("//table[@class='table-hover']//tbody//tr"):
            url = article.xpath(".//a/@href").get()
            title = article.xpath(".//a/text()").get()
            if url and title:
                self.article_data_map[url] = {
                    "url": url,
                    "title": title,
                    "date": self.page_updated_date
                }
                articles.append(url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%d %m %Y"

    def get_date(self, response):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_document_urls(self, response, entry=None) -> list:
        if ".pdf" in response.url.lower():
            return [response.url]
        return []

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return None