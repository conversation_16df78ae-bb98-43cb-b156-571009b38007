from scraper.OCSpider import OCSpider
import re

class MalaysiaCompetitionCommissionGuidelines(OCSpider):
    name = "MalaysiaCompetitionCommissionGuidelines"

    start_urls_names = {
        'https://www.mycc.gov.my/guidelines' : 'Guidelines',# Pagination is not suported
    }

    start_urls_with_no_pagination_set = {
        'https://www.mycc.gov.my/guidelines' 
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self):
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    article_title_pdf_mapping = {}

    def get_articles(self, response) -> list:
        articles = []
        rows = response.xpath('//div[@class="handbook-wrapper"]')
        for row in rows:
            title = row.xpath('.//div[@class="handbook-title"]//text()').get()
            pdf_link = row.xpath('.//a/@href').get()
            img_link = row.xpath('.//img/@src').get()
            date = row.xpath('//div[@class="handbook-date"]/time/text()').get()
            if title and pdf_link and img_link and date:
                title = title.strip()
                pdf_link = response.urljoin(pdf_link.strip())
                img_link = response.urljoin(img_link.strip())
                self.article_title_pdf_mapping[pdf_link] = (title, pdf_link, img_link, date)
                articles.append(pdf_link)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[0]
    
    def get_body(self, response) -> str:
        return ""
    
    def date_format(self) -> str:
       return "%d %b %Y"

    def get_date(self, response) -> str:
        return self.article_title_pdf_mapping.get(response.url, ("", "", ""))[3]

    def get_images(self, response) -> list[str]:
        return [self.article_title_pdf_mapping.get(response.url, ("", "", ""))[2]]

    def get_document_urls(self, response, entry=None)-> list:
        return [self.article_title_pdf_mapping.get(response.url, ("", "", ""))[1]]

    def get_author(self, response) -> str:
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return None