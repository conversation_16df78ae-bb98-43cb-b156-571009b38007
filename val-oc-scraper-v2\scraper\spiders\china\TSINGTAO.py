import json
from urllib.parse import unquote
from scraper.OCSpider import OCSpider
from datetime import datetime
import scrapy
from scraper.utils.helper import body_normalization
from typing import Optional

class TSINGTAO(OCSpider):
    name="TSINGTAO"

    start_urls_names={
        "https://www.tsingtao.com.cn/notice-group.html" : "学会动态", 
        "https://www.tsingtao.com.cn/investment/invest.html" : "媒体信息",
        "https://www.tsingtao.com.cn/news/news.html" : "最新公告", 
    }
    
    charset = "iso-8859-1"

    api_start_urls = {
        "https://www.tsingtao.com.cn/notice-group.html": {
            "url": "https://www.tsingtao.com.cn/TsingTao/web/search-notice-group",
            "payload": {
                "currentPage" : "1",
                "pageSize" : "10",
                "searchDate" : ""
            }
        },
        "https://www.tsingtao.com.cn/investment/invest.html": {
            "url": "https://www.tsingtao.com.cn/TsingTao/web/investment/noticeSearch",
            "payload": {
                "currentPage" : "1",
                "pageSize" : "10",
                "searchDate" : ""
            }
        },
        "https://www.tsingtao.com.cn/news/news.html": {
            "url": "https://www.tsingtao.com.cn/TsingTao/web/news/getNewsPage",
            "payload": {
                "currentPage" : "1",
                "pageSize" : "8",
            }
        },
        }

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_urls[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            current_page = response.meta.get("current_page", 1)
            api_data["payload"]["currentPage"] = str(current_page)
            payload = api_data["payload"]
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers=headers,
                dont_filter=True,
                formdata=payload,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "current_page": current_page
                },
            )  
    
    article_to_pdf_urls_mapping={}

    def get_articles(self, response) -> list:
        start_url = response.meta.get("start_url")
        articles =[]
        mapping ={}
        data = json.loads(response.text)
        elements= data.get("records",[])
        if start_url == "https://www.tsingtao.com.cn/news/news.html":
            for item in elements:
                id = item.get('objectid')
                url =f"https://www.tsingtao.com.cn/news/{id}.html"
                articles.append(url)
        else:
            base_url = "https://www.tsingtao.com.cn/TsingTao/"
            articles =[]
            mapping ={}
            for item in elements:
                title = item.get("maintitle", "")
                attachments = item.get("attachmentlist", [])
                for attachment in attachments:
                    filepath = attachment.get("filepath")
                    date = attachment.get("createdtime")
                    full_url=base_url + filepath
                    mapping[full_url]={
                        "title": title,
                        "date": date,
                        "pdf_urls": full_url
                    }
                    articles.append(full_url)
            self.article_to_pdf_urls_mapping.update(mapping)
        return articles

    def get_href(self, entry) -> str:
        return entry   

    def get_title(self, response) -> str:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        if "text" in content_type or "html" in content_type:
            title = response.xpath('//div[@class="page-news-title"]//text()').get()
            if title:
                return title.strip()
        # Fallback for non-HTML responses like PDFs
        decoded_url = unquote(response.url)
        mapping_entry = self.article_to_pdf_urls_mapping.get(decoded_url)
        if mapping_entry:
            return mapping_entry.get("title", "")
        return ""
    
    def get_body(self, response) -> str:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        if "text" in content_type or "html" in content_type:
            body = body_normalization(response.xpath('//div[@class="page-news-content"]//p//text()').getall())
            if body:
                return body
        return ""

    def get_images(self, response) -> list:
        return []
    
    def get_document_urls(self, response, entry=None)->list :
        try:
            decoded_url = unquote(response.url)
            mapping_entry = self.article_to_pdf_urls_mapping.get(decoded_url)
            if mapping_entry:
                pdf_url = mapping_entry.get("pdf_urls", "")
                print("PDF URL found:", pdf_url)
                return pdf_url
            else:
                print("No mapping found for:", decoded_url)
                return ""
        except Exception as e:
            pass
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        content_type = response.headers.get("Content-Type", b"").decode("utf-8").lower()
        date = ""
        if "text" in content_type or "html" in content_type:
            date = response.xpath("//time//text()").get()
            if date:
                date = date.replace("发布时间：", "").strip()
        if not date:
            decoded_url = unquote(response.url)
            mapping_entry = self.article_to_pdf_urls_mapping.get(decoded_url)
            if mapping_entry:
                date_str = mapping_entry.get("date", "")
                if date_str:
                    try:
                        dateobj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                        date = dateobj.strftime("%Y-%m-%d")
                    except ValueError:
                        # fallback if date format doesn't match
                        date = date_str
        return date or ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        try :
            current_page = response.meta.get("current_page")
            print(current_page)
            if response.status != 200:
                return None
            return str(int(current_page) + 1)
        except Exception as e:
            pass
        

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        start_url = response.meta.get("start_url")
        api_url = response.meta.get("api_url")
        next_page = self.get_next_page(response)
        payload = response.meta.get('payload')
        payload['currentPage'] = next_page  
        yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                dont_filter=True,
                formdata=payload,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "payload": payload,
                    "current_page": next_page
                },
            ) 
