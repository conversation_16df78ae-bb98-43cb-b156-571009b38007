from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaTourismAssociation(OCSpider):
    name = 'ChinaTourismAssociation'
    
    start_urls_names = {
        'http://www.chinata.com.cn/h-col-115.html': '行业新闻',
        'http://www.chinata.com.cn/h-col-116.html': '协会动态',
        'http://www.chinata.com.cn/sys-nr/?_reqArgs=%7B%22args%22%3A%7B%22_jcp%22%3A%224_27%22%7D%2C%22type%22%3A15%7D': '通知公告',
        'http://www.chinata.com.cn/sys-nr/?_reqArgs=%7B%22args%22%3A%7B%22_jcp%22%3A%224_24%22%7D%2C%22type%22%3A15%7D': '协会动态',
    }

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        if "h-col-115" in response.url or "h-col-116" in response.url:
            articles = response.xpath("//div[@class='news_title']//a/@href").getall()  # Xpath 1
        else:
            articles = response.xpath("//td[@class='newsTitle']//a//@href").getall()   # XPath 2 for other start urls
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class="title"]//text()').get()
        
    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M'
    
    def get_date(self, response) -> str:
        return response.xpath("//span[@class='newsInfo']//text()").re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}")
        
    def get_authors(self, response):
        return []
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='jz_fix_ue_img']/p//text()").getall())
    
    def get_images(self, response) -> list[str]:
        images = response.xpath("//p//img/@src").extract()
        return images
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath("//span[@class='pageNext']//a//@href").get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:  # Condition to check if the next page is the same as the current page
                    self.logger.info(f"Next page resolves to the current page URL: {response.url}. Stopping pagination.")
                    return None  # Stop crawling
                self.logger.info(f"Found next page: {next_page_url}")
                return next_page_url
            self.logger.info("No next page found.")
            return None
        except Exception as e:
             self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
             return None