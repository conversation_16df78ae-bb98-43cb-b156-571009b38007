from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import dateparser

class VietnamNationalAuthorityOfTourism(OCSpider):
    name = "VietnamNationalAuthorityOfTourism"

    start_urls_names = {
        "https://vietnamtourism.gov.vn/cat/55?page=1": "Notice"
    }

    start_urls_with_no_pagination_set = {}

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 3000
        },
        "DOWNLOAD_DELAY": 3,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 6000

    charset = "utf-8"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Vietnamese"
    
    @property
    def timezone(self):
        return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}

    def get_articles(self, response) -> list:
        links = response.xpath('//*[@id="default-theme-wrapper"]//section//div[2]/div/div/div[2]/div/a/@href').getall()
        return links
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p[@style="text-align: justify;"]//text() | ''//div/p/span/span/span/span/span/span//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div//p//img/@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath('//span[@class="post-publish-date"]//text()').get()
        if not date:
            return None
        clean_date = date.strip()
        dt = dateparser.parse(clean_date, languages=["vi"])
        if not dt:
            return None
        return dt.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        return response.xpath('//a[normalize-space(.)=">>"]/@href').get()