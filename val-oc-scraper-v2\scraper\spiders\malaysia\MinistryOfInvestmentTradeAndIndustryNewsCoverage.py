from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from typing import Optional
import re
from urllib.parse import urljoin
import scrapy
from urllib.parse import urljoin
from datetime import datetime


class MinistryOfInvestmentTradeAndIndustryNewsCoverage(OCSpider):
    name = "MinistryOfInvestmentTradeAndIndustryNewsCoverage"

    start_urls_names = {
        "https://www.miti.gov.my/index.php/pages/view/8818": "News Coverage",
    }

    charset = "iso-8859-1"

    country = "Malaysia"


    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000


    @property
    def language(self): 
        return "Bahasa Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_data_map = {}

    def parse_intermediate(self, response):
        self.extract_articles_with_dates(response)
        article_urls = list(self.article_data_map.keys())[:100]
        for idx, url in enumerate(article_urls, 1):
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                dont_filter=True
            )


    def get_articles(self, response) -> list:
        articles = response.xpath('//*[@id="footable_11293"]/tbody/tr/td[3]/a/@href').getall()
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return articles[start_idx:end_idx]


    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = response.xpath("//div[contains(@class, 'news-detail_newsdetailsItemHead')]/span/text()").get()
        if title:
            return title.strip()
        url = response.url
        if hasattr(self, "article_data_map"):
            article = self.article_data_map.get(url)
            if article:
                return article.get("title", "")
        return ""

    def get_body(self, response) -> str: 
        return ""

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        date_text = response.xpath("//div[contains(@class, 'news-detail_newsInfo')]/span[1]/text()").get()
        if date_text:
            return date_text.strip()
        url = response.url
        if hasattr(self, "article_data_map"):
            article = self.article_data_map.get(url)
            if article:
                return article.get("date", "")
        return ""

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None
   
    def extract_articles_with_dates(self, response):
        mapping = {}
        base_url = response.url
        list_items = response.xpath("//li")
        for idx, li in enumerate(list_items, 1):
            title = li.xpath(".//a//text()").get()
            href = li.xpath(".//a/@href").get()
            if not title or not href:
                continue
            full_url = urljoin(base_url, href.strip())
            date_match = re.search(r'(\d{8})', full_url)
            if not date_match:
                continue
            try:
                parsed_date = datetime.strptime(date_match.group(1), "%Y%m%d")
                formatted_date = parsed_date.strftime("%B %Y")
            except ValueError:
                continue
            mapping[full_url] = {
                "title": title.strip(),
                "date": formatted_date
            }
        if not hasattr(self, "article_data_map"):
            self.article_data_map = {}
        self.article_data_map.update(mapping)
        return self.article_data_map
