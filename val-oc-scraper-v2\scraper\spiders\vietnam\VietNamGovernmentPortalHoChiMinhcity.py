#VietNamGovernmentPortalHoChiMinhcity
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class VietNamGovernmentPortalHoChiMinhcity(OCSpider):
    name = "VietNamGovernmentPortalHoChiMinhcity"

    start_urls_names = {
        f"https://tphcm.chinhphu.vn/timelinelist/1011/{i}.htm": "News" for i in range(1,90,1)
        }
       
    start_urls_with_no_pagination_set = {
        "https://tphcm.chinhphu.vn/timelinelist/1011/1.htm" # Pagination is not suported
    }
    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "minister"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//h2//a//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@data-role="content"]//p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//table//tr//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//span[@data-role="publishdate"]//text()').get()
        #print(date,"Date is ================================")
        date_obj = datetime.strptime(date.strip(), "%d/%m/%Y %I:%M %p")
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None