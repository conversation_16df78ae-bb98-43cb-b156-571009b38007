from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from urllib.parse import unquote, urljoin
import re

class DepartmentOfIrrigationAndDrainageAnnualReports(OCSpider):
    name = "DepartmentOfIrrigationAndDrainageAnnualReports"

    start_urls_names = {
        "https://www.water.gov.my/index.php/pages/view/1382": "Annual Report"  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        "https://www.water.gov.my/index.php/pages/view/1382"
    }

    charset = "iso-8859-1"
    country = "Malaysia"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Bahasa Malaysia"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    article_to_pdf_mapping = {}

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(
            hbp.get_proxy(response.url, timeout=10000),
            callback=self.parse
        )
        request.meta['start_url'] = response.request.meta.get('start_url', response.url)
        yield request

    def get_articles(self, response) -> list: 
        mapping = {}
        articles = []
        base_url = 'https://www.water.gov.my'
        rows = response.xpath('//div[@class="product"]')
        for row in rows:
            title = row.xpath('./@title').get()
            year_match = re.search(r'\b(20\d{2}|19\d{2})\b', title or "")
            date = year_match.group(0) if year_match else None
            article_url = row.xpath('./@data-url').get()
            if title and date and article_url:
                full_article_url = urljoin(base_url, article_url)
                normalized_link = unquote(full_article_url).rstrip('/')
                articles.append(normalized_link)
                mapping[normalized_link] = {
                    'title': title,
                    'pdf': None,          # No direct PDF here: will be filled after visiting article page
                    'date': date
                }
        self.article_to_pdf_mapping.update(mapping)
        return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get("title", "")

    def get_body(self, response) -> str:
        return ""
    
    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y"

    def get_date(self, response) -> str:
        normalized_url = unquote(response.url).rstrip('/')
        return self.article_to_pdf_mapping.get(normalized_url, {}).get('date')

    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        normalized_url = unquote(response.url).rstrip('/')
        cached = self.article_to_pdf_mapping.get(normalized_url, {}).get("pdf")
        if cached is not None:
            return [cached] if cached else []
        pdf_url = response.xpath('//a[contains(translate(@href, "PDF", "pdf"), ".pdf")]/@href').get()
        if pdf_url:
            pdf_url = urljoin(response.url, pdf_url)
            # Save for future calls
            self.article_to_pdf_mapping[normalized_url]["pdf"] = pdf_url
            return [pdf_url]
        # Fallback: Sometimes the PDF is embedded in iframe, object, etc.
        pdf_url = response.xpath('//iframe[contains(@src,".pdf")]/@src | //embed[contains(@src,".pdf")]/@src').get()
        if pdf_url:
            pdf_url = urljoin(response.url, pdf_url)
            self.article_to_pdf_mapping[normalized_url]["pdf"] = pdf_url
            return [pdf_url]
        self.article_to_pdf_mapping[normalized_url]["pdf"] = None
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None