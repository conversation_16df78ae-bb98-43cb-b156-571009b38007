from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime

class VietnamEnergyOnline(OCSpider):
    name = "VietnamEnergyOnline"

    start_urls_names = {
        "https://nangluongvietnam.vn/kien-giai-ton-tai&s_cond=&BRSR=0": "News",
        }
    
    start_urls_with_no_pagination_set = {}

    charset = "iso-8859-1"

    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       return response.xpath('//a[@class="article-link f0"]//@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//*[@id="main"]/div/div/div[1]/div[2]/h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//table/tbody/tr/td/img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//*[@id="main"]/div/div/div[1]/div[2]/p/span/span[2]//text()').get()
        date_obj = datetime.strptime(date.strip(), "%d/%m/%Y")
        formatted_date = date_obj.strftime("%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return response.xpath('//a[normalize-space(text())=">"]/@href').get()