from scraper.OCSpider import OCSpider
import re

class MinistryOfWorksDeputyMinistersSpeeches(OCSpider):
    name = "MinistryOfWorksDeputyMinistersSpeeches"
    
    start_urls_names = {
        "https://www.kkr.gov.my/ms/orang-awam/ucapan/ucapan-timbalan-menteri-kerja-raya": "News"
    }

    charset = "iso-8859-1"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Malaysia"
        self.article_data_map ={}

    @property
    def language(self): 
        return "Bahasa Malaysia"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
        
    def get_articles(self, response) -> list:
        return response.xpath("//td[@class='views-field views-field-title']//a//@href").getall()

    def get_href(self, entry) -> str:
        return f"https://www.kkr.gov.my{entry}"
    
    def get_title(self, response) -> str:
        return response.xpath("//h2[@class='page-title']//text()").get().strip()
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%d/%m/%Y"
    
    def get_date(self, response): 
         date_text= response.xpath("//div[@class='field__item']//time//text()").get()
         match = re.search(r'\d{2}/\d{2}/\d{4}', date_text)
         if match:
            date_only = match.group()
            return date_only
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath("//div[@class='field__item']//a//@href").getall()
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
        
    def get_next_page(self, response) :
        return response.xpath("//li[@class='pager__item pager__item--next']//a//@href").get()