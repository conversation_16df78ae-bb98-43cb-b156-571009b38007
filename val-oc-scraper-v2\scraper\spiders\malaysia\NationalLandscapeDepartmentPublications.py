from scraper.OCSpider import OCSpider
import dateparser
import re

class NationalLandscapeDepartmentPublications(OCSpider):
    name = "NationalLandscapeDepartmentPublications"

    start_urls_names = {
        "https://www.jln.gov.my/index.php/pages/view/43": "News/PressNote"
        }
    
    start_urls_with_no_pagination_set = {
        "https://www.jln.gov.my/index.php/pages/view/43"
    }

    charset = "iso-8859-1"
   
    country = "Malaysia"
    
    @property
    def source_type(self) -> str:
        return "official_line"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
        
    article_data_map = {}

    def get_articles(self, response) -> list:  
        articles = []
        for row in response.xpath('//*[@id="container_content"]/div[1]/table/tbody/tr'):
            tds = row.xpath('./td')
            if not tds:
                continue
            next_row = row.xpath('following-sibling::tr[1]')
            next_tds = next_row.xpath('./td')
            for td, next_td in zip(tds, next_tds):
                url = td.xpath('.//a/@href').get()
                img_url = td.xpath('.//a//img/@src').get()
                title = next_td.xpath('.//span[1]/text()').get()
                date = next_td.xpath('.//span[2]/text()').get()
                if not date:
                    date = "2025"
                if url and title:
                    self.article_data_map[url] = {"date": date,"title": title,"img_url": img_url}
                    articles.append(url)
        return list(set(articles))

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
           
    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> list:
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("img_url", "")]
        
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        curr = date[-4:]
        year_str = curr if re.fullmatch(r"\d{4}", curr) else "2025"
        parsed_date = dateparser.parse(year_str, languages=['ms', 'en'])
        return parsed_date.strftime("%Y")
    
    def get_authors(self, response):
        return ""

    def get_document_urls(self, response, entry=None) -> list:
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        # No next page to scrape
        return None 