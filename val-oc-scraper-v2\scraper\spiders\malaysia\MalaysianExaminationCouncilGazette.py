from scraper.OCSpider import OCSpider
import re

class MalaysianExaminationCouncilGazette(OCSpider):
    name = "MalaysianExaminationCouncilGazette"

    start_urls_names = {
        'https://www.mpm.edu.my/en/resources/pewartaan#': 'Proclamation'  # Pagination is not supported
    }

    start_urls_with_no_pagination_set = {
        'https://www.mpm.edu.my/en/resources/pewartaan#'
    }

    charset = "iso-8859-1"

    country = "Malaysia"

    @property
    def language(self): 
        return "Malay"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"

    articles_url_date_docUrl_mapping={}
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//div[@class="pd-filebox"]'):
            title = article.xpath('.//div[@class="pd-title"]/text()').get()
            date_element = article.xpath('.//div[@class="pd-button-details"]//a/@onmouseover').get()
            date = re.search(r'\d{2} \w+ \d{4}', date_element).group()
            doc_url = response.urljoin(article.xpath('.//div[@class="pd-float"]//a/@href').get())
            if title and date and doc_url:
                self.articles_url_date_docUrl_mapping[doc_url]= [title , date]
                articles.append(doc_url)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.articles_url_date_docUrl_mapping.get(response.url)[0]

    def get_body(self, response) -> str:
        return ""       

    def get_date(self, response) -> str: 
        return self.articles_url_date_docUrl_mapping.get(response.url, None)[1]
        
    def date_format(self) -> str:
        return "%d %B %Y"

    def get_images(self, response) -> list[str]:
        return []

    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        return [response.url]

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        return None       