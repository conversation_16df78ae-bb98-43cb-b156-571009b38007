from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser

class MinistryofFinanceEconomicReports(OCSpider):
    name = "MinistryofFinanceEconomicReports"
    
    # Option 1: Start with a single URL and paginate through years
    start_urls_names = {
        "https://www.mof.gov.my/portal/arkib/ekonomi/ek2022.html": "News"
    }

    charset = "iso-8859-1"
    country = "Malaysia"

    def __init__(self):
        super().__init__()
        self.current_year = 2022  # Starting year
        self.end_year = 1998      # End year

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Malay"

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
     
    article_data_map = {}
     
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//div[@class="furniture-middle"]//p'):
           link = article.xpath(".//a/@href").get()
           title = article.xpath(".//a//text()").get()
           if link:
               articles.append(link)
               self.article_data_map[link] = {"title": title, "link": link}
        return list(set(articles))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response) -> str:
        return ""
        
    def get_images(self, response) -> list:
        return []
       
    def date_format(self) -> str:
        return "%Y"
    
    def get_date(self, response) -> str:
        # Extract year from the current URL or use current_year
        url = response.url
        year_match = url.split('ek')[-1].split('.html')[0]
        try:
            year = int(year_match)
        except ValueError:
            year = self.current_year
            
        parsed_date = dateparser.parse(str(year), languages=['ms','en'])
        return parsed_date.strftime("%Y")
        
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        if ".pdf" in response.url:
            return [response.url]
        return []
        
    def get_page_flag(self) -> bool:
        return False  # Enable pagination
    
    def get_next_page(self, response) :
        return None 
