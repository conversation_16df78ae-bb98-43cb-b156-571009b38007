from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ParliamentofPadangCity(OCSpider):
    name = "ParliamentofPadangCity"

    start_urls_names = {
        "https://dprd.padang.go.id/category/artikel": "Articles"
    }

    charset = "utf-8"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.country = "Indonesia"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='margin-min10']/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='content-head']//h1//text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='content-detail']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="image-konten-default"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d" 
    
    def get_date(self, response) -> str:
        raw_date = response.xpath("//div[@class='content-date']//p//text()").get()
        if not raw_date:
            return None
        raw_date = raw_date.strip().replace("WIB", "").strip()
        indonesian_months = {
            "Jan": "January",
            "Feb": "February",
            "Mar": "March",
            "Apr": "April",
            "Mei": "May",
            "Jun": "June",
            "Jul": "July",
            "Agu": "August",
            "Sep": "September",
            "Okt": "October",
            "Nov": "November",
            "Des": "December",
        }
        parts = raw_date.split()
        if len(parts) != 3:
            return
        day, month_id, year = parts
        month_en = indonesian_months.get(month_id)
        if not month_en:
            return
        try:
            date_obj = datetime.strptime(f"{day} {month_en} {year}", "%d %B %Y")
            return date_obj.strftime(self.date_format())
        except ValueError as e:
            return
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        # No next page to scrape
        return None