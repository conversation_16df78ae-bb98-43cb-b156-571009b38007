#The Financial Investment Review (VietnamFinance)
from scraper.OCSpider import <PERSON>CSpid<PERSON>
from scraper.utils.helper import body_normalization
import scrapy
from scrapy import Selector
from typing import Optional
from datetime import datetime
import json
import dateparser

class TheFinancialInvestmentReviewVietnamFinance(OCSpider):
    name = "TheFinancialInvestmentReviewVietnamFinance" 
    #pagination is not working i think response is not comming from api after 1 page please check onece and payload also changing i after 1 page i try my best to complete this 

    start_urls_names = {
        "https://vietnamfinance.vn/thoi-su/": "News"
    }

    start_urls_with_no_pagination_set = {}

    api_start_url = {
        "https://vietnamfinance.vn/thoi-su/": {
        "url": "https://vietnamfinance.vn/?mod=iframe&act=loadCate&page=1",
        "payload": {
            "last_id": "131116",
            "last_push": "2025-08-16 07:15:00",
             "cate_id":"36"
        }
        }
    }

    charset = "utf-8"
   
    country = "Vietnam"

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
        return "Asia/Ho_<PERSON>_Minh"
     
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        current_page = response.meta.get("current_page", 1)
        api_data["url"] = f"https://vietnamfinance.vn/?mod=iframe&act=loadCate&page={current_page}"    
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )

    articles_to_date = {}

    def get_articles(self, response) -> list:  
        return response.xpath('//div[@class="article__img flex-0 w-40"]//a//@href').getall()
        
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return  response.xpath('//h1[@class="detail-title mb-20"]//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//div[@class="content_detailnews"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//figure//img//@src').getall()

    def date_format(self) -> str:
        return "%d-%m-%Y"

    def get_date(self, response) -> str:
        date= response.xpath('//div[@class="detail-time-public fs-14 font-arial text-gray-66 mb-20"]//span//text()').get()
        date_obj = dateparser.parse(date, languages=['en'])
        return date_obj.strftime("%d-%m-%Y")

    def get_authors(self, response):
        return ""
            
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        return int(response.meta.get("current_page")) + 1

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_url.get(start_url)
        if not api_data:
           self.logger.error("API data not found for start_url")
           return
        api_url = api_data["url"]
        next_page = self.get_next_page(response, current_page)
        if next_page:
          payload = response.meta.get("payload", {}).copy()
          payload["last_id"] = "110465"
          payload["last_push"]= "2024-05-08 16:59:48"
          yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            formdata=payload,
            callback=self.parse_intermediate,
            meta={"current_page": next_page, "start_url": start_url, "payload": payload},
            dont_filter=True,
           )
        else:
         return None