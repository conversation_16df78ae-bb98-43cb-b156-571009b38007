from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
import re
from urllib.parse import unquote

class MinistryofDefenceDeputyMinistersSpeeches(OCSpider):
    name = "MinistryofDefenceDeputyMinistersSpeeches"

    start_urls_names = {
        'https://www.mod.gov.my/index.php/en/media3/teks-ucapan-timb-menteri': 'ministry'
    }

    country = "Malaysia"

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Kuala_Lumpur"
    
    @property
    def language(self): 
        return "Bahasa Malaysia"

    # def parse_intermediate(self, response):
    #     hbp = HeadlessBrowserProxy()
    #     request = scrapy.Request(hbp.get_proxy(response.url, timeout=30000), callback=self.parse
    #     )
    #     request.meta['start_url'] = response.request.meta['start_url']
    #     yield request

    def parse_intermediate(self, response):
        if response.status == 404:
            self.logger.warning(f"Skipping 404 page: {response.url}")
            return  # Don't yield anything

        hbp = HeadlessBrowserProxy()
        proxy_url = hbp.get_proxy(response.url, timeout=30000)
        if not proxy_url:
            self.logger.warning(f"Proxy failed for: {response.url}")
            return  # Avoid yielding if proxy failed

        request = scrapy.Request(
            proxy_url,
            callback=self.parse
        )
        request.meta['start_url'] = response.request.meta.get('start_url', response.url)
        yield request
    
    month_map = {
        'Januari': 'January',
        'Februari': 'February',
        'Mac': 'March',
        'April': 'April',
        'Mei': 'May',
        'Jun': 'June',
        'Julai': 'July',
        'Ogos': 'August',
        'September': 'September',
        'Oktober': 'October',
        'November': 'November',
        'Disember': 'December',
        'August':'August',
    }

    article_url_date_docUrl_mapping={}

    def get_articles(self, response) -> list:
        articles =[]
        elements = response.xpath('//a[@class="uk-position-cover uk-position-z-index"]')
        for article in elements:
            block = article.xpath('./@title').get()
            match = re.match(r'(\d{1,2}) (\w+) (\d{4}) - (.+)', block)
            doc_url = article.xpath('./@href').get()
            if doc_url:
                if doc_url.startswith('/'):
                    doc_url = response.urljoin(doc_url)
                    doc_url = doc_url.replace('https://proxy.scrapeops.io', 'https://www.mod.gov.my')
                if 'https://mod.gov.my/' in doc_url:
                    doc_url = doc_url.replace('https://mod.gov.my/', 'https://www.mod.gov.my/')
                doc_url = unquote(doc_url)
                if match:
                    day = match.group(1)
                    month = match.group(2)
                    year = match.group(3)
                    title = match.group(4)
                    english_month = self.month_map.get(month, '')
                    date = f"{day} {english_month} {year}"
                    if doc_url and date and title :
                            articles.append(doc_url)
                            self.article_url_date_docUrl_mapping[doc_url]=[title , date]
        return articles
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        url = unquote(response.url)
        return self.article_url_date_docUrl_mapping.get(url)[0]

    def get_body(self, response) -> str:
        return ''
       
    def get_date(self, response) -> str:
        url = unquote(response.url)
        return self.article_url_date_docUrl_mapping.get(url)[1]

    def date_format(self) -> str:
        return "%d %B %Y"

    def get_images(self, response) -> list[str]:
       return []
    
    def get_document_urls(self, response, entry=None):
        return [response.url]

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def next_page(self, response):
        return None