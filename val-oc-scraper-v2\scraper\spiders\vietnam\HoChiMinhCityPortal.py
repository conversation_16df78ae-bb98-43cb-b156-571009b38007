#Economics & Urban Newspaper

from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import dateparser
from datetime import datetime
import re

class HoChiMinhCityPortal(OCSpider):
    name = "HoChiMinhCityPortal"

    start_urls_names = {
        #"https://hochiminhcity.gov.vn/tin-t%E1%BB%A9c-s%E1%BB%B1-ki%E1%BB%87n-all-?_com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV_cur=1&p_p_id=com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV_delta=10&p_r_p_resetCur=false": "public",
        "https://hochiminhcity.gov.vn/tin-tuc-so-nganh?_com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV_cur=2&p_p_id=com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV_delta=10&p_r_p_resetCur=false":"News",
        "https://hochiminhcity.gov.vn/vi/phuong-xa-dac-khu?_com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV_cur=2&p_p_id=com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV&p_p_lifecycle=0&p_p_state=normal&p_p_mode=view&_com_liferay_asset_publisher_web_portlet_AssetPublisherPortlet_INSTANCE_iwwYBC4Hj8wV_delta=10&p_r_p_resetCur=false":"News"
        }

    charset = "iso-8859-1"

    country = "Vietnam"

    
    custom_settings = {
          "DOWNLOADER_MIDDLEWARES": {
              'scraper.middlewares.HeadlessBrowserProxy': 8000
          },
          "DOWNLOAD_DELAY": 5,
          "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
      }
    HEADLESS_BROWSER_WAIT_TIME = 9000

    @property
    def source_type(self) -> str:
        return "unknown"
    
    @property
    def language(self):
        return "Vietnamese"

    @property
    def timezone(self):
     return "Asia/Ho_Chi_Minh"
    
    article_data_map = {}
     
    def get_articles(self, response) -> list:  
       links= response.xpath('//div[@class="news-items row"]//a//@href').getall()
       return list(set(links))
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h3[@class="title-divvv"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="contentText"]//p//text()').getall())
   
    def get_images(self, response) -> list:
         return response.xpath('//figure//img//@src').getall()
       
    def date_format(self) -> str:
     return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date =  response.xpath('//span[@class="sp-dislaydate"]//text()').get()
        date_obj = dateparser.parse(date, languages=['vi'])
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
         return []
         
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
          return None
