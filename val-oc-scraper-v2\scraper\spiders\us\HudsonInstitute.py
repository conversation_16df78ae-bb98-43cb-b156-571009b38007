from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class HudsonInstitute(OCSpider):
    name = "HudsonInstitute"

    country = "US"

    start_urls_names = {
        "https://www.hudson.org/search?hud-content-type=260&expert=&date-from=&date-to=&keywords=&topics=All&region=All": "Commentary",
    }
        
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="c-horizontal-card__content"]/a[@class="c-horizontal-card__title"]/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="block block-layout-builder block-field-blocknodeshort-form-articletitle"]/span/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="field body"]/p/text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//div[@class="field field-date"]/time/text()').get().strip()
        return datetime.strptime(date_str, "%b %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return response.xpath('//div[@class="expert-author--names"]/a/text()').get()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="pager__item pager__item--next "]/a/@href').get()
        if not next_page:
           return None
        else:
            return next_page